{"actions": {"4e48f67a-fe7a-41b8-b961-1cd55dc59808": {"name": "主机命令行 - Register New Time Provider via Rundll32.exe", "desc": "In this action, an attacker is trying to register the malicious gametime.dll as a new Time Provider via rundll32.exe.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001138", "ver": 16, "uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "mitre_techniques", "tag": "T1547 - 启动或登录自动启动执行", "tag_en": "T1547 - Boot or Logon Autostart Execution", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "4e48f67a-fe7a-41b8-b961-1cd55dc59808", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "gametime.dll", "file_name": "gametime.dll", "file_owner": "system", "file_transfer_library": 998000592, "filesize": 136704, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "f21ed2c2d408c38a94ed52c6f3aedadf", "sha1sum": "6f057d747186dfae258b23a4df72ab9e34cd5a93", "sha256sum": "e5c7041705c2f8e4806be989da38ce9704cc83699ed2565a8c0074820a461cca"}], "monitor_connections": "off", "raw_text": "copy /y C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\gametime.dll C:\\Windows\\System32\r\nrundll32.exe C:\\Windows\\System32\\gametime.dll, Register\r\nsc.exe stop w32time & echo \"\"\r\nsc.exe start w32time\r\ntasklist /svc | findstr /i winver.exe\r\ntaskkill /f /im winver.exe\r\nrundll32.exe C:\\Windows\\System32\\gametime.dll, Deregister\r\nsc.exe stop w32time\r\ndel C:\\Windows\\System32\\gametime.dll\r\nsc.exe start w32time\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "copy /y C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\gametime.dll C:\\Windows\\System32", "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "rundll32.exe C:\\Windows\\System32\\gametime.dll, Register", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "sc.exe stop w32time & echo \"\"", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "sc.exe start w32time", "prompt": "auto", "sleep": 1, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i winver.exe", "prompt": "auto", "sleep": 1, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im winver.exe", "prompt": "auto", "sleep": 0, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rundll32.exe C:\\Windows\\System32\\gametime.dll, <PERSON><PERSON><PERSON>", "prompt": "auto", "sleep": 0, "step_order": 7, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "sc.exe stop w32time", "prompt": "auto", "sleep": 0, "step_order": 8, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del C:\\Windows\\System32\\gametime.dll", "prompt": "auto", "sleep": 0, "step_order": 9, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "sc.exe start w32time", "prompt": "auto", "sleep": 0, "step_order": 10, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 11, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "************************************": {"name": "主机命令行 - Create a Shortcut named TA866.lnk to Startup Folder using Powershell Script", "desc": "In this action, an attacker is creating a shortcut named TA866.lnk in the logged-on user’s Startup folder.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001114", "ver": 16, "uuid": "************************************", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "************************************", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "************************************", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "************************************", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "************************************", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "************************************", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "************************************", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "************************************", "tag_type": "mitre_techniques", "tag": "T1547.009 - 快捷方式修改", "tag_en": "T1547.009 - Shortcut Modification", "type": 1}, {"uuid": "************************************", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "************************************", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "************************************", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "************************************", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1018", "tag_en": "ATT&CK:M1018", "type": 1}, {"uuid": "************************************", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1022", "tag_en": "ATT&CK:M1022", "type": 1}, {"uuid": "************************************", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1038", "tag_en": "ATT&CK:M1038", "type": 1}, {"uuid": "************************************", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "************************************", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "TA866.js", "file_name": "TA866.js", "file_owner": "system", "file_transfer_library": 998000578, "filesize": 321, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "6e441a242cc93b751deb8ae1850e9525", "sha1sum": "a5500856dfccc261386c0eabb1d68dc9715f6050", "sha256sum": "13ee0bf920e3425f60efd5964f0070b7391370<PERSON>de9a33c5daf8c9c80a01ea9"}], "monitor_connections": "off", "raw_text": "whoami\r\npowershell.exe New-Item -ItemType SymbolicLink -Path '%USERPROFILE%\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup' -Name \"TA866.lnk\" -Value \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\TA866.js\";\r\ndel \"%USERPROFILE%\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\TA866.lnk\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "powershell.exe New-Item -ItemType SymbolicLink -Path '%USERPROFILE%\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup' -Name \"TA866.lnk\" -Value \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\TA866.js\";", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%USERPROFILE%\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\TA866.lnk\"", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "86e00dfb-57e5-43c6-9895-43aa94d15e89": {"name": "主机命令行 - Create a New Persistence Registry Key \"Recycle Bin\" in HKLM hive by using RecycleBinPersistence Tool", "desc": "In this action, an attacker is trying to create a new registry key \"Recycle Bin\" to persist on the target system.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001117", "ver": 16, "uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "mitre_techniques", "tag": "T1547.001 - 注册表启动项/启动文件夹", "tag_en": "T1547.001 - Registry Run Keys / Startup Folder", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "86e00dfb-57e5-43c6-9895-43aa94d15e89", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "RecycleBinPersistence.exe", "file_name": "RecycleBinPersistence.exe", "file_owner": "system", "file_transfer_library": 998000580, "filesize": 125952, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "679cf5393cd7ccafb2f7a4889d1e5215", "sha1sum": "15612ebfc77ccfaff0cbde1003c99eb19d78a0a6", "sha256sum": "2be42d2bfeefac8abfafdd50d998d2e2b4a66423bdc6ea4079ce0c6e7b49c371"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\RecycleBinPersistence.exe \r\nreg.exe DELETE  \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{645FF040-5081-101B-9F08-00AA002F954E}\\shell\\open\" /f\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\RecycleBinPersistence.exe ", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "reg.exe DELETE  \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{645FF040-5081-101B-9F08-00AA002F954E}\\shell\\open\" /f", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "6e7aa54a-b615-4a73-bec3-50c57867b4b2": {"name": "主机命令行 - Copy an Executable to Startup Folder for Persistence via Grpconv.exe", "desc": "In this action, an attacker tries to copy an executable to the startup folder for the persistence via Grpconv.exe binary.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001119", "ver": 16, "uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "mitre_techniques", "tag": "T1547.001 - 注册表启动项/启动文件夹", "tag_en": "T1547.001 - Registry Run Keys / Startup Folder", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "6e7aa54a-b615-4a73-bec3-50c57867b4b2", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "setup.ini", "file_name": "grpconvPersistence.txt", "file_owner": "system", "file_transfer_library": 998000581, "filesize": 116, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "45cec29356b652c044fc1d3b67834496", "sha1sum": "16af1ca005742aa9501f7507fb74eb1d1944d03f", "sha256sum": "a3b66b814d891c71b230af0ba807e877fc6abb1e0b660116a29e86ecf607b725"}], "monitor_connections": "off", "raw_text": "whoami\r\ncopy /y C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\setup.ini \"%USERPROFILE%\"\r\ngrpconv.exe -o\r\ndir \"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\GrpconvCalc.lnk\"\r\ndel \"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\GrpconvCalc.lnk\" & del \"%USERPROFILE%\\\\setup.ini\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "copy /y C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\setup.ini \"%USERPROFILE%\"", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "grpconv.exe -o", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "dir \"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\GrpconvCalc.lnk\"", "prompt": "auto", "sleep": 1, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\GrpconvCalc.lnk\" & del \"%USERPROFILE%\\\\setup.ini\"", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "ab899b3d-9da1-430d-b112-4f6d807e811e": {"name": "主机命令行 - Create a Registry Key by using SharPersist Tool", "desc": "In this action, an attacker is trying to create a registry key in Hkcurun Hive.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001137", "ver": 16, "uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "mitre_techniques", "tag": "T1547 - 启动或登录自动启动执行", "tag_en": "T1547 - Boot or Logon Autostart Execution", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "ab899b3d-9da1-430d-b112-4f6d807e811e", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "sharpersist.exe", "file_name": "sharpersist.exe", "file_owner": "system", "file_transfer_library": 998000591, "filesize": 230912, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "ad9b68820de30d71cc2ec17235bb4ad1", "sha1sum": "daedb9d53501dcb655044ce4cbb5d39a645070b4", "sha256sum": "7806b81514ecc44219a6f6193b15b23aea0a947f3c91b339332bea1445745596"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\sharpersist.exe -t reg -c \"C:\\Windows\\System32\\cmd.exe\" -a \"/c calc.exe\" -k \"hkcurun\" -v \"Test Stuff\" -m add\r\nC:\\Users\\<USER>\\Documents\\{{v_action_id}}\\sharpersist.exe -t reg -k \"hkcurun\" -v \"Test Stuff\" -m remove\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\sharpersist.exe -t reg -c \"C:\\Windows\\System32\\cmd.exe\" -a \"/c calc.exe\" -k \"hkcurun\" -v \"Test Stuff\" -m add", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "\\[\\+\\] SUCCESS", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\sharpersist.exe -t reg -k \"hkcurun\" -v \"Test Stuff\" -m remove", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e": {"name": "主机命令行 - Execute SharpHide Tool", "desc": "In this action, an attacker is trying to create a hidden registry key by using SharpHide Tool", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001135", "ver": 16, "uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "mitre_techniques", "tag": "T1547 - 启动或登录自动启动执行", "tag_en": "T1547 - Boot or Logon Autostart Execution", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "SharpHide.exe", "file_name": "SharpHide.exe", "file_owner": "system", "file_transfer_library": 998000589, "filesize": 9216, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "91fc44b5f3aae412bc8791f5e270ab31", "sha1sum": "e25896cd448dd05d7091eba6f5a75df952760027", "sha256sum": "0986251f701a76f49ff9bf2efa3d6008c910572d9441f0a01ca17814c290fdea"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpHide.exe action=create keyvalue=\"C:\\Windows\\Temp\\Bla.exe\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpHide.exe action=create keyvalue=\"C:\\Windows\\Temp\\Bla.exe\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "successfully", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a": {"name": "主机命令行 - Write a File that Contains Ransom Note for Netwalker Ransomware and Open It", "desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001123", "ver": 16, "uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 1, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "mitre_tactics", "tag": "TA0040 - 破坏影响", "tag_en": "TA0040 - Impact", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "mitre_techniques", "tag": "T1491 - 损毁", "tag_en": "T1491 - Defacement", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1053", "tag_en": "ATT&CK:M1053", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "0aba08a5-e7b4-40ee-a7c1-d8919a579f6a", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "echo \"Netwalker Malware is there\" > \"%TMP%\\Netwalker_Ransomware_619815.txt\"\r\necho \"Your files are encrypted.\" >> \"%TMP%\\Netwalker_Ransomware_619815.txt\"\r\nnotepad.exe \"%TMP%\\Netwalker_Ransomware_619815.txt\"\r\ntaskkill /f /im \"notepad.exe\"\r\ndel \"%TMP%\\Netwalker_Ransomware_619815.txt\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "echo \"Netwalker Malware is there\" > \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "echo \"Your files are encrypted.\" >> \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "notepad.exe \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im \"notepad.exe\"", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "46b572a9-bd46-4a9b-b470-af9829803f0a": {"name": "主机命令行 - Write a File that Contains Ransom Note for Sodinokibi Ransomware and Open It", "desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001133", "ver": 16, "uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 1, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "mitre_tactics", "tag": "TA0040 - 破坏影响", "tag_en": "TA0040 - Impact", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "mitre_techniques", "tag": "T1491 - 损毁", "tag_en": "T1491 - Defacement", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1053", "tag_en": "ATT&CK:M1053", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "46b572a9-bd46-4a9b-b470-af9829803f0a", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "echo \"Sodinokibi(Revil) Ransomware is there\" > \"%TMP%\\Sodinokibi.txt\"\r\necho \"Your files are encrypted.\" >> \"%TMP%\\Sodinokibi.txt\"\r\nnotepad.exe \"%TMP%\\Sodinokibi.txt\"\r\ntaskkill /f /im \"notepad.exe\"\r\ndel \"%TMP%\\Sodinokibi.txt\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "echo \"Sodinokibi(Revil) Ransomware is there\" > \"%TMP%\\Sodinokibi.txt\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "echo \"Your files are encrypted.\" >> \"%TMP%\\Sodinokibi.txt\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "notepad.exe \"%TMP%\\Sodinokibi.txt\"", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im \"notepad.exe\"", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%TMP%\\Sodinokibi.txt\"", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "e43c4665-e562-4e92-b384-b67a022d1217": {"name": "主机命令行 - Write a File that Contains Ransom Note for Darkside Ransomware and Open It", "desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001131", "ver": 16, "uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 1, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "mitre_tactics", "tag": "TA0040 - 破坏影响", "tag_en": "TA0040 - Impact", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "mitre_techniques", "tag": "T1491 - 损毁", "tag_en": "T1491 - Defacement", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1053", "tag_en": "ATT&CK:M1053", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "e43c4665-e562-4e92-b384-b67a022d1217", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "echo \"Darkside Ransomware is there\" > \"%TMP%\\Darkside.txt\"\r\necho \"Your files are encrypted.\" >> \"%TMP%\\Darkside.txt\"\r\nnotepad.exe \"%TMP%\\Darkside.txt\"\r\ndel \"%TMP%\\Darkside.txt\"\r\ntaskkill /f /im \"notepad.exe\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "echo \"Darkside Ransomware is there\" > \"%TMP%\\Darkside.txt\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "echo \"Your files are encrypted.\" >> \"%TMP%\\Darkside.txt\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "notepad.exe \"%TMP%\\Darkside.txt\"", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%TMP%\\Darkside.txt\"", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im \"notepad.exe\"", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "9a2af110-0b63-42f0-ab25-bc1910e4785d": {"name": "主机命令行 - Capture User Input using Powershell Script", "desc": "In this action, The PowerShell script captures user input using SetWindowsHookExA API.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001157", "ver": 16, "uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 4, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "9a2af110-0b63-42f0-ab25-bc1910e4785d", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "keylog.ps1", "file_name": "keylog.ps1", "file_owner": "system", "file_transfer_library": 998000597, "filesize": 1694, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "dab9f80b71af1eccec39395ad7e0e7d1", "sha1sum": "d3d2b3ae91c279b3c69464b0f887e3deadb965a0", "sha256sum": "b15927f206b9fc43c8d03cfc33fb53d7f9d0adcc5e32e52aaab2a8e701beb01f"}], "monitor_connections": "off", "raw_text": "cd C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\nstart /b powershell.exe -c \"Unblock-File C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\keylog.ps1; C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\keylog.ps1\"\r\ntaskkill /f /im powershell.exe\r\ndel log.txt\r\ncd .. & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "cd C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "start /b powershell.exe -c \"Unblock-File C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\keylog.ps1; C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\keylog.ps1\"", "prompt": "auto", "sleep": 20, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im powershell.exe", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del log.txt", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "cd .. & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf": {"name": "主机命令行 - List the Global Groups on the Primary Domain Controller", "desc": "In this action, the net command is using to list the global groups on the Primary Domain Controller of the current domain.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001144", "ver": 16, "uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "mitre_techniques", "tag": "T1069 - 权限组发现", "tag_en": "T1069 - Permission Groups Discovery", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf", "tag_type": "run_as", "tag": "Domain", "tag_en": "Domain", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "net.exe group /domain & echo \"\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "net.exe group /domain & echo \"\"", "incompatible_check": "match", "incompatible_match": "domain either does not exist", "incompatible_order": 1, "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "ca4d2457-f8bf-4ff2-a091-90aa32ecb502": {"name": "主机命令行 - Create a scheduled task \"InetlSecurityAssistManager\" using schtasks command", "desc": "In this action, a scheduled task called \"InetlSecurityAssistManager\" is created and the scheduled task contains Wscript commands \"wscript %TMP%\\test.vbs\".", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001160", "ver": 16, "uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 4, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "mitre_techniques", "tag": "T1053.005 - 计划任务", "tag_en": "T1053.005 - Scheduled Task", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1028", "tag_en": "ATT&CK:M1028", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1018", "tag_en": "ATT&CK:M1018", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "ca4d2457-f8bf-4ff2-a091-90aa32ecb502", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "for /f \"tokens=*\" %i in ('whoami') do if \"%i\"==\"nt authority\\system\" (schtasks /create /sc MINUTE /tn \"InetlSecurityAssistManager\" /tr \"wscript %TMP%\\test.vbs\" /mo 10 /F /ru \"SYSTEM\") else (schtasks /create /sc MINUTE /tn \"InetlSecurityAssistManager\" /tr \"wscript %TMP%\\test.vbs\" /mo 10 /F)\r\nschtasks /delete /tn \"InetlSecurityAssistManager\" /f\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "for /f \"tokens=*\" %i in ('whoami') do if \"%i\"==\"nt authority\\system\" (schtasks /create /sc MINUTE /tn \"InetlSecurityAssistManager\" /tr \"wscript %TMP%\\test.vbs\" /mo 10 /F /ru \"SYSTEM\") else (schtasks /create /sc MINUTE /tn \"InetlSecurityAssistManager\" /tr \"wscript %TMP%\\test.vbs\" /mo 10 /F)", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "schtasks /delete /tn \"InetlSecurityAssistManager\" /f", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "5cc2ffc1-dceb-4541-8a72-796c2ce2129f": {"name": "主机命令行 - Capture Screenshot via .NET Binary", "desc": "In this action, an attacker is trying to capture screenshots from the target system by using a .NET binary.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001158", "ver": 16, "uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 4, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "mitre_techniques", "tag": "T1113 - 屏幕捕获", "tag_en": "T1113 - Screen Capture", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "5cc2ffc1-dceb-4541-8a72-796c2ce2129f", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "ScreenshotCapture.exe", "file_name": "ScreenshotCapture.exe", "file_owner": "system", "file_transfer_library": 998000598, "filesize": 5120, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "33a4860aff253381926cf5acf7335160", "sha1sum": "d028583a638b3f3d52df81a4024b5664103ea51c", "sha256sum": "8b68f41fc247a7c20a3b1dc43b54fc2fc734aa0f3ef6a00a4f4cce9652415f25"}], "monitor_connections": "off", "raw_text": "whoami\r\ncd C:\\Users\\<USER>\\Documents\\{{v_action_id}} & ScreenshotCapture.exe\r\ndir screenshot.jpg\r\ncd .. & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "cd C:\\Users\\<USER>\\Documents\\{{v_action_id}} & ScreenshotCapture.exe", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "dir screenshot.jpg", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "cd .. & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "e2d3dbad-d8f9-4089-bb66-224785a926f0": {"name": "主机命令行 - Copy \".docx\" Files Using PowerShell", "desc": "In this action, an attacker can copies \".docx\" files from one location to another by using PowerShell.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001156", "ver": 16, "uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "mitre_techniques", "tag": "T1119 - 自动收集", "tag_en": "T1119 - Automated Collection", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1041", "tag_en": "ATT&CK:M1041", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1029", "tag_en": "ATT&CK:M1029", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "e2d3dbad-d8f9-4089-bb66-224785a926f0", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "mkdir C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\npowershell.exe -Command \"Get-ChildItem -Recurse -Include *.doc | copy-item -destination 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}'\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "mkdir C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -Command \"Get-ChildItem -Recurse -Include *.doc | copy-item -destination 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}'\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "981e7229-f6a0-4ea3-855b-444d6791fa6e": {"name": "主机命令行 - Query the \"Terminal Server Client\" Registry Key", "desc": "In this action, The reg command retrieves the value of a registry key HKEY_CURRENT_USER\\Software\\Microsoft\\Terminal Server Client\\Default", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001150", "ver": 16, "uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "mitre_techniques", "tag": "T1012 - 查询注册表", "tag_en": "T1012 - Query Registry", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "981e7229-f6a0-4ea3-855b-444d6791fa6e", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "reg query \"HKEY_CURRENT_USER\\Software\\Microsoft\\Terminal Server Client\\Default\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "reg query \"HKEY_CURRENT_USER\\Software\\Microsoft\\Terminal Server Client\\Default\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "483e952d-4b29-4d41-b735-658e3d263801": {"name": "主机命令行 - Discover System Hardware via WMI", "desc": "In this action, the WMI component gets some information such as PointingDevice, BaseBoard, and DiskDrive.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001149", "ver": 16, "uuid": "483e952d-4b29-4d41-b735-658e3d263801", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "mitre_tactics", "tag": "TA0002 - 攻击执行", "tag_en": "TA0002 - Execution", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "mitre_techniques", "tag": "T1047 - Windows管理规范", "tag_en": "T1047 - Windows Management Instrumentation", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1038", "tag_en": "ATT&CK:M1038", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1040", "tag_en": "ATT&CK:M1040", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1018", "tag_en": "ATT&CK:M1018", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "483e952d-4b29-4d41-b735-658e3d263801", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_DiskDrive\\\"\"\r\npowershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_PointingDevice\\\"\"\r\npowershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_BaseBoard\\\"\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_DiskDrive\\\"\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_PointingDevice\\\"\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_BaseBoard\\\"\"", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "b4d6f8c7-b425-4208-a682-36f4c236e569": {"name": "主机命令行 - Enumerate Services via SC Command", "desc": "In this action, the Sc.exe command-line tool queries any Windows Service.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001148", "ver": 16, "uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "mitre_techniques", "tag": "T1007 - 系统服务发现", "tag_en": "T1007 - System Service Discovery", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "b4d6f8c7-b425-4208-a682-36f4c236e569", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "sc query\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "sc query", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "554755f4-2c9c-4c19-800f-f0b0e41020b4": {"name": "主机命令行 - Gather System Information by using Command-line Tools", "desc": "In this action, an attacker utilizes several common Windows command-line tools to enumerate resources.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001146", "ver": 16, "uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "mitre_techniques", "tag": "T1016 - 系统网络配置发现", "tag_en": "T1016 - System Network Configuration Discovery", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "554755f4-2c9c-4c19-800f-f0b0e41020b4", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "ipconfig.exe /all\r\narp -a\r\nroute PRINT\r\nsysteminfo.exe\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "ipconfig.exe /all", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "arp -a", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "route PRINT", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "systeminfo.exe", "prompt": "auto", "sleep": 1, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "5f476cf8-c21b-4e5c-92f4-331977df3ef9": {"name": "主机命令行 - Display Information of \"Administrator\" User via net.exe", "desc": "\"net user administrator\" command displays \"administrator\" user's information.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001140", "ver": 16, "uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "mitre_techniques", "tag": "T1087.001 - 本地帐户", "tag_en": "T1087.001 - Local Account", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1028", "tag_en": "ATT&CK:M1028", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "5f476cf8-c21b-4e5c-92f4-331977df3ef9", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "net.exe user administrator\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "net.exe user administrator", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}}}