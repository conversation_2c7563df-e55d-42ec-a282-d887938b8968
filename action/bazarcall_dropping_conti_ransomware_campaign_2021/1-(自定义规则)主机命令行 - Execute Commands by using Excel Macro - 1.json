{"action": {"name": "(自定义规则)主机命令行 - Execute Commands by using Excel Macro - 1", "desc": "In this action, an attacker is trying to execute commands on the target system by using a Excel Macro Workbook. This macro executes a dll via regsvr32[bazarcall_dropping_conti_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "microsoft.security", "file_owner": "system", "file_transfer_library": 998002212, "randomize_file": null, "file_name": "microsoft.security"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "sodinokibi.xlsm", "file_owner": "system", "file_transfer_library": 998002213, "randomize_file": null, "file_name": "sodinokibi.xlsm"}], "steps": [{"step_order": 1, "command": "cmd.exe /c dir \"%APPDATA%\\Microsoft\\Templates\\\" || mkdir \"%APPDATA%\\Microsoft\\Templates\\\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c \"%APPDATA%\\Microsoft\\Templates\\sodinokibi.xlsm\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "cmd.exe /c timeout 15", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "cmd.exe /c timeout 5 && tasklist /svc | findstr /i calc", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 5, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Initial Access", "ATT&CK:T1566.001"], "mitre_mitigation": ["ATT&CK:M1031", "ATT&CK:M1021", "ATT&CK:M1049", "ATT&CK:M1054", "ATT&CK:M1017"]}}, "picus_id": 10499, "uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "sigma_device_query": "title: Process Execution via WMIC\nstatus: stable\ndescription: Detects the attempt to execute a process via WMIC utility that provides a command-line interface for WMI. This technique is commonly utilized for remote execution of files and gathering information for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1047/\n   - https://attack.mitre.org/tactics/TA0002/\n   - https://learn.microsoft.com/en-us/windows/win32/wmisdk/wmic\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\wmic.exe'\n        ProcessCommandLine: '* process call create*'\n    condition: selection\nfalsepositives:\n    - Administrative tools/scripts\n    - Software installations/updates\nlevel: medium\ntags:\n    - attack.execution\n    - attack.t1047\n    - attack.ta0002"}