{"actions": {"9cfe841e-d65d-49b6-9c39-5ddcb17d2578": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "ver": 4, "vid": "8001450", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Zoom 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:21:19", "updated_at": "2025-08-08 17:21:19", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "9cfe841e-d65d-49b6-9c39-5ddcb17d2578", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54699\",\"TCP/54720\",\"TCP/54727\",\"TCP/54732\",\"TCP/54749\",\"TCP/54674\",\"TCP/54717\",\"TCP/54728\",\"TCP/54730\",\"TCP/54737\",\"TCP/54665\",\"TCP/54701\",\"TCP/54716\",\"TCP/54725\",\"TCP/54742\",\"TCP/54745\",\"TCP/54750\",\"TCP/54677\",\"TCP/54668\",\"TCP/54685\",\"TCP/54734\",\"TCP/54723\",\"TCP/54726\",\"TCP/54729\",\"TCP/54733\",\"TCP/54743\",\"TCP/54666\",\"TCP/54688\",\"TCP/54694\",\"TCP/54708\",\"TCP/54731\",\"TCP/54739\",\"TCP/54669\",\"TCP/54697\",\"TCP/54735\",\"TCP/54667\",\"TCP/54698\",\"TCP/54703\",\"TCP/54707\",\"TCP/54741\",\"TCP/54746\",\"TCP/54675\",\"TCP/54679\",\"TCP/54738\",\"TCP/54682\",\"TCP/54691\",\"TCP/54693\",\"TCP/54722\",\"TCP/54704\",\"TCP/54712\",\"TCP/54713\",\"TCP/54664\",\"TCP/54681\",\"TCP/54744\",\"TCP/54678\",\"TCP/54702\",\"TCP/54736\",\"TCP/54687\",\"TCP/54709\",\"TCP/54696\",\"TCP/54705\",\"TCP/54672\",\"TCP/54680\",\"TCP/54710\",\"TCP/54683\",\"TCP/54686\",\"TCP/54748\",\"TCP/54676\",\"TCP/54715\",\"TCP/54690\",\"TCP/54711\",\"TCP/54721\",\"TCP/54724\",\"TCP/54689\",\"TCP/54714\",\"TCP/54740\",\"TCP/54719\",\"TCP/54671\",\"TCP/54695\",\"TCP/54700\",\"TCP/54692\",\"TCP/54747\",\"TCP/54718\",\"TCP/54706\",\"TCP/54684\",\"TCP/54670\"]", "pcap_library": {"file_location": "pcap_library_998000859.pcap", "filesize": "372710 bytes", "md5sum": "894c81063dddfe006678c3194bd5ea52", "orig_file_name": "pcap_library_998000859.pcap", "packet_count": 1623}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8135\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8135", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "2238f0a8-bbc0-424b-b4a8-f04256d3621d": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "ver": 4, "vid": "8001449", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Zloader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:20:40", "updated_at": "2025-08-08 17:20:40", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "2238f0a8-bbc0-424b-b4a8-f04256d3621d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54524\",\"TCP/54528\",\"TCP/54546\",\"TCP/54557\",\"TCP/54504\",\"TCP/54510\",\"TCP/54564\",\"TCP/54567\",\"TCP/54532\",\"TCP/54484\",\"TCP/54491\",\"TCP/54495\",\"TCP/54497\",\"TCP/54570\",\"TCP/54487\",\"TCP/54525\",\"TCP/54535\",\"TCP/54542\",\"TCP/54554\",\"TCP/54561\",\"TCP/54565\",\"TCP/54483\",\"TCP/54511\",\"TCP/54517\",\"TCP/54534\",\"TCP/54551\",\"TCP/54563\",\"TCP/54486\",\"TCP/54489\",\"TCP/54492\",\"TCP/54501\",\"TCP/54519\",\"TCP/54522\",\"TCP/54523\",\"TCP/54540\",\"TCP/54550\",\"TCP/54571\",\"TCP/54509\",\"TCP/54514\",\"TCP/54482\",\"TCP/54494\",\"TCP/54520\",\"TCP/54543\",\"TCP/54556\",\"TCP/54566\",\"TCP/54530\",\"TCP/54560\",\"TCP/54568\",\"TCP/54488\",\"TCP/54505\",\"TCP/54496\",\"TCP/54507\",\"TCP/54518\",\"TCP/54529\",\"TCP/54537\",\"TCP/54538\",\"TCP/54549\",\"TCP/54499\",\"TCP/54515\",\"TCP/54569\",\"TCP/54500\",\"TCP/54506\",\"TCP/54539\",\"TCP/54541\",\"TCP/54544\",\"TCP/54558\",\"TCP/54536\",\"TCP/54547\",\"TCP/54572\",\"TCP/54485\",\"TCP/54516\",\"TCP/54552\",\"TCP/54573\",\"TCP/54498\",\"TCP/54533\",\"TCP/54574\",\"TCP/54493\",\"TCP/54503\",\"TCP/54526\",\"TCP/54553\",\"TCP/54555\",\"TCP/54562\",\"TCP/54512\",\"TCP/54508\",\"TCP/54490\",\"TCP/54502\",\"TCP/54513\",\"TCP/54521\",\"TCP/54548\",\"TCP/54545\",\"TCP/54559\",\"TCP/54527\",\"TCP/54531\"]", "pcap_library": {"file_location": "pcap_library_998000858.pcap", "filesize": "387276 bytes", "md5sum": "62fa5c2893e0b63e9d71ecd5dec8e04e", "orig_file_name": "pcap_library_998000858.pcap", "packet_count": 1771}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8120\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8120", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "c73e9212-a38c-4771-a705-9a7806f70fc4": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "ver": 4, "vid": "8001448", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 YouTube 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:19:56", "updated_at": "2025-08-08 17:19:56", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "c73e9212-a38c-4771-a705-9a7806f70fc4", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54365\",\"TCP/54369\",\"TCP/54401\",\"TCP/54408\",\"TCP/54366\",\"TCP/54388\",\"TCP/54402\",\"TCP/54383\",\"TCP/54403\",\"TCP/54395\",\"TCP/54404\",\"TCP/54406\",\"TCP/54351\",\"TCP/54358\",\"TCP/54374\",\"TCP/54379\",\"TCP/54394\",\"TCP/54393\",\"TCP/54396\",\"TCP/54359\",\"TCP/54361\",\"TCP/54372\",\"TCP/54384\",\"TCP/54392\",\"TCP/54345\",\"TCP/54370\",\"TCP/54386\",\"TCP/54400\",\"TCP/54348\",\"TCP/54360\",\"TCP/54382\",\"TCP/54352\",\"TCP/54363\",\"TCP/54380\",\"TCP/54373\",\"TCP/54378\",\"TCP/54390\",\"TCP/54405\",\"TCP/54350\",\"TCP/54391\",\"TCP/54376\",\"TCP/54385\",\"TCP/54368\",\"TCP/54375\",\"TCP/54381\",\"TCP/54389\",\"TCP/54407\",\"TCP/54367\",\"TCP/54399\",\"TCP/54347\",\"TCP/54354\",\"TCP/54371\",\"TCP/54377\",\"TCP/54398\",\"TCP/54355\",\"TCP/54364\",\"TCP/54344\",\"TCP/54356\",\"TCP/54387\",\"TCP/54349\",\"TCP/54397\",\"TCP/54357\",\"TCP/54353\",\"TCP/54362\",\"TCP/54346\"]", "pcap_library": {"file_location": "pcap_library_998000857.pcap", "filesize": "406453 bytes", "md5sum": "3431787c21fc7aa5e2bee6a7920ed08f", "orig_file_name": "pcap_library_998000857.pcap", "packet_count": 1173}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8134\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8134", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "45a88a8a-933e-4d90-bbbd-5f18604179a1": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "ver": 4, "vid": "8001447", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Xbash 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:19:17", "updated_at": "2025-08-08 17:19:17", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "45a88a8a-933e-4d90-bbbd-5f18604179a1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54224\",\"TCP/54227\",\"TCP/54195\",\"TCP/54233\",\"TCP/54185\",\"TCP/54198\",\"TCP/54219\",\"TCP/54221\",\"TCP/54184\",\"TCP/54190\",\"TCP/54202\",\"TCP/54211\",\"TCP/54222\",\"TCP/54197\",\"TCP/54206\",\"TCP/54181\",\"TCP/54186\",\"TCP/54209\",\"TCP/54239\",\"TCP/54194\",\"TCP/54210\",\"TCP/54213\",\"TCP/54189\",\"TCP/54217\",\"TCP/54191\",\"TCP/54192\",\"TCP/54214\",\"TCP/54240\",\"TCP/54183\",\"TCP/54203\",\"TCP/54218\",\"TCP/54225\",\"TCP/54237\",\"TCP/54245\",\"TCP/54246\",\"TCP/54200\",\"TCP/54215\",\"TCP/54231\",\"TCP/54205\",\"TCP/54187\",\"TCP/54188\",\"TCP/54193\",\"TCP/54212\",\"TCP/54226\",\"TCP/54236\",\"TCP/54229\",\"TCP/54249\",\"TCP/54201\",\"TCP/54204\",\"TCP/54220\",\"TCP/54228\",\"TCP/54238\",\"TCP/54230\",\"TCP/54248\",\"TCP/54207\",\"TCP/54235\",\"TCP/54244\",\"TCP/54241\",\"TCP/54182\",\"TCP/54199\",\"TCP/54232\",\"TCP/54234\",\"TCP/54196\",\"TCP/54242\",\"TCP/54247\",\"TCP/54208\",\"TCP/54216\",\"TCP/54243\",\"TCP/54223\"]", "pcap_library": {"file_location": "pcap_library_998000856.pcap", "filesize": "311054 bytes", "md5sum": "5bf61c92a89d68f6aa16f066a971010e", "orig_file_name": "pcap_library_998000856.pcap", "packet_count": 1195}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8119\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8119", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b065e5be-4d18-4970-9e6d-cddfa1b8e893": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "ver": 4, "vid": "8001446", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Ursnif 银行木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:18:31", "updated_at": "2025-08-08 17:18:31", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b065e5be-4d18-4970-9e6d-cddfa1b8e893", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54090\",\"TCP/54092\",\"TCP/54098\",\"TCP/54120\",\"TCP/54123\",\"TCP/54144\",\"TCP/54145\",\"TCP/54088\",\"TCP/54100\",\"TCP/54126\",\"TCP/54150\",\"TCP/54115\",\"TCP/54103\",\"TCP/54109\",\"TCP/54142\",\"TCP/54157\",\"TCP/54114\",\"TCP/54130\",\"TCP/54134\",\"TCP/54138\",\"TCP/54085\",\"TCP/54161\",\"TCP/54113\",\"TCP/54127\",\"TCP/54140\",\"TCP/54146\",\"TCP/54147\",\"TCP/54158\",\"TCP/54132\",\"TCP/54101\",\"TCP/54104\",\"TCP/54143\",\"TCP/54108\",\"TCP/54112\",\"TCP/54131\",\"TCP/54136\",\"TCP/54093\",\"TCP/54137\",\"TCP/54152\",\"TCP/54086\",\"TCP/54128\",\"TCP/54129\",\"TCP/54084\",\"TCP/54097\",\"TCP/54111\",\"TCP/54091\",\"TCP/54121\",\"TCP/54122\",\"TCP/54148\",\"TCP/54149\",\"TCP/54155\",\"TCP/54153\",\"TCP/54160\",\"TCP/54096\",\"TCP/54154\",\"TCP/54159\",\"TCP/54117\",\"TCP/54135\",\"TCP/54139\",\"TCP/54099\",\"TCP/54107\",\"TCP/54095\",\"TCP/54156\",\"TCP/54087\",\"TCP/54105\",\"TCP/54110\",\"TCP/54119\",\"TCP/54106\",\"TCP/54124\",\"TCP/54141\",\"TCP/54116\",\"TCP/54089\",\"TCP/54094\",\"TCP/54118\",\"TCP/54102\",\"TCP/54125\",\"TCP/54133\"]", "pcap_library": {"file_location": "pcap_library_998000855.pcap", "filesize": "346188 bytes", "md5sum": "339bfc24a8b27beec2c592d2d5e3ad30", "orig_file_name": "pcap_library_998000855.pcap", "packet_count": 1329}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8118\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8118", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "59082366-6fc9-4629-b36f-04daf700e71b": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "ver": 4, "vid": "8001445", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Ursnif IcedID 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:17:47", "updated_at": "2025-08-08 17:17:47", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "59082366-6fc9-4629-b36f-04daf700e71b", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54065\",\"TCP/54066\",\"TCP/54067\",\"TCP/54070\",\"TCP/54071\",\"TCP/54039\",\"TCP/54045\",\"TCP/54047\",\"TCP/54050\",\"TCP/54026\",\"TCP/54014\",\"TCP/54021\",\"TCP/54022\",\"TCP/54025\",\"TCP/54002\",\"TCP/54027\",\"TCP/54032\",\"TCP/54036\",\"TCP/54041\",\"TCP/54052\",\"TCP/54029\",\"TCP/54069\",\"TCP/54017\",\"TCP/54030\",\"TCP/54055\",\"TCP/54068\",\"TCP/54000\",\"TCP/54019\",\"TCP/54051\",\"TCP/54058\",\"TCP/54064\",\"TCP/54004\",\"TCP/54006\",\"TCP/54013\",\"TCP/54015\",\"TCP/54046\",\"TCP/54008\",\"TCP/54061\",\"TCP/54063\",\"TCP/54012\",\"TCP/54001\",\"TCP/54005\",\"TCP/54020\",\"TCP/54040\",\"TCP/54042\",\"TCP/54031\",\"TCP/54033\",\"TCP/54062\",\"TCP/54072\",\"TCP/54018\",\"TCP/54011\",\"TCP/54028\",\"TCP/54035\",\"TCP/54053\",\"TCP/54003\",\"TCP/54016\",\"TCP/54034\",\"TCP/54048\",\"TCP/54060\",\"TCP/54049\",\"TCP/54009\",\"TCP/54024\",\"TCP/54056\",\"TCP/54059\",\"TCP/54023\",\"TCP/54037\",\"TCP/54038\",\"TCP/54044\",\"TCP/54007\",\"TCP/54010\",\"TCP/54057\",\"TCP/54054\"]", "pcap_library": {"file_location": "pcap_library_998000854.pcap", "filesize": "325224 bytes", "md5sum": "3bbc9bff9d35028ef58f414c9b8b4c35", "orig_file_name": "pcap_library_998000854.pcap", "packet_count": 1247}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8117\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8117", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "05bbe6b4-8fdc-442b-8827-500253f4b761": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "ver": 4, "vid": "8001444", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 TrickBot 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:16:59", "updated_at": "2025-08-08 17:16:59", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "05bbe6b4-8fdc-442b-8827-500253f4b761", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53924\",\"TCP/53927\",\"TCP/53951\",\"TCP/53989\",\"TCP/53920\",\"TCP/53932\",\"TCP/53939\",\"TCP/53960\",\"TCP/53965\",\"TCP/53979\",\"TCP/53926\",\"TCP/53938\",\"TCP/53992\",\"TCP/53915\",\"TCP/53917\",\"TCP/53925\",\"TCP/53933\",\"TCP/53937\",\"TCP/53942\",\"TCP/53946\",\"TCP/53952\",\"TCP/53948\",\"TCP/53964\",\"TCP/53980\",\"TCP/53918\",\"TCP/53921\",\"TCP/53936\",\"TCP/53945\",\"TCP/53954\",\"TCP/53967\",\"TCP/53978\",\"TCP/53928\",\"TCP/53963\",\"TCP/53986\",\"TCP/53953\",\"TCP/53982\",\"TCP/53919\",\"TCP/53972\",\"TCP/53981\",\"TCP/53935\",\"TCP/53943\",\"TCP/53949\",\"TCP/53969\",\"TCP/53983\",\"TCP/53959\",\"TCP/53974\",\"TCP/53984\",\"TCP/53991\",\"TCP/53958\",\"TCP/53962\",\"TCP/53966\",\"TCP/53968\",\"TCP/53973\",\"TCP/53916\",\"TCP/53940\",\"TCP/53947\",\"TCP/53923\",\"TCP/53944\",\"TCP/53950\",\"TCP/53961\",\"TCP/53970\",\"TCP/53977\",\"TCP/53988\",\"TCP/53922\",\"TCP/53931\",\"TCP/53955\",\"TCP/53985\",\"TCP/53934\",\"TCP/53987\",\"TCP/53971\",\"TCP/53929\",\"TCP/53930\",\"TCP/53976\",\"TCP/53941\",\"TCP/53957\",\"TCP/53975\",\"TCP/53990\"]", "pcap_library": {"file_location": "pcap_library_998000853.pcap", "filesize": "336573 bytes", "md5sum": "c7b3bfb73b0fe13a0f1e3174de323286", "orig_file_name": "pcap_library_998000853.pcap", "packet_count": 1329}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8116\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8116", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "8391abd4-5aba-4fd3-8a6c-807b34b1504f": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "ver": 4, "vid": "8001443", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 TrickBot Ryuk 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:16:17", "updated_at": "2025-08-08 17:16:17", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "8391abd4-5aba-4fd3-8a6c-807b34b1504f", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53838\",\"TCP/53857\",\"TCP/53866\",\"TCP/53886\",\"TCP/53896\",\"TCP/53871\",\"TCP/53876\",\"TCP/53847\",\"TCP/53832\",\"TCP/53844\",\"TCP/53898\",\"TCP/53858\",\"TCP/53895\",\"TCP/53899\",\"TCP/53870\",\"TCP/53872\",\"TCP/53825\",\"TCP/53839\",\"TCP/53848\",\"TCP/53890\",\"TCP/53909\",\"TCP/53837\",\"TCP/53830\",\"TCP/53874\",\"TCP/53892\",\"TCP/53851\",\"TCP/53865\",\"TCP/53867\",\"TCP/53893\",\"TCP/53862\",\"TCP/53869\",\"TCP/53881\",\"TCP/53882\",\"TCP/53902\",\"TCP/53835\",\"TCP/53855\",\"TCP/53842\",\"TCP/53863\",\"TCP/53910\",\"TCP/53908\",\"TCP/53836\",\"TCP/53845\",\"TCP/53854\",\"TCP/53873\",\"TCP/53884\",\"TCP/53887\",\"TCP/53829\",\"TCP/53834\",\"TCP/53877\",\"TCP/53900\",\"TCP/53907\",\"TCP/53849\",\"TCP/53868\",\"TCP/53841\",\"TCP/53852\",\"TCP/53853\",\"TCP/53878\",\"TCP/53885\",\"TCP/53888\",\"TCP/53827\",\"TCP/53891\",\"TCP/53905\",\"TCP/53840\",\"TCP/53846\",\"TCP/53850\",\"TCP/53826\",\"TCP/53833\",\"TCP/53856\",\"TCP/53904\",\"TCP/53843\",\"TCP/53879\",\"TCP/53883\",\"TCP/53831\",\"TCP/53861\",\"TCP/53897\",\"TCP/53903\",\"TCP/53860\",\"TCP/53864\",\"TCP/53875\",\"TCP/53889\",\"TCP/53894\",\"TCP/53828\",\"TCP/53901\",\"TCP/53906\",\"TCP/53859\",\"TCP/53880\",\"TCP/53911\"]", "pcap_library": {"file_location": "pcap_library_998000852.pcap", "filesize": "392636 bytes", "md5sum": "68f51d107719e2221c7250b853ab2e03", "orig_file_name": "pcap_library_998000852.pcap", "packet_count": 1492}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8115\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8115", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "942e40c3-630a-4864-8091-6d1b5184e714": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "ver": 4, "vid": "8001442", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Trevor 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:15:28", "updated_at": "2025-08-08 17:15:28", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "942e40c3-630a-4864-8091-6d1b5184e714", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53785\",\"TCP/53763\",\"TCP/53765\",\"TCP/53773\",\"TCP/53779\",\"TCP/53764\",\"TCP/53777\",\"TCP/53806\",\"TCP/53815\",\"TCP/53772\",\"TCP/53776\",\"TCP/53803\",\"TCP/53811\",\"TCP/53768\",\"TCP/53761\",\"TCP/53794\",\"TCP/53795\",\"TCP/53813\",\"TCP/53783\",\"TCP/53802\",\"TCP/53770\",\"TCP/53759\",\"TCP/53771\",\"TCP/53797\",\"TCP/53762\",\"TCP/53775\",\"TCP/53782\",\"TCP/53769\",\"TCP/53787\",\"TCP/53796\",\"TCP/53791\",\"TCP/53792\",\"TCP/53758\",\"TCP/53788\",\"TCP/53809\",\"TCP/53778\",\"TCP/53766\",\"TCP/53767\",\"TCP/53760\",\"TCP/53784\",\"TCP/53786\",\"TCP/53789\",\"TCP/53790\",\"TCP/53804\",\"TCP/53808\",\"TCP/53812\",\"TCP/53807\",\"TCP/53814\",\"TCP/53798\",\"TCP/53799\",\"TCP/53780\",\"TCP/53757\",\"TCP/53774\",\"TCP/53805\",\"TCP/53793\",\"TCP/53800\",\"TCP/53810\",\"TCP/53801\"]", "pcap_library": {"file_location": "pcap_library_998000851.pcap", "filesize": "314626 bytes", "md5sum": "e542e888d451bd19a2f73f665fa5db4b", "orig_file_name": "pcap_library_998000851.pcap", "packet_count": 1103}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8140\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8140", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "becac28b-75ba-4f46-b688-2fc50a09fb81": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "ver": 4, "vid": "8001441", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 StackOverflow 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:14:53", "updated_at": "2025-08-08 17:14:53", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "becac28b-75ba-4f46-b688-2fc50a09fb81", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53511\",\"TCP/53531\",\"TCP/53534\",\"TCP/53553\",\"TCP/53501\",\"TCP/53514\",\"TCP/53515\",\"TCP/53525\",\"TCP/53536\",\"TCP/53543\",\"TCP/53554\",\"TCP/53510\",\"TCP/53540\",\"TCP/53542\",\"TCP/53522\",\"TCP/53550\",\"TCP/53496\",\"TCP/53503\",\"TCP/53539\",\"TCP/53528\",\"TCP/53532\",\"TCP/53520\",\"TCP/53497\",\"TCP/53506\",\"TCP/53518\",\"TCP/53548\",\"TCP/53504\",\"TCP/53521\",\"TCP/53546\",\"TCP/53500\",\"TCP/53526\",\"TCP/53529\",\"TCP/53516\",\"TCP/53523\",\"TCP/53527\",\"TCP/53538\",\"TCP/53508\",\"TCP/53524\",\"TCP/53509\",\"TCP/53502\",\"TCP/53498\",\"TCP/53535\",\"TCP/53547\",\"TCP/53549\",\"TCP/53551\",\"TCP/53533\",\"TCP/53505\",\"TCP/53537\",\"TCP/53544\",\"TCP/53545\",\"TCP/53519\",\"TCP/53541\",\"TCP/53507\",\"TCP/53552\",\"TCP/53499\",\"TCP/53530\",\"TCP/53513\",\"TCP/53512\",\"TCP/53517\"]", "pcap_library": {"file_location": "pcap_library_998000850.pcap", "filesize": "309821 bytes", "md5sum": "e6ab488f7211aa1b61b09fb38f974566", "orig_file_name": "pcap_library_998000850.pcap", "packet_count": 1113}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8133\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8133", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "ver": 4, "vid": "8001440", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Slack 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:14:00", "updated_at": "2025-08-08 17:14:00", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "e10f9fb3-79ff-4c5c-8ed1-2313e78ff0fc", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53395\",\"TCP/53396\",\"TCP/53402\",\"TCP/53364\",\"TCP/53372\",\"TCP/53397\",\"TCP/53404\",\"TCP/53419\",\"TCP/53425\",\"TCP/53426\",\"TCP/53352\",\"TCP/53389\",\"TCP/53401\",\"TCP/53406\",\"TCP/53411\",\"TCP/53422\",\"TCP/53430\",\"TCP/53349\",\"TCP/53351\",\"TCP/53363\",\"TCP/53366\",\"TCP/53370\",\"TCP/53429\",\"TCP/53403\",\"TCP/53412\",\"TCP/53371\",\"TCP/53348\",\"TCP/53415\",\"TCP/53417\",\"TCP/53374\",\"TCP/53384\",\"TCP/53394\",\"TCP/53410\",\"TCP/53416\",\"TCP/53353\",\"TCP/53392\",\"TCP/53427\",\"TCP/53359\",\"TCP/53365\",\"TCP/53387\",\"TCP/53408\",\"TCP/53346\",\"TCP/53355\",\"TCP/53393\",\"TCP/53421\",\"TCP/53354\",\"TCP/53358\",\"TCP/53386\",\"TCP/53424\",\"TCP/53388\",\"TCP/53362\",\"TCP/53381\",\"TCP/53350\",\"TCP/53357\",\"TCP/53432\",\"TCP/53369\",\"TCP/53356\",\"TCP/53367\",\"TCP/53379\",\"TCP/53398\",\"TCP/53399\",\"TCP/53400\",\"TCP/53376\",\"TCP/53380\",\"TCP/53382\",\"TCP/53391\",\"TCP/53428\",\"TCP/53373\",\"TCP/53407\",\"TCP/53409\",\"TCP/53375\",\"TCP/53420\",\"TCP/53431\",\"TCP/53347\",\"TCP/53378\",\"TCP/53368\",\"TCP/53423\",\"TCP/53390\",\"TCP/53405\",\"TCP/53413\",\"TCP/53414\",\"TCP/53377\",\"TCP/53360\",\"TCP/53361\",\"TCP/53383\",\"TCP/53418\",\"TCP/53385\"]", "pcap_library": {"file_location": "pcap_library_998000849.pcap", "filesize": "398083 bytes", "md5sum": "882eacb035ec34b681e6553f229fc274", "orig_file_name": "pcap_library_998000849.pcap", "packet_count": 1643}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8132\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8132", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "9d6abc35-3816-4111-b56a-d4bcf85e7712": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "ver": 4, "vid": "8001439", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Salesforce API 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:13:25", "updated_at": "2025-08-08 17:13:25", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "9d6abc35-3816-4111-b56a-d4bcf85e7712", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53203\",\"TCP/53216\",\"TCP/53219\",\"TCP/53265\",\"TCP/53266\",\"TCP/53209\",\"TCP/53221\",\"TCP/53228\",\"TCP/53258\",\"TCP/53264\",\"TCP/53194\",\"TCP/53202\",\"TCP/53210\",\"TCP/53212\",\"TCP/53217\",\"TCP/53198\",\"TCP/53204\",\"TCP/53205\",\"TCP/53208\",\"TCP/53214\",\"TCP/53222\",\"TCP/53226\",\"TCP/53191\",\"TCP/53206\",\"TCP/53235\",\"TCP/53245\",\"TCP/53232\",\"TCP/53236\",\"TCP/53237\",\"TCP/53246\",\"TCP/53252\",\"TCP/53257\",\"TCP/53196\",\"TCP/53199\",\"TCP/53231\",\"TCP/53243\",\"TCP/53247\",\"TCP/53197\",\"TCP/53242\",\"TCP/53251\",\"TCP/53262\",\"TCP/53268\",\"TCP/53190\",\"TCP/53213\",\"TCP/53218\",\"TCP/53255\",\"TCP/53207\",\"TCP/53239\",\"TCP/53259\",\"TCP/53244\",\"TCP/53201\",\"TCP/53234\",\"TCP/53230\",\"TCP/53240\",\"TCP/53241\",\"TCP/53253\",\"TCP/53254\",\"TCP/53267\",\"TCP/53192\",\"TCP/53256\",\"TCP/53233\",\"TCP/53250\",\"TCP/53261\",\"TCP/53195\",\"TCP/53220\",\"TCP/53223\",\"TCP/53263\",\"TCP/53248\",\"TCP/53200\",\"TCP/53249\",\"TCP/53260\",\"TCP/53211\",\"TCP/53224\",\"TCP/53193\",\"TCP/53227\",\"TCP/53225\",\"TCP/53229\",\"TCP/53215\",\"TCP/53238\"]", "pcap_library": {"file_location": "pcap_library_998000848.pcap", "filesize": "378204 bytes", "md5sum": "9056854ec03a69823b723948f53a523c", "orig_file_name": "pcap_library_998000848.pcap", "packet_count": 1503}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8131\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8131", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "620d14a6-b56d-4947-b2c5-ed83944dcbc9": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "ver": 4, "vid": "8001438", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Saefko 木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:12:24", "updated_at": "2025-08-08 17:12:24", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "620d14a6-b56d-4947-b2c5-ed83944dcbc9", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53039\",\"TCP/53043\",\"TCP/53061\",\"TCP/53067\",\"TCP/53077\",\"TCP/53091\",\"TCP/53034\",\"TCP/53059\",\"TCP/53104\",\"TCP/53111\",\"TCP/53114\",\"TCP/53119\",\"TCP/53031\",\"TCP/53032\",\"TCP/53033\",\"TCP/53051\",\"TCP/53052\",\"TCP/53068\",\"TCP/53053\",\"TCP/53055\",\"TCP/53094\",\"TCP/53097\",\"TCP/53100\",\"TCP/53102\",\"TCP/53106\",\"TCP/53064\",\"TCP/53065\",\"TCP/53070\",\"TCP/53108\",\"TCP/53109\",\"TCP/53115\",\"TCP/53120\",\"TCP/53060\",\"TCP/53095\",\"TCP/53101\",\"TCP/53103\",\"TCP/53112\",\"TCP/53085\",\"TCP/53049\",\"TCP/53076\",\"TCP/53082\",\"TCP/53110\",\"TCP/53113\",\"TCP/53057\",\"TCP/53030\",\"TCP/53058\",\"TCP/53081\",\"TCP/53041\",\"TCP/53080\",\"TCP/53116\",\"TCP/53036\",\"TCP/53069\",\"TCP/53072\",\"TCP/53042\",\"TCP/53062\",\"TCP/53063\",\"TCP/53096\",\"TCP/53038\",\"TCP/53044\",\"TCP/53075\",\"TCP/53099\",\"TCP/53105\",\"TCP/53037\",\"TCP/53040\",\"TCP/53088\",\"TCP/53083\",\"TCP/53046\",\"TCP/53066\",\"TCP/53071\",\"TCP/53079\",\"TCP/53089\",\"TCP/53048\",\"TCP/53073\",\"TCP/53093\",\"TCP/53087\",\"TCP/53118\",\"TCP/53090\",\"TCP/53092\",\"TCP/53107\",\"TCP/53035\",\"TCP/53056\",\"TCP/53098\",\"TCP/53054\",\"TCP/53045\",\"TCP/53047\",\"TCP/53074\",\"TCP/53078\",\"TCP/53084\",\"TCP/53117\",\"TCP/53050\",\"TCP/53086\"]", "pcap_library": {"file_location": "pcap_library_998000847.pcap", "filesize": "393848 bytes", "md5sum": "3d53e23454dae2cc91eadcaed38ae265", "orig_file_name": "pcap_library_998000847.pcap", "packet_count": 1698}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8114\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8114", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "50d58cbc-153c-4273-92a3-3ab8fc51ef89": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "ver": 4, "vid": "8001437", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 RigEK 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:11:44", "updated_at": "2025-08-08 17:11:44", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "50d58cbc-153c-4273-92a3-3ab8fc51ef89", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52977\",\"TCP/52979\",\"TCP/52983\",\"TCP/53007\",\"TCP/53010\",\"TCP/53013\",\"TCP/53015\",\"TCP/53019\",\"TCP/52986\",\"TCP/52996\",\"TCP/53009\",\"TCP/53012\",\"TCP/53016\",\"TCP/53020\",\"TCP/53025\",\"TCP/52991\",\"TCP/52992\",\"TCP/52998\",\"TCP/53005\",\"TCP/53018\",\"TCP/52981\",\"TCP/52985\",\"TCP/52993\",\"TCP/52994\",\"TCP/53026\",\"TCP/53006\",\"TCP/53014\",\"TCP/53021\",\"TCP/52984\",\"TCP/53003\",\"TCP/52990\",\"TCP/53000\",\"TCP/53008\",\"TCP/53023\",\"TCP/52989\",\"TCP/52976\",\"TCP/53002\",\"TCP/53004\",\"TCP/52978\",\"TCP/52988\",\"TCP/53011\",\"TCP/53017\",\"TCP/52982\",\"TCP/53022\",\"TCP/52980\",\"TCP/52995\",\"TCP/53024\",\"TCP/52987\",\"TCP/52997\",\"TCP/53001\",\"TCP/52999\"]", "pcap_library": {"file_location": "pcap_library_998000846.pcap", "filesize": "318392 bytes", "md5sum": "ac24141c8f3a32762162a0e6484cd275", "orig_file_name": "pcap_library_998000846.pcap", "packet_count": 969}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8113\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8113", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "ver": 4, "vid": "8001436", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Reddit 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:10:57", "updated_at": "2025-08-08 17:10:57", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "68c6399b-61f1-4cb1-a7b5-6c066b1fbd37", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52848\",\"TCP/52856\",\"TCP/52873\",\"TCP/52835\",\"TCP/52844\",\"TCP/52861\",\"TCP/52880\",\"TCP/52813\",\"TCP/52816\",\"TCP/52823\",\"TCP/52825\",\"TCP/52837\",\"TCP/52839\",\"TCP/52849\",\"TCP/52855\",\"TCP/52868\",\"TCP/52871\",\"TCP/52875\",\"TCP/52812\",\"TCP/52836\",\"TCP/52841\",\"TCP/52859\",\"TCP/52872\",\"TCP/52826\",\"TCP/52830\",\"TCP/52842\",\"TCP/52850\",\"TCP/52853\",\"TCP/52862\",\"TCP/52878\",\"TCP/52811\",\"TCP/52833\",\"TCP/52877\",\"TCP/52820\",\"TCP/52821\",\"TCP/52824\",\"TCP/52829\",\"TCP/52838\",\"TCP/52854\",\"TCP/52817\",\"TCP/52840\",\"TCP/52865\",\"TCP/52867\",\"TCP/52869\",\"TCP/52879\",\"TCP/52828\",\"TCP/52831\",\"TCP/52834\",\"TCP/52845\",\"TCP/52866\",\"TCP/52858\",\"TCP/52860\",\"TCP/52874\",\"TCP/52846\",\"TCP/52819\",\"TCP/52827\",\"TCP/52876\",\"TCP/52864\",\"TCP/52814\",\"TCP/52847\",\"TCP/52881\",\"TCP/52818\",\"TCP/52843\",\"TCP/52870\",\"TCP/52832\",\"TCP/52852\",\"TCP/52815\",\"TCP/52822\",\"TCP/52851\",\"TCP/52882\",\"TCP/52857\",\"TCP/52863\"]", "pcap_library": {"file_location": "pcap_library_998000845.pcap", "filesize": "330666 bytes", "md5sum": "b0a5a98831043cb42e4be15111e4d782", "orig_file_name": "pcap_library_998000845.pcap", "packet_count": 1275}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8130\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8130", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "eb3c3928-0001-4158-82f2-94ea25fe3231": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "ver": 4, "vid": "8001435", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Ratankba 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:10:13", "updated_at": "2025-08-08 17:10:13", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "eb3c3928-0001-4158-82f2-94ea25fe3231", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52738\",\"TCP/52732\",\"TCP/52723\",\"TCP/52743\",\"TCP/52754\",\"TCP/52755\",\"TCP/52768\",\"TCP/52728\",\"TCP/52737\",\"TCP/52764\",\"TCP/52766\",\"TCP/52767\",\"TCP/52776\",\"TCP/52784\",\"TCP/52788\",\"TCP/52739\",\"TCP/52798\",\"TCP/52780\",\"TCP/52762\",\"TCP/52778\",\"TCP/52791\",\"TCP/52797\",\"TCP/52730\",\"TCP/52744\",\"TCP/52756\",\"TCP/52759\",\"TCP/52750\",\"TCP/52782\",\"TCP/52761\",\"TCP/52771\",\"TCP/52746\",\"TCP/52758\",\"TCP/52793\",\"TCP/52794\",\"TCP/52775\",\"TCP/52777\",\"TCP/52748\",\"TCP/52729\",\"TCP/52734\",\"TCP/52752\",\"TCP/52769\",\"TCP/52785\",\"TCP/52796\",\"TCP/52770\",\"TCP/52790\",\"TCP/52795\",\"TCP/52724\",\"TCP/52747\",\"TCP/52765\",\"TCP/52774\",\"TCP/52741\",\"TCP/52760\",\"TCP/52787\",\"TCP/52773\",\"TCP/52781\",\"TCP/52736\",\"TCP/52740\",\"TCP/52783\",\"TCP/52722\",\"TCP/52742\",\"TCP/52749\",\"TCP/52779\",\"TCP/52789\",\"TCP/52753\",\"TCP/52786\",\"TCP/52733\",\"TCP/52751\",\"TCP/52772\",\"TCP/52792\",\"TCP/52745\",\"TCP/52735\",\"TCP/52757\",\"TCP/52763\"]", "pcap_library": {"file_location": "pcap_library_998000844.pcap", "filesize": "341453 bytes", "md5sum": "38fa3452e76a9e5cb54f6f8cb164f2e0", "orig_file_name": "pcap_library_998000844.pcap", "packet_count": 1390}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8097\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8097", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "fabce498-4265-425e-8d0d-e86a3e15863a": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "ver": 4, "vid": "8001434", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Ramnit 木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:09:09", "updated_at": "2025-08-08 17:09:09", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "fabce498-4265-425e-8d0d-e86a3e15863a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52550\",\"TCP/52553\",\"TCP/52564\",\"TCP/52569\",\"TCP/52577\",\"TCP/52584\",\"TCP/52597\",\"TCP/52567\",\"TCP/52573\",\"TCP/52574\",\"TCP/52583\",\"TCP/52595\",\"TCP/52596\",\"TCP/52560\",\"TCP/52575\",\"TCP/52578\",\"TCP/52591\",\"TCP/52582\",\"TCP/52566\",\"TCP/52571\",\"TCP/52593\",\"TCP/52554\",\"TCP/52556\",\"TCP/52558\",\"TCP/52561\",\"TCP/52565\",\"TCP/52581\",\"TCP/52586\",\"TCP/52589\",\"TCP/52548\",\"TCP/52552\",\"TCP/52562\",\"TCP/52563\",\"TCP/52580\",\"TCP/52592\",\"TCP/52549\",\"TCP/52557\",\"TCP/52568\",\"TCP/52598\",\"TCP/52572\",\"TCP/52585\",\"TCP/52588\",\"TCP/52551\",\"TCP/52594\",\"TCP/52599\",\"TCP/52555\",\"TCP/52576\",\"TCP/52579\",\"TCP/52590\",\"TCP/52570\",\"TCP/52600\",\"TCP/52559\",\"TCP/52587\"]", "pcap_library": {"file_location": "pcap_library_998000843.pcap", "filesize": "275874 bytes", "md5sum": "8bb3427f41ed7dfda616cf716872d8d5", "orig_file_name": "pcap_library_998000843.pcap", "packet_count": 915}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8112\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8112", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "ver": 4, "vid": "8001433", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 QuantLoader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:07:56", "updated_at": "2025-08-08 17:07:56", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "29cf5c63-92a2-4fc4-b2f6-da9eaf8eb9b3", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52496\",\"TCP/52498\",\"TCP/52531\",\"TCP/52483\",\"TCP/52503\",\"TCP/52508\",\"TCP/52484\",\"TCP/52472\",\"TCP/52474\",\"TCP/52488\",\"TCP/52490\",\"TCP/52493\",\"TCP/52509\",\"TCP/52512\",\"TCP/52505\",\"TCP/52475\",\"TCP/52479\",\"TCP/52480\",\"TCP/52482\",\"TCP/52516\",\"TCP/52532\",\"TCP/52536\",\"TCP/52499\",\"TCP/52471\",\"TCP/52519\",\"TCP/52491\",\"TCP/52521\",\"TCP/52534\",\"TCP/52538\",\"TCP/52546\",\"TCP/52494\",\"TCP/52506\",\"TCP/52510\",\"TCP/52517\",\"TCP/52528\",\"TCP/52476\",\"TCP/52477\",\"TCP/52485\",\"TCP/52500\",\"TCP/52524\",\"TCP/52530\",\"TCP/52535\",\"TCP/52507\",\"TCP/52529\",\"TCP/52533\",\"TCP/52545\",\"TCP/52518\",\"TCP/52525\",\"TCP/52489\",\"TCP/52544\",\"TCP/52495\",\"TCP/52501\",\"TCP/52502\",\"TCP/52504\",\"TCP/52527\",\"TCP/52541\",\"TCP/52543\",\"TCP/52478\",\"TCP/52486\",\"TCP/52481\",\"TCP/52520\",\"TCP/52497\",\"TCP/52537\",\"TCP/52513\",\"TCP/52540\",\"TCP/52542\",\"TCP/52511\",\"TCP/52487\",\"TCP/52526\",\"TCP/52522\",\"TCP/52539\",\"TCP/52514\",\"TCP/52515\",\"TCP/52523\",\"TCP/52473\",\"TCP/52492\"]", "pcap_library": {"file_location": "pcap_library_998000842.pcap", "filesize": "339410 bytes", "md5sum": "8921c0bb004e93357d538362c1fe11e6", "orig_file_name": "pcap_library_998000842.pcap", "packet_count": 1316}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8111\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8111", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "a2664f19-6a95-4a75-b507-aa6ec4450a52": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "ver": 4, "vid": "8001432", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 QakBot 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:07:13", "updated_at": "2025-08-08 17:07:13", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "a2664f19-6a95-4a75-b507-aa6ec4450a52", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52419\",\"TCP/52440\",\"TCP/52442\",\"TCP/52444\",\"TCP/52447\",\"TCP/52449\",\"TCP/52400\",\"TCP/52411\",\"TCP/52429\",\"TCP/52433\",\"TCP/52438\",\"TCP/52401\",\"TCP/52406\",\"TCP/52413\",\"TCP/52415\",\"TCP/52425\",\"TCP/52435\",\"TCP/52439\",\"TCP/52446\",\"TCP/52414\",\"TCP/52426\",\"TCP/52428\",\"TCP/52434\",\"TCP/52418\",\"TCP/52420\",\"TCP/52421\",\"TCP/52422\",\"TCP/52445\",\"TCP/52405\",\"TCP/52391\",\"TCP/52398\",\"TCP/52402\",\"TCP/52423\",\"TCP/52424\",\"TCP/52427\",\"TCP/52393\",\"TCP/52399\",\"TCP/52403\",\"TCP/52408\",\"TCP/52409\",\"TCP/52392\",\"TCP/52430\",\"TCP/52431\",\"TCP/52432\",\"TCP/52443\",\"TCP/52417\",\"TCP/52448\",\"TCP/52407\",\"TCP/52416\",\"TCP/52441\",\"TCP/52394\",\"TCP/52404\",\"TCP/52436\",\"TCP/52412\",\"TCP/52410\",\"TCP/52437\"]", "pcap_library": {"file_location": "pcap_library_998000841.pcap", "filesize": "276868 bytes", "md5sum": "a590a8b06f0b5f4ec23dbe37275341ec", "orig_file_name": "pcap_library_998000841.pcap", "packet_count": 968}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8110\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8110", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "1ba7b572-bf19-4834-b892-84ac7c1705d9": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "ver": 4, "vid": "8001431", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 PowRuner 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:05:14", "updated_at": "2025-08-08 17:05:14", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "1ba7b572-bf19-4834-b892-84ac7c1705d9", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52326\",\"TCP/52354\",\"TCP/52363\",\"TCP/52364\",\"TCP/52371\",\"TCP/52338\",\"TCP/52373\",\"TCP/52313\",\"TCP/52319\",\"TCP/52323\",\"TCP/52340\",\"TCP/52372\",\"TCP/52320\",\"TCP/52331\",\"TCP/52315\",\"TCP/52359\",\"TCP/52370\",\"TCP/52329\",\"TCP/52352\",\"TCP/52355\",\"TCP/52374\",\"TCP/52327\",\"TCP/52332\",\"TCP/52357\",\"TCP/52316\",\"TCP/52339\",\"TCP/52351\",\"TCP/52369\",\"TCP/52375\",\"TCP/52308\",\"TCP/52309\",\"TCP/52333\",\"TCP/52341\",\"TCP/52343\",\"TCP/52350\",\"TCP/52377\",\"TCP/52337\",\"TCP/52342\",\"TCP/52348\",\"TCP/52361\",\"TCP/52356\",\"TCP/52311\",\"TCP/52321\",\"TCP/52328\",\"TCP/52360\",\"TCP/52367\",\"TCP/52318\",\"TCP/52312\",\"TCP/52334\",\"TCP/52335\",\"TCP/52336\",\"TCP/52324\",\"TCP/52345\",\"TCP/52314\",\"TCP/52344\",\"TCP/52353\",\"TCP/52365\",\"TCP/52376\",\"TCP/52317\",\"TCP/52322\",\"TCP/52362\",\"TCP/52310\",\"TCP/52349\",\"TCP/52346\",\"TCP/52347\",\"TCP/52366\",\"TCP/52325\",\"TCP/52368\",\"TCP/52330\"]", "pcap_library": {"file_location": "pcap_library_998000840.pcap", "filesize": "310680 bytes", "md5sum": "fad118613c64e5954435b0902448edf2", "orig_file_name": "pcap_library_998000840.pcap", "packet_count": 1184}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8096\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8096", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "719ef365-6a61-4b33-a484-796f6a2a0abd": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "ver": 4, "vid": "8001430", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 POSeidon 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:04:27", "updated_at": "2025-08-08 17:04:27", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "719ef365-6a61-4b33-a484-796f6a2a0abd", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52253\",\"TCP/52264\",\"TCP/52284\",\"TCP/52292\",\"TCP/52297\",\"TCP/52302\",\"TCP/52248\",\"TCP/52286\",\"TCP/52287\",\"TCP/52303\",\"TCP/52265\",\"TCP/52274\",\"TCP/52293\",\"TCP/52299\",\"TCP/52251\",\"TCP/52256\",\"TCP/52257\",\"TCP/52268\",\"TCP/52270\",\"TCP/52281\",\"TCP/52289\",\"TCP/52290\",\"TCP/52260\",\"TCP/52261\",\"TCP/52266\",\"TCP/52279\",\"TCP/52282\",\"TCP/52288\",\"TCP/52291\",\"TCP/52250\",\"TCP/52252\",\"TCP/52254\",\"TCP/52269\",\"TCP/52271\",\"TCP/52277\",\"TCP/52249\",\"TCP/52255\",\"TCP/52258\",\"TCP/52267\",\"TCP/52275\",\"TCP/52295\",\"TCP/52263\",\"TCP/52272\",\"TCP/52280\",\"TCP/52259\",\"TCP/52296\",\"TCP/52300\",\"TCP/52301\",\"TCP/52262\",\"TCP/52276\",\"TCP/52278\",\"TCP/52294\",\"TCP/52298\",\"TCP/52273\",\"TCP/52283\",\"TCP/52285\"]", "pcap_library": {"file_location": "pcap_library_998000839.pcap", "filesize": "290960 bytes", "md5sum": "aed2cf7be76b275acecb6e83ec985af5", "orig_file_name": "pcap_library_998000839.pcap", "packet_count": 1065}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8109\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8109", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "584ab900-466d-4450-b4d5-6c0018ba4585": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "ver": 4, "vid": "8001429", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Office 365 Calendar 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 17:03:15", "updated_at": "2025-08-08 17:03:15", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "584ab900-466d-4450-b4d5-6c0018ba4585", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52191\",\"TCP/52181\",\"TCP/52182\",\"TCP/52192\",\"TCP/52196\",\"TCP/52177\",\"TCP/52189\",\"TCP/52206\",\"TCP/52178\",\"TCP/52183\",\"TCP/52197\",\"TCP/52198\",\"TCP/52201\",\"TCP/52187\",\"TCP/52199\",\"TCP/52207\",\"TCP/52200\",\"TCP/52205\",\"TCP/52190\",\"TCP/52195\",\"TCP/52203\",\"TCP/52202\",\"TCP/52204\",\"TCP/52179\",\"TCP/52184\",\"TCP/52180\",\"TCP/52186\",\"TCP/52193\",\"TCP/52194\",\"TCP/52185\"]", "pcap_library": {"file_location": "pcap_library_998000838.pcap", "filesize": "220070 bytes", "md5sum": "737b41163b9e7bf0531588c7dca83d44", "orig_file_name": "pcap_library_998000838.pcap", "packet_count": 571}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8129\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8129", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "18b19f19-1887-4365-b799-ad5e6fd26869": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "ver": 4, "vid": "8001428", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 msu edu 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:59:53", "updated_at": "2025-08-08 16:59:53", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "18b19f19-1887-4365-b799-ad5e6fd26869", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52102\",\"TCP/52111\",\"TCP/52115\",\"TCP/52116\",\"TCP/52097\",\"TCP/52099\",\"TCP/52100\",\"TCP/52113\",\"TCP/52101\",\"TCP/52104\",\"TCP/52108\",\"TCP/52112\",\"TCP/52114\",\"TCP/52121\",\"TCP/52105\",\"TCP/52109\",\"TCP/52120\",\"TCP/52107\",\"TCP/52096\",\"TCP/52098\",\"TCP/52106\",\"TCP/52094\",\"TCP/52095\",\"TCP/52117\",\"TCP/52118\",\"TCP/52119\",\"TCP/52103\",\"TCP/52110\"]", "pcap_library": {"file_location": "pcap_library_998000837.pcap", "filesize": "213141 bytes", "md5sum": "5c8eb26e498afb9b1e5801f7ea6d206d", "orig_file_name": "pcap_library_998000837.pcap", "packet_count": 548}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8128\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8128", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "ver": 4, "vid": "8001427", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟微软 mscrl 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:54:36", "updated_at": "2025-08-08 16:54:36", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "f15448f2-d0a9-4ec4-b04d-d8e376bf8ff0", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51876\",\"TCP/51879\",\"TCP/51895\",\"TCP/51862\",\"TCP/51863\",\"TCP/51878\",\"TCP/51893\",\"TCP/51886\",\"TCP/51874\",\"TCP/51894\",\"TCP/51896\",\"TCP/51900\",\"TCP/51901\",\"TCP/51877\",\"TCP/51885\",\"TCP/51887\",\"TCP/51888\",\"TCP/51868\",\"TCP/51872\",\"TCP/51880\",\"TCP/51867\",\"TCP/51869\",\"TCP/51870\",\"TCP/51865\",\"TCP/51866\",\"TCP/51875\",\"TCP/51889\",\"TCP/51871\",\"TCP/51891\",\"TCP/51898\",\"TCP/51899\",\"TCP/51881\",\"TCP/51882\",\"TCP/51884\",\"TCP/51897\",\"TCP/51864\",\"TCP/51873\"]", "pcap_library": {"file_location": "pcap_library_998000836.pcap", "filesize": "231810 bytes", "md5sum": "4bb7aab85cf1c260fbf5c546acc2d736", "orig_file_name": "pcap_library_998000836.pcap", "packet_count": 701}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8127\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8127", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "8933b28d-1a1e-4bbd-b046-57410dc9f71e": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "ver": 4, "vid": "8001426", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Mayoclinic 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:53:14", "updated_at": "2025-08-08 16:53:14", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "8933b28d-1a1e-4bbd-b046-57410dc9f71e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51816\",\"TCP/51822\",\"TCP/51826\",\"TCP/51828\",\"TCP/51808\",\"TCP/51813\",\"TCP/51814\",\"TCP/51817\",\"TCP/51818\",\"TCP/51820\",\"TCP/51803\",\"TCP/51805\",\"TCP/51806\",\"TCP/51807\",\"TCP/51819\",\"TCP/51825\",\"TCP/51804\",\"TCP/51810\",\"TCP/51824\",\"TCP/51827\",\"TCP/51809\",\"TCP/51821\",\"TCP/51829\",\"TCP/51823\",\"TCP/51802\",\"TCP/51815\",\"TCP/51812\",\"TCP/51811\"]", "pcap_library": {"file_location": "pcap_library_998000835.pcap", "filesize": "248582 bytes", "md5sum": "5996cd8fac257c78fcec07b03b716d5e", "orig_file_name": "pcap_library_998000835.pcap", "packet_count": 533}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8126\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8126", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "f4170305-7995-4c12-b669-fc80f5237d57": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "ver": 4, "vid": "8001425", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Kronos 木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:52:04", "updated_at": "2025-08-08 16:52:04", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "f4170305-7995-4c12-b669-fc80f5237d57", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51745\",\"TCP/51760\",\"TCP/51765\",\"TCP/51771\",\"TCP/51728\",\"TCP/51739\",\"TCP/51741\",\"TCP/51749\",\"TCP/51750\",\"TCP/51761\",\"TCP/51769\",\"TCP/51742\",\"TCP/51751\",\"TCP/51763\",\"TCP/51731\",\"TCP/51746\",\"TCP/51747\",\"TCP/51754\",\"TCP/51756\",\"TCP/51758\",\"TCP/51759\",\"TCP/51732\",\"TCP/51738\",\"TCP/51743\",\"TCP/51752\",\"TCP/51767\",\"TCP/51727\",\"TCP/51735\",\"TCP/51736\",\"TCP/51730\",\"TCP/51748\",\"TCP/51753\",\"TCP/51734\",\"TCP/51770\",\"TCP/51729\",\"TCP/51737\",\"TCP/51744\",\"TCP/51733\",\"TCP/51755\",\"TCP/51762\",\"TCP/51764\",\"TCP/51766\",\"TCP/51740\",\"TCP/51768\"]", "pcap_library": {"file_location": "pcap_library_998000834.pcap", "filesize": "256398 bytes", "md5sum": "7f76f9c9fadcae1035ceb42bf13a2d19", "orig_file_name": "pcap_library_998000834.pcap", "packet_count": 855}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8108\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8108", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "71fe1c8f-21b2-41aa-b210-5c528c2602a8": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "ver": 4, "vid": "8001424", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 JasperLoader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:51:19", "updated_at": "2025-08-08 16:51:19", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "71fe1c8f-21b2-41aa-b210-5c528c2602a8", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51679\",\"TCP/51637\",\"TCP/51638\",\"TCP/51641\",\"TCP/51654\",\"TCP/51661\",\"TCP/51676\",\"TCP/51639\",\"TCP/51650\",\"TCP/51651\",\"TCP/51653\",\"TCP/51658\",\"TCP/51666\",\"TCP/51674\",\"TCP/51631\",\"TCP/51649\",\"TCP/51672\",\"TCP/51673\",\"TCP/51652\",\"TCP/51660\",\"TCP/51663\",\"TCP/51635\",\"TCP/51642\",\"TCP/51647\",\"TCP/51648\",\"TCP/51657\",\"TCP/51659\",\"TCP/51644\",\"TCP/51645\",\"TCP/51662\",\"TCP/51665\",\"TCP/51675\",\"TCP/51630\",\"TCP/51646\",\"TCP/51656\",\"TCP/51632\",\"TCP/51677\",\"TCP/51636\",\"TCP/51664\",\"TCP/51633\",\"TCP/51634\",\"TCP/51655\",\"TCP/51667\",\"TCP/51640\",\"TCP/51643\",\"TCP/51678\"]", "pcap_library": {"file_location": "pcap_library_998000833.pcap", "filesize": "264382 bytes", "md5sum": "7049a326ccf70fb58424eb2dfe290a33", "orig_file_name": "pcap_library_998000833.pcap", "packet_count": 792}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8107\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8107", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "9dc94478-21a0-4d30-965f-7e153298e7a9": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "ver": 4, "vid": "8001423", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Jaff 勒索流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:50:32", "updated_at": "2025-08-08 16:50:32", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "9dc94478-21a0-4d30-965f-7e153298e7a9", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51602\",\"TCP/51606\",\"TCP/51613\",\"TCP/51615\",\"TCP/51616\",\"TCP/51618\",\"TCP/51600\",\"TCP/51605\",\"TCP/51612\",\"TCP/51619\",\"TCP/51623\",\"TCP/51625\",\"TCP/51611\",\"TCP/51601\",\"TCP/51604\",\"TCP/51610\",\"TCP/51614\",\"TCP/51617\",\"TCP/51620\",\"TCP/51624\",\"TCP/51621\",\"TCP/51622\",\"TCP/51603\",\"TCP/51607\",\"TCP/51608\",\"TCP/51609\",\"TCP/51599\"]", "pcap_library": {"file_location": "pcap_library_998000832.pcap", "filesize": "198194 bytes", "md5sum": "3bfb09e84f9ae14ff3fa2c31b979418f", "orig_file_name": "pcap_library_998000832.pcap", "packet_count": 470}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8106\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8106", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "979c820a-b314-4e0b-a3b7-9395c1402767": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "ver": 4, "vid": "8001422", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 iHeartRadio 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:49:55", "updated_at": "2025-08-08 16:49:55", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "979c820a-b314-4e0b-a3b7-9395c1402767", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51553\",\"TCP/51554\",\"TCP/51567\",\"TCP/51576\",\"TCP/51577\",\"TCP/51578\",\"TCP/51550\",\"TCP/51552\",\"TCP/51564\",\"TCP/51583\",\"TCP/51585\",\"TCP/51548\",\"TCP/51570\",\"TCP/51571\",\"TCP/51581\",\"TCP/51575\",\"TCP/51561\",\"TCP/51568\",\"TCP/51573\",\"TCP/51549\",\"TCP/51562\",\"TCP/51563\",\"TCP/51579\",\"TCP/51588\",\"TCP/51547\",\"TCP/51566\",\"TCP/51584\",\"TCP/51551\",\"TCP/51560\",\"TCP/51565\",\"TCP/51574\",\"TCP/51586\",\"TCP/51555\",\"TCP/51558\",\"TCP/51569\",\"TCP/51572\",\"TCP/51580\",\"TCP/51582\",\"TCP/51587\",\"TCP/51559\",\"TCP/51557\"]", "pcap_library": {"file_location": "pcap_library_998000831.pcap", "filesize": "266065 bytes", "md5sum": "bb73e5a66b328e11ab9d6335f13f0585", "orig_file_name": "pcap_library_998000831.pcap", "packet_count": 791}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8125\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8125", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "af134f0c-98c5-499d-8cb9-291d57703ba1": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "ver": 4, "vid": "8001421", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Hancitor 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:49:01", "updated_at": "2025-08-08 16:49:01", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "af134f0c-98c5-499d-8cb9-291d57703ba1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51498\",\"TCP/51500\",\"TCP/51513\",\"TCP/51494\",\"TCP/51495\",\"TCP/51501\",\"TCP/51502\",\"TCP/51508\",\"TCP/51510\",\"TCP/51491\",\"TCP/51506\",\"TCP/51514\",\"TCP/51492\",\"TCP/51497\",\"TCP/51499\",\"TCP/51503\",\"TCP/51504\",\"TCP/51509\",\"TCP/51512\",\"TCP/51493\",\"TCP/51505\",\"TCP/51507\",\"TCP/51511\",\"TCP/51496\"]", "pcap_library": {"file_location": "pcap_library_998000830.pcap", "filesize": "190356 bytes", "md5sum": "602b08a009145ab19133c6f35d821c30", "orig_file_name": "pcap_library_998000830.pcap", "packet_count": 418}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8105\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8105", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "baf559bc-0cdc-4053-888f-2deaca245c8c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "ver": 4, "vid": "8001420", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 GoTo Meeting 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:48:15", "updated_at": "2025-08-08 16:48:15", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "baf559bc-0cdc-4053-888f-2deaca245c8c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51460\",\"TCP/51467\",\"TCP/51472\",\"TCP/51473\",\"TCP/51464\",\"TCP/51480\",\"TCP/51451\",\"TCP/51466\",\"TCP/51475\",\"TCP/51479\",\"TCP/51481\",\"TCP/51455\",\"TCP/51458\",\"TCP/51476\",\"TCP/51483\",\"TCP/51485\",\"TCP/51454\",\"TCP/51457\",\"TCP/51470\",\"TCP/51484\",\"TCP/51462\",\"TCP/51474\",\"TCP/51477\",\"TCP/51478\",\"TCP/51456\",\"TCP/51461\",\"TCP/51468\",\"TCP/51482\",\"TCP/51459\",\"TCP/51471\",\"TCP/51463\",\"TCP/51452\",\"TCP/51453\",\"TCP/51465\",\"TCP/51469\",\"TCP/51486\"]", "pcap_library": {"file_location": "pcap_library_998000829.pcap", "filesize": "235978 bytes", "md5sum": "bdb5c81b44c7f10396b4a62706780385", "orig_file_name": "pcap_library_998000829.pcap", "packet_count": 689}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8124\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8124", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "c4205dec-e8b5-490e-99c5-1e403d72c925": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "ver": 4, "vid": "8001419", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 GlobeImposter 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:47:25", "updated_at": "2025-08-08 16:47:25", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "c4205dec-e8b5-490e-99c5-1e403d72c925", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51393\",\"TCP/51374\",\"TCP/51365\",\"TCP/51366\",\"TCP/51373\",\"TCP/51381\",\"TCP/51388\",\"TCP/51392\",\"TCP/51375\",\"TCP/51369\",\"TCP/51395\",\"TCP/51397\",\"TCP/51387\",\"TCP/51364\",\"TCP/51385\",\"TCP/51389\",\"TCP/51400\",\"TCP/51367\",\"TCP/51383\",\"TCP/51368\",\"TCP/51380\",\"TCP/51382\",\"TCP/51363\",\"TCP/51370\",\"TCP/51384\",\"TCP/51394\",\"TCP/51399\",\"TCP/51376\",\"TCP/51398\",\"TCP/51386\",\"TCP/51390\",\"TCP/51391\",\"TCP/51362\",\"TCP/51372\",\"TCP/51371\",\"TCP/51396\"]", "pcap_library": {"file_location": "pcap_library_998000828.pcap", "filesize": "225571 bytes", "md5sum": "fd47c9df8be8ec63131950fd6ffe1781", "orig_file_name": "pcap_library_998000828.pcap", "packet_count": 630}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8104\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8104", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "ver": 4, "vid": "8001418", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 GandCrab 勒索流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:46:31", "updated_at": "2025-08-08 16:46:31", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "a192fec5-ce2e-47ab-b4ee-3cee64c27c3c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51322\",\"TCP/51324\",\"TCP/51339\",\"TCP/51344\",\"TCP/51353\",\"TCP/51323\",\"TCP/51342\",\"TCP/51347\",\"TCP/51351\",\"TCP/51326\",\"TCP/51330\",\"TCP/51334\",\"TCP/51337\",\"TCP/51345\",\"TCP/51352\",\"TCP/51332\",\"TCP/51338\",\"TCP/51346\",\"TCP/51354\",\"TCP/51328\",\"TCP/51333\",\"TCP/51341\",\"TCP/51350\",\"TCP/51336\",\"TCP/51343\",\"TCP/51331\",\"TCP/51327\",\"TCP/51329\",\"TCP/51340\",\"TCP/51325\",\"TCP/51335\",\"TCP/51349\",\"TCP/51355\",\"TCP/51348\"]", "pcap_library": {"file_location": "pcap_library_998000827.pcap", "filesize": "220178 bytes", "md5sum": "293eafd87fc021d8156bd047ef37ccda", "orig_file_name": "pcap_library_998000827.pcap", "packet_count": 652}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8103\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8103", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "cf32e7e3-5017-4737-a7ed-207749ad6644": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "ver": 4, "vid": "8001417", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 FormBook 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:45:39", "updated_at": "2025-08-08 16:45:39", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "cf32e7e3-5017-4737-a7ed-207749ad6644", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51270\",\"TCP/51271\",\"TCP/51272\",\"TCP/51273\",\"TCP/51274\",\"TCP/51282\",\"TCP/51244\",\"TCP/51247\",\"TCP/51249\",\"TCP/51250\",\"TCP/51253\",\"TCP/51260\",\"TCP/51265\",\"TCP/51237\",\"TCP/51257\",\"TCP/51277\",\"TCP/51284\",\"TCP/51242\",\"TCP/51246\",\"TCP/51251\",\"TCP/51263\",\"TCP/51267\",\"TCP/51280\",\"TCP/51275\",\"TCP/51279\",\"TCP/51259\",\"TCP/51278\",\"TCP/51281\",\"TCP/51248\",\"TCP/51283\",\"TCP/51238\",\"TCP/51240\",\"TCP/51258\",\"TCP/51262\",\"TCP/51269\",\"TCP/51241\",\"TCP/51243\",\"TCP/51264\",\"TCP/51245\",\"TCP/51261\",\"TCP/51239\",\"TCP/51254\",\"TCP/51255\",\"TCP/51266\",\"TCP/51276\",\"TCP/51268\"]", "pcap_library": {"file_location": "pcap_library_998000826.pcap", "filesize": "261024 bytes", "md5sum": "03cde091d3e7f8a4c15cb381bb88ea97", "orig_file_name": "pcap_library_998000826.pcap", "packet_count": 886}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8102\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8102", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "0c758637-169a-4e1f-a457-636bf0ea603e": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "ver": 4, "vid": "8001416", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Emotet 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:44:57", "updated_at": "2025-08-08 16:44:57", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "0c758637-169a-4e1f-a457-636bf0ea603e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51188\",\"TCP/51189\",\"TCP/51200\",\"TCP/51169\",\"TCP/51172\",\"TCP/51194\",\"TCP/51175\",\"TCP/51177\",\"TCP/51187\",\"TCP/51191\",\"TCP/51198\",\"TCP/51170\",\"TCP/51173\",\"TCP/51182\",\"TCP/51186\",\"TCP/51180\",\"TCP/51192\",\"TCP/51196\",\"TCP/51201\",\"TCP/51171\",\"TCP/51197\",\"TCP/51179\",\"TCP/51167\",\"TCP/51168\",\"TCP/51166\",\"TCP/51190\",\"TCP/51199\",\"TCP/51165\",\"TCP/51181\",\"TCP/51183\",\"TCP/51185\",\"TCP/51193\",\"TCP/51174\",\"TCP/51195\",\"TCP/51184\"]", "pcap_library": {"file_location": "pcap_library_998000825.pcap", "filesize": "229210 bytes", "md5sum": "ae1cfdd429ba1d0a2acdb2162d86a391", "orig_file_name": "pcap_library_998000825.pcap", "packet_count": 617}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8101\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8101", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "d2acc38f-2b46-4c47-8a6a-771119b7b395": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "ver": 4, "vid": "8001415", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 APT29（The Dukes）流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:44:11", "updated_at": "2025-08-08 16:44:11", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "d2acc38f-2b46-4c47-8a6a-771119b7b395", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51124\",\"TCP/51128\",\"TCP/51136\",\"TCP/51150\",\"TCP/51141\",\"TCP/51143\",\"TCP/51152\",\"TCP/51161\",\"TCP/51145\",\"TCP/51153\",\"TCP/51159\",\"TCP/51123\",\"TCP/51131\",\"TCP/51138\",\"TCP/51155\",\"TCP/51158\",\"TCP/51125\",\"TCP/51126\",\"TCP/51127\",\"TCP/51134\",\"TCP/51139\",\"TCP/51157\",\"TCP/51137\",\"TCP/51146\",\"TCP/51160\",\"TCP/51132\",\"TCP/51133\",\"TCP/51162\",\"TCP/51129\",\"TCP/51144\",\"TCP/51147\",\"TCP/51163\",\"TCP/51122\",\"TCP/51156\",\"TCP/51130\",\"TCP/51142\",\"TCP/51140\",\"TCP/51151\",\"TCP/51149\",\"TCP/51135\",\"TCP/51154\"]", "pcap_library": {"file_location": "pcap_library_998000824.pcap", "filesize": "231570 bytes", "md5sum": "a9d00b3067f19cf9d27102e9f1ceab43", "orig_file_name": "pcap_library_998000824.pcap", "packet_count": 711}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8095\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8095", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "54182f32-d085-4571-8132-f341ac33988c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "54182f32-d085-4571-8132-f341ac33988c", "ver": 4, "vid": "8001414", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 COVID-19 Koadic 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:43:23", "updated_at": "2025-08-08 16:43:23", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "54182f32-d085-4571-8132-f341ac33988c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51080\",\"TCP/51082\",\"TCP/51086\",\"TCP/51091\",\"TCP/51075\",\"TCP/51079\",\"TCP/51056\",\"TCP/51062\",\"TCP/51064\",\"TCP/51073\",\"TCP/51094\",\"TCP/51058\",\"TCP/51052\",\"TCP/51065\",\"TCP/51081\",\"TCP/51063\",\"TCP/51072\",\"TCP/51074\",\"TCP/51083\",\"TCP/51088\",\"TCP/51089\",\"TCP/51048\",\"TCP/51061\",\"TCP/51090\",\"TCP/51093\",\"TCP/51043\",\"TCP/51051\",\"TCP/51078\",\"TCP/51084\",\"TCP/51050\",\"TCP/51067\",\"TCP/51042\",\"TCP/51044\",\"TCP/51066\",\"TCP/51085\",\"TCP/51059\",\"TCP/51076\",\"TCP/51060\",\"TCP/51087\",\"TCP/51077\"]", "pcap_library": {"file_location": "pcap_library_998000823.pcap", "filesize": "375462 bytes", "md5sum": "e27d3728875be3fe2337ee3b6da5429d", "orig_file_name": "pcap_library_998000823.pcap", "packet_count": 790}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8099\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8099", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "23ea3cba-dd53-4620-ad76-d2212e1a2528": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "ver": 4, "vid": "8001413", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 COVID-19 Koadic 流量，变种 #1", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:42:31", "updated_at": "2025-08-08 16:42:31", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "23ea3cba-dd53-4620-ad76-d2212e1a2528", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/50985\",\"TCP/50991\",\"TCP/51006\",\"TCP/51011\",\"TCP/51012\",\"TCP/51023\",\"TCP/51025\",\"TCP/51001\",\"TCP/51021\",\"TCP/51005\",\"TCP/50995\",\"TCP/50982\",\"TCP/50989\",\"TCP/51016\",\"TCP/51019\",\"TCP/51024\",\"TCP/51007\",\"TCP/51020\",\"TCP/50983\",\"TCP/51015\",\"TCP/51017\",\"TCP/50984\",\"TCP/51002\",\"TCP/51014\",\"TCP/50986\",\"TCP/50990\",\"TCP/50992\",\"TCP/51022\",\"TCP/50998\",\"TCP/50999\",\"TCP/51008\",\"TCP/51010\",\"TCP/50988\",\"TCP/50996\",\"TCP/51004\",\"TCP/51018\",\"TCP/51009\",\"TCP/50994\",\"TCP/50997\",\"TCP/51000\",\"TCP/51003\",\"TCP/50993\",\"TCP/50987\",\"TCP/51013\"]", "pcap_library": {"file_location": "pcap_library_998000822.pcap", "filesize": "249794 bytes", "md5sum": "f9975d72d4cfcce382d38ab49de14710", "orig_file_name": "pcap_library_998000822.pcap", "packet_count": 764}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8100\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8100", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "2a186b0e-1f32-49d7-9c4e-d65206a069cb": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "ver": 4, "vid": "8001412", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Chrome 浏览器流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:41:43", "updated_at": "2025-08-08 16:41:43", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "2a186b0e-1f32-49d7-9c4e-d65206a069cb", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/50966\",\"TCP/50945\",\"TCP/50952\",\"TCP/50954\",\"TCP/50958\",\"TCP/50961\",\"TCP/50962\",\"TCP/50972\",\"TCP/50971\",\"TCP/50955\",\"TCP/50963\",\"TCP/50964\",\"TCP/50950\",\"TCP/50968\",\"TCP/50973\",\"TCP/50947\",\"TCP/50951\",\"TCP/50967\",\"TCP/50944\",\"TCP/50970\",\"TCP/50949\",\"TCP/50948\",\"TCP/50946\",\"TCP/50953\",\"TCP/50969\",\"TCP/50956\",\"TCP/50957\",\"TCP/50960\",\"TCP/50959\",\"TCP/50965\"]", "pcap_library": {"file_location": "pcap_library_998000821.pcap", "filesize": "220417 bytes", "md5sum": "37078abfafe2c25d07e75ddd28ace668", "orig_file_name": "pcap_library_998000821.pcap", "packet_count": 524}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8123\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8123", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "9a576aac-fa0b-40f6-988d-8d500242109b": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "ver": 4, "vid": "8001411", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 APT10 ChChes 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:40:52", "updated_at": "2025-08-08 16:40:52", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "9a576aac-fa0b-40f6-988d-8d500242109b", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49867\",\"TCP/49856\",\"TCP/49861\",\"TCP/49868\",\"TCP/49881\",\"TCP/49877\",\"TCP/49875\",\"TCP/49851\",\"TCP/49859\",\"TCP/49860\",\"TCP/49866\",\"TCP/49870\",\"TCP/49879\",\"TCP/49857\",\"TCP/49858\",\"TCP/49865\",\"TCP/49880\",\"TCP/49852\",\"TCP/49864\",\"TCP/49876\",\"TCP/49855\",\"TCP/49871\",\"TCP/49872\",\"TCP/49878\",\"TCP/49863\",\"TCP/49873\",\"TCP/49853\",\"TCP/49854\",\"TCP/49874\",\"TCP/49862\",\"TCP/49869\"]", "pcap_library": {"file_location": "pcap_library_998000820.pcap", "filesize": "227216 bytes", "md5sum": "a6c8fca50eafe5a8d378a0640e0ca603", "orig_file_name": "pcap_library_998000820.pcap", "packet_count": 540}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8094\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8094", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "cbbc9d58-1479-473b-8981-df82a44e7274": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "ver": 4, "vid": "8001410", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 BlueNoroff RAT 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:40:03", "updated_at": "2025-08-08 16:40:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "cbbc9d58-1479-473b-8981-df82a44e7274", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49823\",\"TCP/49830\",\"TCP/49831\",\"TCP/49805\",\"TCP/49806\",\"TCP/49826\",\"TCP/49829\",\"TCP/49813\",\"TCP/49828\",\"TCP/49810\",\"TCP/49811\",\"TCP/49836\",\"TCP/49819\",\"TCP/49821\",\"TCP/49827\",\"TCP/49817\",\"TCP/49807\",\"TCP/49812\",\"TCP/49825\",\"TCP/49804\",\"TCP/49808\",\"TCP/49820\",\"TCP/49822\",\"TCP/49818\",\"TCP/49832\",\"TCP/49834\",\"TCP/49824\",\"TCP/49833\",\"TCP/49809\",\"TCP/49814\",\"TCP/49815\"]", "pcap_library": {"file_location": "pcap_library_998000819.pcap", "filesize": "213999 bytes", "md5sum": "398379259c6c00841dde4bf0b3ed1b4d", "orig_file_name": "pcap_library_998000819.pcap", "packet_count": 595}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8093\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8093", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "f0f09282-36ae-4ee8-baf0-a507c5576df6": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "ver": 4, "vid": "8001409", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Bing Map 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:39:11", "updated_at": "2025-08-08 16:39:11", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "f0f09282-36ae-4ee8-baf0-a507c5576df6", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49729\",\"TCP/49736\",\"TCP/49754\",\"TCP/49765\",\"TCP/49768\",\"TCP/49735\",\"TCP/49738\",\"TCP/49740\",\"TCP/49764\",\"TCP/49771\",\"TCP/49737\",\"TCP/49739\",\"TCP/49742\",\"TCP/49749\",\"TCP/49766\",\"TCP/49744\",\"TCP/49747\",\"TCP/49758\",\"TCP/49762\",\"TCP/49767\",\"TCP/49746\",\"TCP/49734\",\"TCP/49755\",\"TCP/49763\",\"TCP/49743\",\"TCP/49760\",\"TCP/49730\",\"TCP/49748\",\"TCP/49757\",\"TCP/49761\",\"TCP/49741\",\"TCP/49756\",\"TCP/49759\",\"TCP/49769\",\"TCP/49770\",\"TCP/49731\",\"TCP/49732\",\"TCP/49733\",\"TCP/49745\",\"TCP/49750\",\"TCP/49751\",\"TCP/49753\",\"TCP/49752\"]", "pcap_library": {"file_location": "pcap_library_998000818.pcap", "filesize": "305426 bytes", "md5sum": "564b53b66a87b975c425bdcce52d1f19", "orig_file_name": "pcap_library_998000818.pcap", "packet_count": 855}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8122\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8122", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "973e68c5-**************-69b555b8b41e": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "973e68c5-**************-69b555b8b41e", "ver": 4, "vid": "8001408", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 BazarLoader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:38:29", "updated_at": "2025-08-08 16:38:29", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "973e68c5-**************-69b555b8b41e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49640\",\"TCP/49642\",\"TCP/49644\",\"TCP/49649\",\"TCP/49653\",\"TCP/49658\",\"TCP/49648\",\"TCP/49652\",\"TCP/49656\",\"TCP/49657\",\"TCP/49676\",\"TCP/49655\",\"TCP/49660\",\"TCP/49663\",\"TCP/49641\",\"TCP/49645\",\"TCP/49670\",\"TCP/49638\",\"TCP/49651\",\"TCP/49654\",\"TCP/49646\",\"TCP/49650\",\"TCP/49639\",\"TCP/49659\",\"TCP/49643\",\"TCP/49673\",\"TCP/49674\",\"TCP/49647\",\"TCP/49662\",\"TCP/49675\",\"TCP/49661\",\"TCP/49671\"]", "pcap_library": {"file_location": "pcap_library_998000817.pcap", "filesize": "211736 bytes", "md5sum": "8f720f35273f13b1558e2eb34061224e", "orig_file_name": "pcap_library_998000817.pcap", "packet_count": 562}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8098\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8098", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "356d07fc-6ab3-4e2c-8d81-bb71507b95b1": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "ver": 4, "vid": "8001407", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 Amazon Web 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:37:22", "updated_at": "2025-08-08 16:37:22", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "356d07fc-6ab3-4e2c-8d81-bb71507b95b1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49572\",\"TCP/49573\",\"TCP/49578\",\"TCP/49580\",\"TCP/49589\",\"TCP/49593\",\"TCP/49600\",\"TCP/49562\",\"TCP/49570\",\"TCP/49586\",\"TCP/49587\",\"TCP/49598\",\"TCP/49603\",\"TCP/49607\",\"TCP/49568\",\"TCP/49571\",\"TCP/49590\",\"TCP/49609\",\"TCP/49559\",\"TCP/49560\",\"TCP/49575\",\"TCP/49581\",\"TCP/49585\",\"TCP/49558\",\"TCP/49563\",\"TCP/49566\",\"TCP/49567\",\"TCP/49569\",\"TCP/49574\",\"TCP/49579\",\"TCP/49561\",\"TCP/49583\",\"TCP/49584\",\"TCP/49588\",\"TCP/49591\",\"TCP/49592\",\"TCP/49565\",\"TCP/49577\",\"TCP/49597\",\"TCP/49602\",\"TCP/49605\",\"TCP/49610\",\"TCP/49576\",\"TCP/49594\",\"TCP/49596\",\"TCP/49595\",\"TCP/49599\",\"TCP/49556\",\"TCP/49564\",\"TCP/49582\",\"TCP/49557\",\"TCP/49601\",\"TCP/49606\",\"TCP/49608\",\"TCP/49604\"]", "pcap_library": {"file_location": "pcap_library_998000816.pcap", "filesize": "328827 bytes", "md5sum": "d9d244f56ae937b6d05331990d2f4e78", "orig_file_name": "pcap_library_998000816.pcap", "packet_count": 1083}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8121\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8121", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "36b12bc5-0c60-4838-911e-04cdd1232151": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "ver": 4, "vid": "8001406", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.9 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:36:41", "updated_at": "2025-08-08 16:36:41", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "36b12bc5-0c60-4838-911e-04cdd1232151", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55747\",\"TCP/55760\",\"TCP/55767\",\"TCP/55782\",\"TCP/55784\",\"TCP/55800\",\"TCP/55735\",\"TCP/55746\",\"TCP/55749\",\"TCP/55738\",\"TCP/55764\",\"TCP/55770\",\"TCP/55787\",\"TCP/55801\",\"TCP/55773\",\"TCP/55793\",\"TCP/55794\",\"TCP/55805\",\"TCP/55786\",\"TCP/55740\",\"TCP/55748\",\"TCP/55753\",\"TCP/55754\",\"TCP/55766\",\"TCP/55777\",\"TCP/55743\",\"TCP/55765\",\"TCP/55795\",\"TCP/55798\",\"TCP/55802\",\"TCP/55776\",\"TCP/55785\",\"TCP/55736\",\"TCP/55755\",\"TCP/55762\",\"TCP/55769\",\"TCP/55751\",\"TCP/55756\",\"TCP/55759\",\"TCP/55761\",\"TCP/55763\",\"TCP/55789\",\"TCP/55804\",\"TCP/55737\",\"TCP/55768\",\"TCP/55775\",\"TCP/55778\",\"TCP/55791\",\"TCP/55803\",\"TCP/55734\",\"TCP/55739\",\"TCP/55781\",\"TCP/55733\",\"TCP/55779\",\"TCP/55780\",\"TCP/55788\",\"TCP/55796\",\"TCP/55750\",\"TCP/55741\",\"TCP/55772\",\"TCP/55745\",\"TCP/55757\",\"TCP/55758\",\"TCP/55783\",\"TCP/55731\",\"TCP/55732\",\"TCP/55744\",\"TCP/55742\",\"TCP/55790\",\"TCP/55774\",\"TCP/55792\",\"TCP/55752\",\"TCP/55797\",\"TCP/55771\",\"TCP/55799\"]", "pcap_library": {"file_location": "pcap_library_998000815.pcap", "filesize": "786094 bytes", "md5sum": "3b65e090ea48b773b51fb803f075708a", "orig_file_name": "pcap_library_998000815.pcap", "packet_count": 1686}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8092\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8092", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "deec36fa-786d-4415-b0eb-f9ad916e879d": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "ver": 4, "vid": "8001405", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.8 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:35:26", "updated_at": "2025-08-08 16:35:26", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "deec36fa-786d-4415-b0eb-f9ad916e879d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55650\",\"TCP/55657\",\"TCP/55685\",\"TCP/55697\",\"TCP/55703\",\"TCP/55676\",\"TCP/55659\",\"TCP/55666\",\"TCP/55671\",\"TCP/55678\",\"TCP/55687\",\"TCP/55708\",\"TCP/55663\",\"TCP/55696\",\"TCP/55710\",\"TCP/55689\",\"TCP/55699\",\"TCP/55702\",\"TCP/55716\",\"TCP/55726\",\"TCP/55705\",\"TCP/55654\",\"TCP/55658\",\"TCP/55673\",\"TCP/55677\",\"TCP/55651\",\"TCP/55656\",\"TCP/55665\",\"TCP/55674\",\"TCP/55684\",\"TCP/55690\",\"TCP/55712\",\"TCP/55655\",\"TCP/55672\",\"TCP/55680\",\"TCP/55681\",\"TCP/55701\",\"TCP/55720\",\"TCP/55722\",\"TCP/55664\",\"TCP/55692\",\"TCP/55698\",\"TCP/55715\",\"TCP/55719\",\"TCP/55723\",\"TCP/55679\",\"TCP/55667\",\"TCP/55686\",\"TCP/55700\",\"TCP/55707\",\"TCP/55711\",\"TCP/55660\",\"TCP/55717\",\"TCP/55652\",\"TCP/55670\",\"TCP/55668\",\"TCP/55682\",\"TCP/55691\",\"TCP/55721\",\"TCP/55662\",\"TCP/55704\",\"TCP/55724\",\"TCP/55669\",\"TCP/55695\",\"TCP/55714\",\"TCP/55653\",\"TCP/55706\",\"TCP/55688\",\"TCP/55709\",\"TCP/55713\",\"TCP/55683\",\"TCP/55725\",\"TCP/55661\",\"TCP/55718\",\"TCP/55693\",\"TCP/55675\",\"TCP/55694\"]", "pcap_library": {"file_location": "pcap_library_998000814.pcap", "filesize": "804459 bytes", "md5sum": "835245eebff39240f2e62acd43fb2720", "orig_file_name": "pcap_library_998000814.pcap", "packet_count": 1734}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8091\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8091", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "90012bd3-7750-40cf-ab59-f39ccf7d370c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "ver": 4, "vid": "8001404", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.7 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:34:03", "updated_at": "2025-08-08 16:34:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "90012bd3-7750-40cf-ab59-f39ccf7d370c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55597\",\"TCP/55603\",\"TCP/55620\",\"TCP/55625\",\"TCP/55592\",\"TCP/55626\",\"TCP/55619\",\"TCP/55624\",\"TCP/55634\",\"TCP/55641\",\"TCP/55605\",\"TCP/55609\",\"TCP/55617\",\"TCP/55627\",\"TCP/55637\",\"TCP/55638\",\"TCP/55591\",\"TCP/55611\",\"TCP/55618\",\"TCP/55642\",\"TCP/55594\",\"TCP/55602\",\"TCP/55587\",\"TCP/55633\",\"TCP/55643\",\"TCP/55610\",\"TCP/55596\",\"TCP/55622\",\"TCP/55629\",\"TCP/55630\",\"TCP/55646\",\"TCP/55600\",\"TCP/55608\",\"TCP/55631\",\"TCP/55588\",\"TCP/55604\",\"TCP/55613\",\"TCP/55614\",\"TCP/55635\",\"TCP/55606\",\"TCP/55589\",\"TCP/55590\",\"TCP/55601\",\"TCP/55607\",\"TCP/55628\",\"TCP/55636\",\"TCP/55593\",\"TCP/55595\",\"TCP/55644\",\"TCP/55640\",\"TCP/55621\",\"TCP/55623\",\"TCP/55639\",\"TCP/55599\",\"TCP/55598\",\"TCP/55615\",\"TCP/55616\",\"TCP/55612\",\"TCP/55645\",\"TCP/55632\"]", "pcap_library": {"file_location": "pcap_library_998000813.pcap", "filesize": "654266 bytes", "md5sum": "ca76ae03a3d1c6b6997e70ad7a10286a", "orig_file_name": "pcap_library_998000813.pcap", "packet_count": 1353}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8090\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8090", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "ver": 4, "vid": "8001403", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.6 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:32:26", "updated_at": "2025-08-08 16:32:26", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "dfd21eb5-ab5a-4768-ae06-e3ff4b0d707c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55515\",\"TCP/55533\",\"TCP/55538\",\"TCP/55543\",\"TCP/55575\",\"TCP/55578\",\"TCP/55509\",\"TCP/55517\",\"TCP/55525\",\"TCP/55560\",\"TCP/55510\",\"TCP/55519\",\"TCP/55545\",\"TCP/55561\",\"TCP/55569\",\"TCP/55516\",\"TCP/55524\",\"TCP/55532\",\"TCP/55556\",\"TCP/55579\",\"TCP/55514\",\"TCP/55527\",\"TCP/55529\",\"TCP/55563\",\"TCP/55565\",\"TCP/55577\",\"TCP/55507\",\"TCP/55534\",\"TCP/55539\",\"TCP/55549\",\"TCP/55511\",\"TCP/55546\",\"TCP/55548\",\"TCP/55550\",\"TCP/55559\",\"TCP/55518\",\"TCP/55542\",\"TCP/55544\",\"TCP/55547\",\"TCP/55572\",\"TCP/55512\",\"TCP/55551\",\"TCP/55552\",\"TCP/55570\",\"TCP/55523\",\"TCP/55530\",\"TCP/55522\",\"TCP/55531\",\"TCP/55557\",\"TCP/55536\",\"TCP/55562\",\"TCP/55564\",\"TCP/55505\",\"TCP/55513\",\"TCP/55521\",\"TCP/55554\",\"TCP/55558\",\"TCP/55567\",\"TCP/55520\",\"TCP/55573\",\"TCP/55580\",\"TCP/55537\",\"TCP/55574\",\"TCP/55576\",\"TCP/55581\",\"TCP/55508\",\"TCP/55528\",\"TCP/55535\",\"TCP/55568\",\"TCP/55571\",\"TCP/55504\",\"TCP/55526\",\"TCP/55555\",\"TCP/55541\",\"TCP/55566\",\"TCP/55503\",\"TCP/55540\",\"TCP/55506\",\"TCP/55553\"]", "pcap_library": {"file_location": "pcap_library_998000812.pcap", "filesize": "822817 bytes", "md5sum": "00bfc6b4f96844f5c4f4fd46cfddb770", "orig_file_name": "pcap_library_998000812.pcap", "packet_count": 1802}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8089\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8089", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "629d3f54-3de4-4a00-8aa3-a5487c7010b5": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "ver": 4, "vid": "8001402", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.5 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:30:50", "updated_at": "2025-08-08 16:30:50", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "629d3f54-3de4-4a00-8aa3-a5487c7010b5", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55477\",\"TCP/55413\",\"TCP/55444\",\"TCP/55467\",\"TCP/55479\",\"TCP/55418\",\"TCP/55434\",\"TCP/55448\",\"TCP/55482\",\"TCP/55489\",\"TCP/55495\",\"TCP/55449\",\"TCP/55438\",\"TCP/55463\",\"TCP/55475\",\"TCP/55424\",\"TCP/55469\",\"TCP/55460\",\"TCP/55415\",\"TCP/55423\",\"TCP/55441\",\"TCP/55476\",\"TCP/55491\",\"TCP/55419\",\"TCP/55435\",\"TCP/55436\",\"TCP/55443\",\"TCP/55484\",\"TCP/55490\",\"TCP/55426\",\"TCP/55465\",\"TCP/55474\",\"TCP/55439\",\"TCP/55462\",\"TCP/55478\",\"TCP/55487\",\"TCP/55420\",\"TCP/55425\",\"TCP/55445\",\"TCP/55446\",\"TCP/55459\",\"TCP/55470\",\"TCP/55422\",\"TCP/55452\",\"TCP/55472\",\"TCP/55429\",\"TCP/55440\",\"TCP/55453\",\"TCP/55458\",\"TCP/55468\",\"TCP/55492\",\"TCP/55437\",\"TCP/55442\",\"TCP/55455\",\"TCP/55416\",\"TCP/55432\",\"TCP/55447\",\"TCP/55450\",\"TCP/55451\",\"TCP/55433\",\"TCP/55481\",\"TCP/55496\",\"TCP/55497\",\"TCP/55427\",\"TCP/55483\",\"TCP/55485\",\"TCP/55493\",\"TCP/55417\",\"TCP/55421\",\"TCP/55486\",\"TCP/55414\",\"TCP/55488\",\"TCP/55461\",\"TCP/55430\",\"TCP/55431\",\"TCP/55473\",\"TCP/55428\",\"TCP/55454\",\"TCP/55471\",\"TCP/55494\",\"TCP/55456\",\"TCP/55466\",\"TCP/55457\",\"TCP/55464\",\"TCP/55480\",\"TCP/55498\"]", "pcap_library": {"file_location": "pcap_library_998000811.pcap", "filesize": "882200 bytes", "md5sum": "8b42226253ad6ecd102f4023b6698978", "orig_file_name": "pcap_library_998000811.pcap", "packet_count": 1925}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8088\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8088", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "79431949-a5c9-4232-9666-2acb0ce78c3c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "ver": 4, "vid": "8001401", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.4 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:29:49", "updated_at": "2025-08-08 16:29:49", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "79431949-a5c9-4232-9666-2acb0ce78c3c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55388\",\"TCP/55394\",\"TCP/55342\",\"TCP/55347\",\"TCP/55408\",\"TCP/55352\",\"TCP/55354\",\"TCP/55356\",\"TCP/55366\",\"TCP/55375\",\"TCP/55380\",\"TCP/55398\",\"TCP/55407\",\"TCP/55377\",\"TCP/55409\",\"TCP/55346\",\"TCP/55357\",\"TCP/55379\",\"TCP/55383\",\"TCP/55397\",\"TCP/55400\",\"TCP/55403\",\"TCP/55341\",\"TCP/55361\",\"TCP/55372\",\"TCP/55373\",\"TCP/55391\",\"TCP/55395\",\"TCP/55410\",\"TCP/55360\",\"TCP/55343\",\"TCP/55362\",\"TCP/55345\",\"TCP/55386\",\"TCP/55401\",\"TCP/55411\",\"TCP/55371\",\"TCP/55378\",\"TCP/55370\",\"TCP/55374\",\"TCP/55369\",\"TCP/55402\",\"TCP/55389\",\"TCP/55390\",\"TCP/55405\",\"TCP/55350\",\"TCP/55385\",\"TCP/55344\",\"TCP/55351\",\"TCP/55363\",\"TCP/55365\",\"TCP/55382\",\"TCP/55364\",\"TCP/55387\",\"TCP/55393\",\"TCP/55396\",\"TCP/55358\",\"TCP/55406\",\"TCP/55355\",\"TCP/55359\",\"TCP/55376\",\"TCP/55349\",\"TCP/55381\",\"TCP/55392\",\"TCP/55367\",\"TCP/55399\",\"TCP/55404\",\"TCP/55368\",\"TCP/55348\",\"TCP/55353\",\"TCP/55384\"]", "pcap_library": {"file_location": "pcap_library_998000810.pcap", "filesize": "750398 bytes", "md5sum": "547c2dd3eaac8799cfa26413355f672f", "orig_file_name": "pcap_library_998000810.pcap", "packet_count": 1598}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8087\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8087", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "4f63c323-7828-4a04-a1de-9b8375fbc1d7": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "ver": 4, "vid": "8001400", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.3 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:28:21", "updated_at": "2025-08-08 16:28:21", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "4f63c323-7828-4a04-a1de-9b8375fbc1d7", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55284\",\"TCP/55286\",\"TCP/55295\",\"TCP/55301\",\"TCP/55324\",\"TCP/55326\",\"TCP/55282\",\"TCP/55308\",\"TCP/55320\",\"TCP/55277\",\"TCP/55312\",\"TCP/55279\",\"TCP/55280\",\"TCP/55316\",\"TCP/55323\",\"TCP/55276\",\"TCP/55281\",\"TCP/55307\",\"TCP/55275\",\"TCP/55293\",\"TCP/55303\",\"TCP/55335\",\"TCP/55309\",\"TCP/55315\",\"TCP/55325\",\"TCP/55290\",\"TCP/55304\",\"TCP/55310\",\"TCP/55298\",\"TCP/55317\",\"TCP/55319\",\"TCP/55328\",\"TCP/55271\",\"TCP/55283\",\"TCP/55321\",\"TCP/55330\",\"TCP/55268\",\"TCP/55272\",\"TCP/55299\",\"TCP/55331\",\"TCP/55333\",\"TCP/55287\",\"TCP/55269\",\"TCP/55296\",\"TCP/55267\",\"TCP/55285\",\"TCP/55292\",\"TCP/55329\",\"TCP/55270\",\"TCP/55273\",\"TCP/55311\",\"TCP/55314\",\"TCP/55278\",\"TCP/55289\",\"TCP/55300\",\"TCP/55327\",\"TCP/55291\",\"TCP/55274\",\"TCP/55302\",\"TCP/55306\",\"TCP/55336\",\"TCP/55294\",\"TCP/55297\",\"TCP/55305\",\"TCP/55313\",\"TCP/55318\",\"TCP/55322\",\"TCP/55288\",\"TCP/55332\",\"TCP/55334\"]", "pcap_library": {"file_location": "pcap_library_998000809.pcap", "filesize": "740164 bytes", "md5sum": "2e01234771bd0050d51ad9100de5e6e1", "orig_file_name": "pcap_library_998000809.pcap", "packet_count": 1544}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8086\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8086", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "360cebb4-b8b4-4657-9979-4ee41e896e36": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "ver": 4, "vid": "8001399", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.2 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:26:54", "updated_at": "2025-08-08 16:26:54", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "360cebb4-b8b4-4657-9979-4ee41e896e36", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55197\",\"TCP/55206\",\"TCP/55216\",\"TCP/55229\",\"TCP/55165\",\"TCP/55166\",\"TCP/55182\",\"TCP/55189\",\"TCP/55190\",\"TCP/55217\",\"TCP/55237\",\"TCP/55238\",\"TCP/55177\",\"TCP/55212\",\"TCP/55233\",\"TCP/55242\",\"TCP/55185\",\"TCP/55198\",\"TCP/55188\",\"TCP/55195\",\"TCP/55215\",\"TCP/55221\",\"TCP/55181\",\"TCP/55184\",\"TCP/55203\",\"TCP/55243\",\"TCP/55168\",\"TCP/55175\",\"TCP/55205\",\"TCP/55208\",\"TCP/55222\",\"TCP/55236\",\"TCP/55178\",\"TCP/55200\",\"TCP/55211\",\"TCP/55234\",\"TCP/55240\",\"TCP/55180\",\"TCP/55192\",\"TCP/55207\",\"TCP/55225\",\"TCP/55230\",\"TCP/55244\",\"TCP/55163\",\"TCP/55171\",\"TCP/55209\",\"TCP/55193\",\"TCP/55218\",\"TCP/55174\",\"TCP/55231\",\"TCP/55246\",\"TCP/55248\",\"TCP/55176\",\"TCP/55179\",\"TCP/55199\",\"TCP/55213\",\"TCP/55232\",\"TCP/55247\",\"TCP/55167\",\"TCP/55169\",\"TCP/55204\",\"TCP/55245\",\"TCP/55224\",\"TCP/55227\",\"TCP/55235\",\"TCP/55173\",\"TCP/55191\",\"TCP/55239\",\"TCP/55223\",\"TCP/55228\",\"TCP/55241\",\"TCP/55220\",\"TCP/55172\",\"TCP/55210\",\"TCP/55186\",\"TCP/55202\",\"TCP/55219\",\"TCP/55183\",\"TCP/55196\",\"TCP/55194\",\"TCP/55201\",\"TCP/55214\",\"TCP/55164\",\"TCP/55226\",\"TCP/55187\",\"TCP/55170\"]", "pcap_library": {"file_location": "pcap_library_998000808.pcap", "filesize": "882879 bytes", "md5sum": "8379f04682de6523ba42012cc1a77eac", "orig_file_name": "pcap_library_998000808.pcap", "packet_count": 1926}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8085\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8085", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "5dbed05e-8005-4e4c-a6fc-766d900321b7": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "ver": 4, "vid": "8001398", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.4.0 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:25:15", "updated_at": "2025-08-08 16:25:15", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "5dbed05e-8005-4e4c-a6fc-766d900321b7", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55105\",\"TCP/55112\",\"TCP/55118\",\"TCP/55119\",\"TCP/55121\",\"TCP/55126\",\"TCP/55147\",\"TCP/55081\",\"TCP/55090\",\"TCP/55107\",\"TCP/55108\",\"TCP/55155\",\"TCP/55095\",\"TCP/55127\",\"TCP/55141\",\"TCP/55154\",\"TCP/55092\",\"TCP/55094\",\"TCP/55101\",\"TCP/55106\",\"TCP/55132\",\"TCP/55139\",\"TCP/55109\",\"TCP/55113\",\"TCP/55125\",\"TCP/55084\",\"TCP/55087\",\"TCP/55097\",\"TCP/55102\",\"TCP/55128\",\"TCP/55098\",\"TCP/55131\",\"TCP/55082\",\"TCP/55116\",\"TCP/55137\",\"TCP/55120\",\"TCP/55129\",\"TCP/55130\",\"TCP/55135\",\"TCP/55096\",\"TCP/55114\",\"TCP/55122\",\"TCP/55123\",\"TCP/55144\",\"TCP/55149\",\"TCP/55088\",\"TCP/55110\",\"TCP/55148\",\"TCP/55150\",\"TCP/55152\",\"TCP/55104\",\"TCP/55111\",\"TCP/55083\",\"TCP/55089\",\"TCP/55153\",\"TCP/55156\",\"TCP/55103\",\"TCP/55138\",\"TCP/55145\",\"TCP/55086\",\"TCP/55091\",\"TCP/55099\",\"TCP/55124\",\"TCP/55134\",\"TCP/55117\",\"TCP/55100\",\"TCP/55136\",\"TCP/55140\",\"TCP/55142\",\"TCP/55115\",\"TCP/55133\",\"TCP/55143\",\"TCP/55146\",\"TCP/55151\",\"TCP/55085\"]", "pcap_library": {"file_location": "pcap_library_998000807.pcap", "filesize": "783881 bytes", "md5sum": "5bea7785df38da746f3d5ef3b2799608", "orig_file_name": "pcap_library_998000807.pcap", "packet_count": 1667}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8084\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8084", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "21326d13-be99-484e-83f3-e46ba6bb4ffb": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "ver": 4, "vid": "8001397", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.3.14 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:22:43", "updated_at": "2025-08-08 16:22:43", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "21326d13-be99-484e-83f3-e46ba6bb4ffb", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55000\",\"TCP/55012\",\"TCP/55060\",\"TCP/55008\",\"TCP/55038\",\"TCP/55046\",\"TCP/55059\",\"TCP/54981\",\"TCP/54992\",\"TCP/55036\",\"TCP/55048\",\"TCP/55064\",\"TCP/55065\",\"TCP/55034\",\"TCP/54991\",\"TCP/54999\",\"TCP/55006\",\"TCP/55029\",\"TCP/55063\",\"TCP/54984\",\"TCP/55013\",\"TCP/55021\",\"TCP/55024\",\"TCP/55025\",\"TCP/55058\",\"TCP/55066\",\"TCP/54982\",\"TCP/55002\",\"TCP/55011\",\"TCP/55026\",\"TCP/55035\",\"TCP/54996\",\"TCP/55001\",\"TCP/55022\",\"TCP/55030\",\"TCP/55050\",\"TCP/55056\",\"TCP/55067\",\"TCP/54995\",\"TCP/55044\",\"TCP/55045\",\"TCP/55055\",\"TCP/55057\",\"TCP/54997\",\"TCP/55040\",\"TCP/54985\",\"TCP/55005\",\"TCP/55009\",\"TCP/54989\",\"TCP/55014\",\"TCP/55033\",\"TCP/55047\",\"TCP/55062\",\"TCP/54988\",\"TCP/55010\",\"TCP/55015\",\"TCP/54990\",\"TCP/55004\",\"TCP/55019\",\"TCP/55020\",\"TCP/55039\",\"TCP/55041\",\"TCP/54983\",\"TCP/54994\",\"TCP/55018\",\"TCP/55023\",\"TCP/55027\",\"TCP/55028\",\"TCP/55031\",\"TCP/55051\",\"TCP/55054\",\"TCP/54987\",\"TCP/55032\",\"TCP/55037\",\"TCP/55049\",\"TCP/55068\",\"TCP/55003\",\"TCP/55007\",\"TCP/54998\",\"TCP/55052\",\"TCP/55053\",\"TCP/55042\",\"TCP/55017\",\"TCP/55043\",\"TCP/54993\",\"TCP/55016\",\"TCP/54986\",\"TCP/55061\"]", "pcap_library": {"file_location": "pcap_library_998000806.pcap", "filesize": "900631 bytes", "md5sum": "4040cedef3757e1a765bd8a4922c7d99", "orig_file_name": "pcap_library_998000806.pcap", "packet_count": 1982}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8083\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8083", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "141bc85e-972b-495d-a8be-c868873f30d7": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "ver": 4, "vid": "8001396", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.3.13 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:21:55", "updated_at": "2025-08-08 16:21:55", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "141bc85e-972b-495d-a8be-c868873f30d7", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54935\",\"TCP/54945\",\"TCP/54953\",\"TCP/54965\",\"TCP/54906\",\"TCP/54910\",\"TCP/54946\",\"TCP/54959\",\"TCP/54974\",\"TCP/54902\",\"TCP/54903\",\"TCP/54947\",\"TCP/54958\",\"TCP/54969\",\"TCP/54905\",\"TCP/54941\",\"TCP/54899\",\"TCP/54972\",\"TCP/54979\",\"TCP/54914\",\"TCP/54915\",\"TCP/54922\",\"TCP/54924\",\"TCP/54928\",\"TCP/54929\",\"TCP/54971\",\"TCP/54977\",\"TCP/54898\",\"TCP/54913\",\"TCP/54921\",\"TCP/54948\",\"TCP/54978\",\"TCP/54938\",\"TCP/54944\",\"TCP/54954\",\"TCP/54955\",\"TCP/54964\",\"TCP/54970\",\"TCP/54919\",\"TCP/54973\",\"TCP/54908\",\"TCP/54912\",\"TCP/54976\",\"TCP/54901\",\"TCP/54904\",\"TCP/54925\",\"TCP/54930\",\"TCP/54936\",\"TCP/54933\",\"TCP/54952\",\"TCP/54940\",\"TCP/54949\",\"TCP/54960\",\"TCP/54937\",\"TCP/54962\",\"TCP/54967\",\"TCP/54926\",\"TCP/54927\",\"TCP/54932\",\"TCP/54950\",\"TCP/54975\",\"TCP/54911\",\"TCP/54917\",\"TCP/54934\",\"TCP/54956\",\"TCP/54907\",\"TCP/54943\",\"TCP/54931\",\"TCP/54951\",\"TCP/54968\",\"TCP/54900\",\"TCP/54916\",\"TCP/54942\",\"TCP/54966\",\"TCP/54957\",\"TCP/54923\",\"TCP/54909\",\"TCP/54918\",\"TCP/54961\",\"TCP/54963\",\"TCP/54920\",\"TCP/54939\"]", "pcap_library": {"file_location": "pcap_library_998000805.pcap", "filesize": "847036 bytes", "md5sum": "f5aaf49ede4319e90877957e4e5c26fb", "orig_file_name": "pcap_library_998000805.pcap", "packet_count": 1846}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8082\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8082", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "03e144b0-fc08-4644-a05f-7f70c775d4e5": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "ver": 4, "vid": "8001395", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.3.12 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 16:20:12", "updated_at": "2025-08-08 16:20:12", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "03e144b0-fc08-4644-a05f-7f70c775d4e5", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54835\",\"TCP/54848\",\"TCP/54860\",\"TCP/54882\",\"TCP/54884\",\"TCP/54874\",\"TCP/54876\",\"TCP/54879\",\"TCP/54889\",\"TCP/54855\",\"TCP/54867\",\"TCP/54841\",\"TCP/54854\",\"TCP/54870\",\"TCP/54878\",\"TCP/54840\",\"TCP/54847\",\"TCP/54850\",\"TCP/54885\",\"TCP/54833\",\"TCP/54844\",\"TCP/54856\",\"TCP/54839\",\"TCP/54846\",\"TCP/54849\",\"TCP/54864\",\"TCP/54871\",\"TCP/54873\",\"TCP/54837\",\"TCP/54886\",\"TCP/54859\",\"TCP/54865\",\"TCP/54869\",\"TCP/54872\",\"TCP/54843\",\"TCP/54857\",\"TCP/54842\",\"TCP/54863\",\"TCP/54883\",\"TCP/54838\",\"TCP/54880\",\"TCP/54853\",\"TCP/54858\",\"TCP/54861\",\"TCP/54831\",\"TCP/54851\",\"TCP/54875\",\"TCP/54852\",\"TCP/54868\",\"TCP/54881\",\"TCP/54832\",\"TCP/54866\",\"TCP/54834\",\"TCP/54887\",\"TCP/54888\",\"TCP/54845\",\"TCP/54836\",\"TCP/54862\",\"TCP/54877\"]", "pcap_library": {"file_location": "pcap_library_998000804.pcap", "filesize": "648760 bytes", "md5sum": "d768064c8df5110b1ebf6a858d378d9d", "orig_file_name": "pcap_library_998000804.pcap", "packet_count": 1366}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8081\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8081", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "ver": 4, "vid": "8001394", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS，C&C通信，模拟 jQuery-c2.3.11 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-09 09:46:18", "updated_at": "2025-08-08 15:49:02", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "0cc0d0bd-ea6b-48c3-8d36-1c7fd3998430", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54764\",\"TCP/54769\",\"TCP/54817\",\"TCP/54755\",\"TCP/54766\",\"TCP/54772\",\"TCP/54798\",\"TCP/54813\",\"TCP/54823\",\"TCP/54771\",\"TCP/54810\",\"TCP/54816\",\"TCP/54818\",\"TCP/54819\",\"TCP/54752\",\"TCP/54753\",\"TCP/54773\",\"TCP/54783\",\"TCP/54795\",\"TCP/54811\",\"TCP/54822\",\"TCP/54757\",\"TCP/54780\",\"TCP/54790\",\"TCP/54820\",\"TCP/54754\",\"TCP/54792\",\"TCP/54796\",\"TCP/54806\",\"TCP/54815\",\"TCP/54759\",\"TCP/54765\",\"TCP/54770\",\"TCP/54787\",\"TCP/54794\",\"TCP/54776\",\"TCP/54788\",\"TCP/54758\",\"TCP/54762\",\"TCP/54814\",\"TCP/54825\",\"TCP/54756\",\"TCP/54767\",\"TCP/54775\",\"TCP/54801\",\"TCP/54803\",\"TCP/54761\",\"TCP/54774\",\"TCP/54785\",\"TCP/54799\",\"TCP/54777\",\"TCP/54779\",\"TCP/54800\",\"TCP/54789\",\"TCP/54821\",\"TCP/54824\",\"TCP/54778\",\"TCP/54791\",\"TCP/54797\",\"TCP/54808\",\"TCP/54804\",\"TCP/54807\",\"TCP/54809\",\"TCP/54781\",\"TCP/54784\",\"TCP/54786\",\"TCP/54812\",\"TCP/54760\",\"TCP/54793\",\"TCP/54805\",\"TCP/54802\",\"TCP/54782\",\"TCP/54763\",\"TCP/54768\"]", "pcap_library": {"file_location": "pcap_library_998000803.pcap", "filesize": "767987 bytes", "md5sum": "be31a462f6a89337c79375fbea7b3043", "orig_file_name": "pcap_library_998000803.pcap", "packet_count": 1649}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8080\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8080", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}