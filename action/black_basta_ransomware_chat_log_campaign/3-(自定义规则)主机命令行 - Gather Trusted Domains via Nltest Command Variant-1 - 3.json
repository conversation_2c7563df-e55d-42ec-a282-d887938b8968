{"action": {"name": "(自定义规则)主机命令行 - Gather Trusted Domains via Nltest Command Variant-1 - 3", "desc": "In this action, an attacker is trying to gather information on domain trusts with nltest command[black_basta_ransomware_chat_log_campaign]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c nltest /domain_trusts", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}}, "tags": [{"tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "tag_type": "os", "type": 1}, {"tag": "Windows:11", "tag_en": "Windows:11", "tag_type": "os", "type": 1}, {"tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "tag_type": "os", "type": 1}, {"tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "tag_type": "os", "type": 1}, {"tag": "Windows:10", "tag_en": "Windows:10", "tag_type": "os", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"tag_type": "mitre_techniques", "tag": "T1482 - 域信任发现", "tag_en": "T1482 - Domain Trust Discovery", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1030", "tag_en": "ATT&CK:M1030", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}], "picus_id": 4, "sigma": "", "uuid": "f3fb6ea7-29b9-4c87-86b4-d31a0c68910b"}