{"actions": {"91405ad4-095a-4e8a-92ef-5899fba8666b": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "ver": 16, "vid": "382891", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用所有配置文件设置禁用 Windows 防火墙", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略，监控可疑命令执行。中长期建议：增加特权帐号管理产品以加强对身份信息系统的监测与保护；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了netsh 命令用于禁用 Windows 防火墙的所有配置文件设置。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:40", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_mitigation", "tag": "M1022", "tag_en": "M1022", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_mitigation", "tag": "M1024", "tag_en": "M1024", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_mitigation", "tag": "M1038", "tag_en": "M1038", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_mitigation", "tag": "M1054", "tag_en": "M1054", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "mitre_techniques", "tag": "T1562 - 防御破坏", "tag_en": "T1562 - Impair Defenses", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1038", "ATT&CK:M1054", "ATT&CK:M1024", "ATT&CK:M1018", "ATT&CK:M1047", "ATT&CK:M1022"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1562"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "netsh advfirewall set allprofiles state off\r\ndir\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "mkdir C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "netsh.exe advfirewall export C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\backup.wfw", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "netsh advfirewall set allprofiles state off", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "netsh.exe advfirewall import C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\backup.wfw", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}