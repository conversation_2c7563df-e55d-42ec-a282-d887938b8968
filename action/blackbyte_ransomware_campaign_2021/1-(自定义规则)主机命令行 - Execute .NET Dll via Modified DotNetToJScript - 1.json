{"action": {"name": "(自定义规则)主机命令行 - Execute .NET Dll via Modified DotNetToJScript - 1", "desc": "In this action, a .NET Dll is executed from a JScript file by using a modified version of DotNetToJScript technique.[blackbyte_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "BlackByte.js", "file_owner": "system", "file_transfer_library": 998002290, "randomize_file": null, "file_name": "BlackByte.js"}], "steps": [{"step_order": 1, "command": "cscript.exe \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\BlackByte.js\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c timeout 5 && tasklist /svc | findstr /i notepad", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "otepad\\.exe", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1218"], "mitre_mitigation": ["ATT&CK:M1026", "ATT&CK:M1038", "ATT&CK:M1042", "ATT&CK:M1050"]}}, "picus_id": 21279, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Suspicious CLR Library Load via Scripting Applications", "ruleId": "6759", "description": "Detects the load of Common Language Runtime (CLR) DLL (clr.dll) from various scripting applications. Adversaries may reflectively load code into a process in order to conceal the execution of malicious payloads.", "releaseDate": 1663027200000, "updateDate": 1663027200000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Suspicious CLR Library Load via Scripting Applications\nstatus: experimental\ndescription: Detects the load of Common Language Runtime (CLR) DLL (clr.dll) from various scripting applications. Adversaries may reflectively load code into a process in order to conceal the execution of malicious payloads.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1620/\n    - https://attack.mitre.org/tactics/TA0005/\n    - https://thewover.github.io/Introducing-Donut/\n    - https://blog.menasec.net/2019/07/interesting-difr-traces-of-net-clr.html\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n  selection:\n    EventID: 7\n    Image|endswith:\n        - 'wscript.exe'\n        - 'cscript.exe'\n        - 'mshta.exe'\n    ImageLoaded|endswith:\n        - '\\clr.dll'\n        - '\\mscoree.dll'\n        - '\\mscorlib.dll'\n  condition: selection\nfalsepositives:\n    - Unknown\nlevel: high\ntags:\n    - attack.defense_evasion\n    - attack.t1620\n    - attack.ta0005", "logSource": {"productName": "windows", "service": "sysmon", "policies": null}, "falsePositives": ["Unknown"], "mitre": [{"name": "Reflective Code Loading", "mitreId": "T1620", "url": "https://attack.mitre.org/techniques/T1620/", "type": "technique"}]}]}, "uuid": "6d9cf5c5-a41b-4334-b4ab-02fca544878d", "sigma_device_query": ""}