{"success_conditions": [{"id": 867, "created_at": "2022-05-31T09:03:29.000Z", "updated_at": "2022-05-31T09:14:12.000Z", "deleted_at": null, "code": null, "output": "The group name could not be found.", "is_inverse": false}, {"id": 866, "created_at": "2022-05-27T07:51:24.000Z", "updated_at": "2022-05-27T11:30:25.000Z", "deleted_at": null, "code": null, "output": "mmc", "is_inverse": false}, {"id": 839, "created_at": "2022-03-18T07:24:26.000Z", "updated_at": "2022-03-18T13:40:17.000Z", "deleted_at": null, "code": 0, "output": "| Finding |", "is_inverse": false}, {"id": 815, "created_at": "2022-01-28T07:20:32.000Z", "updated_at": "2022-01-28T13:57:16.000Z", "deleted_at": null, "code": null, "output": "AmsiScanBuffer failed, HRESULT 80070015", "is_inverse": false}, {"id": 814, "created_at": "2022-01-28T07:20:32.000Z", "updated_at": "2022-01-28T13:57:16.000Z", "deleted_at": null, "code": null, "output": "Failed to detect EICAR test", "is_inverse": false}, {"id": 812, "created_at": "2022-01-21T07:26:06.000Z", "updated_at": "2022-01-21T13:05:21.000Z", "deleted_at": null, "code": 0, "output": "[+] TRUSTEDINSTALLER StopDefenderService() success!", "is_inverse": false}, {"id": 760, "created_at": "2021-11-19T10:22:15.000Z", "updated_at": "2021-11-19T13:29:40.000Z", "deleted_at": null, "code": null, "output": "[+] Successfully set EnableDCOM value", "is_inverse": false}, {"id": 3618, "created_at": "2025-03-06T12:53:12.000Z", "updated_at": "2025-03-07T07:44:12.000Z", "deleted_at": null, "code": null, "output": "virtual machine.", "is_inverse": false}, {"id": 751, "created_at": "2021-10-21T10:08:07.000Z", "updated_at": "2021-10-22T08:20:03.000Z", "deleted_at": null, "code": null, "output": "\"Windows Defender\"", "is_inverse": false}, {"id": 3617, "created_at": "2025-03-06T08:18:25.000Z", "updated_at": "2025-03-06T08:39:11.000Z", "deleted_at": null, "code": 2, "output": "", "is_inverse": false}, {"id": 748, "created_at": "2021-10-15T09:05:08.000Z", "updated_at": "2021-10-15T09:09:12.000Z", "deleted_at": null, "code": null, "output": "Failed to", "is_inverse": true}, {"id": 708, "created_at": "2021-08-24T11:20:28.000Z", "updated_at": "2021-10-27T14:58:13.000Z", "deleted_at": null, "code": null, "output": "Volume Shadow Copy", "is_inverse": false}, {"id": 698, "created_at": "2021-08-19T12:00:14.000Z", "updated_at": "2022-05-20T14:33:59.000Z", "deleted_at": null, "code": null, "output": "version", "is_inverse": false}, {"id": 686, "created_at": "2021-08-18T11:38:00.000Z", "updated_at": "2021-08-27T14:24:16.000Z", "deleted_at": null, "code": null, "output": "Chacal Framework", "is_inverse": false}, {"id": 684, "created_at": "2021-08-18T11:38:00.000Z", "updated_at": "2021-08-27T14:24:16.000Z", "deleted_at": null, "code": null, "output": "succeeded", "is_inverse": false}, {"id": 682, "created_at": "2021-08-18T11:38:00.000Z", "updated_at": "2021-08-27T14:24:16.000Z", "deleted_at": null, "code": 0, "output": "[+] Done!", "is_inverse": false}, {"id": 3616, "created_at": "2025-03-05T08:49:25.000Z", "updated_at": "2025-03-07T07:44:12.000Z", "deleted_at": null, "code": null, "output": ".log", "is_inverse": true}, {"id": 672, "created_at": "2021-06-29T14:01:40.000Z", "updated_at": "2022-05-31T11:07:50.000Z", "deleted_at": null, "code": null, "output": "", "is_inverse": false}, {"id": 671, "created_at": "2021-06-29T14:01:40.000Z", "updated_at": "2022-05-31T11:07:50.000Z", "deleted_at": null, "code": null, "output": "OperationalStatus", "is_inverse": false}, {"id": 666, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2021-08-16T08:53:51.000Z", "deleted_at": null, "code": null, "output": "Checking for User Has Local Admin Privileges", "is_inverse": false}, {"id": 663, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2021-08-16T08:53:51.000Z", "deleted_at": null, "code": null, "output": "cmd.exe ", "is_inverse": false}, {"id": 658, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2021-08-16T08:53:52.000Z", "deleted_at": null, "code": null, "output": "AV Driver is found:", "is_inverse": false}, {"id": 626, "created_at": "2021-04-21T16:03:33.000Z", "updated_at": "2021-08-16T08:53:52.000Z", "deleted_at": null, "code": null, "output": "ProcessName", "is_inverse": false}, {"id": 607, "created_at": "2021-03-09T07:04:47.000Z", "updated_at": "2021-10-27T12:51:52.000Z", "deleted_at": null, "code": null, "output": "The operation completed successfully", "is_inverse": false}, {"id": 592, "created_at": "2021-03-09T07:03:07.000Z", "updated_at": "2021-08-16T08:53:52.000Z", "deleted_at": null, "code": null, "output": "VULNERABLE", "is_inverse": false}, {"id": 575, "created_at": "2021-03-09T06:59:39.000Z", "updated_at": "2021-08-16T08:53:51.000Z", "deleted_at": null, "code": null, "output": "[*] Title:", "is_inverse": false}, {"id": 535, "created_at": "2021-03-09T06:48:38.000Z", "updated_at": "2021-08-16T08:53:52.000Z", "deleted_at": null, "code": null, "output": "ZwQuerySystemInformation", "is_inverse": false}, {"id": 533, "created_at": "2021-03-09T06:46:31.000Z", "updated_at": "2021-08-16T08:53:50.000Z", "deleted_at": null, "code": null, "output": "Stealing token from", "is_inverse": false}, {"id": 924, "created_at": "2022-09-27T07:56:23.000Z", "updated_at": "2022-09-27T11:37:09.000Z", "deleted_at": null, "code": null, "output": "-", "is_inverse": false}, {"id": 28, "created_at": "2021-01-16T09:23:55.000Z", "updated_at": "2021-01-16T09:23:55.000Z", "deleted_at": null, "code": -1, "output": "Microsoft Windows", "is_inverse": false}, {"id": 17, "created_at": "2021-01-08T15:03:06.000Z", "updated_at": "2021-10-27T14:57:12.000Z", "deleted_at": null, "code": 1, "output": "Volume Shadow Copy", "is_inverse": false}, {"id": 3, "created_at": "2021-01-05T13:53:32.000Z", "updated_at": "2022-05-24T13:39:41.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON><PERSON> is executed", "is_inverse": false}, {"id": 705, "created_at": "2021-08-24T11:20:27.000Z", "updated_at": "2022-06-20T11:34:43.000Z", "deleted_at": null, "code": null, "output": "Backup command-line tool", "is_inverse": false}, {"id": 31, "created_at": "2021-01-19T08:46:28.000Z", "updated_at": "2022-06-21T11:56:53.000Z", "deleted_at": null, "code": null, "output": "LastBootUpTime", "is_inverse": false}, {"id": 24, "created_at": "2021-01-13T14:42:44.000Z", "updated_at": "2022-06-21T11:49:02.000Z", "deleted_at": null, "code": 1, "output": "was changed", "is_inverse": false}, {"id": 23, "created_at": "2021-01-13T14:42:30.000Z", "updated_at": "2022-06-21T11:49:02.000Z", "deleted_at": null, "code": 1, "output": "No files found with the specified search criteria", "is_inverse": false}, {"id": 16, "created_at": "2021-01-07T12:19:10.000Z", "updated_at": "2022-06-21T11:37:52.000Z", "deleted_at": null, "code": 0, "output": "c:\\\\", "is_inverse": false}, {"id": 15, "created_at": "2021-01-07T12:18:47.000Z", "updated_at": "2022-06-21T11:37:52.000Z", "deleted_at": null, "code": 0, "output": "C:\\\\", "is_inverse": false}, {"id": 14, "created_at": "2021-01-07T12:18:16.000Z", "updated_at": "2022-06-21T11:37:52.000Z", "deleted_at": null, "code": 1, "output": "File Not Found", "is_inverse": false}, {"id": 10, "created_at": "2021-01-06T17:29:09.000Z", "updated_at": "2022-06-21T12:44:08.000Z", "deleted_at": null, "code": 1, "output": "Everyone", "is_inverse": false}, {"id": 9, "created_at": "2021-01-06T17:28:39.000Z", "updated_at": "2022-06-21T12:44:08.000Z", "deleted_at": null, "code": 0, "output": "Everyone", "is_inverse": false}, {"id": 542, "created_at": "2021-03-09T06:51:04.000Z", "updated_at": "2022-07-05T10:04:06.000Z", "deleted_at": null, "code": null, "output": "PROCESS NAME:", "is_inverse": false}, {"id": 824, "created_at": "2022-02-18T08:20:55.000Z", "updated_at": "2022-07-05T12:01:30.000Z", "deleted_at": null, "code": null, "output": "Initiating RECOVERY mode", "is_inverse": false}, {"id": 706, "created_at": "2021-08-24T11:20:27.000Z", "updated_at": "2022-07-04T14:54:07.000Z", "deleted_at": null, "code": null, "output": "Starting Dynamic Signature removal.", "is_inverse": false}, {"id": 869, "created_at": "2022-06-08T07:18:26.000Z", "updated_at": "2022-07-07T06:21:29.000Z", "deleted_at": null, "code": null, "output": "The LaZagne Project", "is_inverse": false}, {"id": 803, "created_at": "2021-12-21T08:45:49.000Z", "updated_at": "2022-07-19T05:11:19.000Z", "deleted_at": null, "code": null, "output": "system.save", "is_inverse": false}, {"id": 802, "created_at": "2021-12-21T08:45:49.000Z", "updated_at": "2022-07-19T05:11:19.000Z", "deleted_at": null, "code": null, "output": "security.save", "is_inverse": false}, {"id": 801, "created_at": "2021-12-21T08:45:49.000Z", "updated_at": "2022-07-19T05:11:19.000Z", "deleted_at": null, "code": null, "output": "sam.save", "is_inverse": false}, {"id": 709, "created_at": "2021-08-24T11:20:28.000Z", "updated_at": "2022-07-18T13:30:37.000Z", "deleted_at": null, "code": null, "output": "Directory:", "is_inverse": false}, {"id": 707, "created_at": "2021-08-24T11:20:27.000Z", "updated_at": "2022-07-19T05:14:48.000Z", "deleted_at": null, "code": null, "output": "Microsoft JScript", "is_inverse": false}, {"id": 632, "created_at": "2021-06-29T14:01:36.000Z", "updated_at": "2022-07-18T10:13:43.000Z", "deleted_at": null, "code": null, "output": "notepad", "is_inverse": false}, {"id": 613, "created_at": "2021-03-09T07:05:55.000Z", "updated_at": "2022-07-19T05:11:19.000Z", "deleted_at": null, "code": null, "output": "[*] AS-REP hash:", "is_inverse": false}, {"id": 40, "created_at": "2021-02-09T12:25:27.000Z", "updated_at": "2022-07-17T17:08:39.000Z", "deleted_at": null, "code": null, "output": "[!] DCSync returned an error. Do you have permissions?", "is_inverse": true}, {"id": 38, "created_at": "2021-01-21T11:30:47.000Z", "updated_at": "2022-07-17T15:48:51.000Z", "deleted_at": null, "code": null, "output": "This application is dummy console, please wait one second.", "is_inverse": false}, {"id": 35, "created_at": "2021-01-20T12:59:05.000Z", "updated_at": "2022-07-17T15:44:53.000Z", "deleted_at": null, "code": 0, "output": "Volume Shadow Copy", "is_inverse": false}, {"id": 34, "created_at": "2021-01-20T12:58:50.000Z", "updated_at": "2022-07-17T15:44:53.000Z", "deleted_at": null, "code": 1, "output": "Volume Shadow Copy", "is_inverse": false}, {"id": 26, "created_at": "2021-01-15T10:49:02.000Z", "updated_at": "2022-07-17T17:08:39.000Z", "deleted_at": null, "code": null, "output": "Listen OK!", "is_inverse": false}, {"id": 25, "created_at": "2021-01-15T10:33:00.000Z", "updated_at": "2022-07-17T17:08:39.000Z", "deleted_at": null, "code": null, "output": "UserName", "is_inverse": false}, {"id": 19, "created_at": "2021-01-11T15:04:44.000Z", "updated_at": "2022-07-17T17:31:43.000Z", "deleted_at": null, "code": null, "output": "sekurlsa::logonpasswords", "is_inverse": false}, {"id": 544, "created_at": "2021-03-09T06:51:16.000Z", "updated_at": "2022-10-13T07:11:26.000Z", "deleted_at": null, "code": null, "output": "MAC", "is_inverse": false}, {"id": 492, "created_at": "2021-03-09T06:30:36.000Z", "updated_at": "2022-08-18T08:02:57.000Z", "deleted_at": null, "code": null, "output": "C:\\", "is_inverse": false}, {"id": 491, "created_at": "2021-03-09T06:30:23.000Z", "updated_at": "2022-08-18T08:04:48.000Z", "deleted_at": null, "code": 1, "output": "C:\\", "is_inverse": false}, {"id": 633, "created_at": "2021-06-29T14:01:36.000Z", "updated_at": "2022-10-06T12:57:50.000Z", "deleted_at": null, "code": 0, "output": "Done", "is_inverse": false}, {"id": 697, "created_at": "2021-08-19T11:51:51.000Z", "updated_at": "2023-03-28T06:44:34.000Z", "deleted_at": null, "code": null, "output": "Caption", "is_inverse": false}, {"id": 805, "created_at": "2021-12-30T20:08:48.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "[+] Done <PERSON>hooking", "is_inverse": false}, {"id": 724, "created_at": "2021-09-30T09:32:39.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": 0, "output": "Hello from main!", "is_inverse": false}, {"id": 665, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "No items found that satisfy the query.", "is_inverse": false}, {"id": 664, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "DeviceIoControl() returned 0", "is_inverse": false}, {"id": 657, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "PID:", "is_inverse": false}, {"id": 637, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "SignedSharpDirLister.exe", "is_inverse": false}, {"id": 21, "created_at": "2021-01-11T15:14:35.000Z", "updated_at": "2024-12-03T12:24:36.000Z", "deleted_at": null, "code": null, "output": "Starting", "is_inverse": false}, {"id": 883, "created_at": "2022-08-14T10:30:17.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "Completed", "is_inverse": false}, {"id": 885, "created_at": "2022-08-19T12:35:18.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "ALL_SUCCESS", "is_inverse": false}, {"id": 577, "created_at": "2021-03-09T07:00:10.000Z", "updated_at": "2025-02-17T06:41:27.000Z", "deleted_at": null, "code": null, "output": "Blocked", "is_inverse": false}, {"id": 753, "created_at": "2021-10-21T10:08:08.000Z", "updated_at": "2025-02-26T13:15:27.000Z", "deleted_at": null, "code": 1, "output": "", "is_inverse": true}, {"id": 690, "created_at": "2021-08-18T11:38:01.000Z", "updated_at": "2025-02-26T13:19:36.000Z", "deleted_at": null, "code": 0, "output": "explorer.exe | PID:", "is_inverse": false}, {"id": 806, "created_at": "2022-01-07T07:34:24.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": 0, "output": "[!] Content :", "is_inverse": false}, {"id": 794, "created_at": "2021-12-10T09:20:01.000Z", "updated_at": "2025-02-28T13:10:51.000Z", "deleted_at": null, "code": null, "output": "\"c:\\", "is_inverse": false}, {"id": 687, "created_at": "2021-08-18T11:38:01.000Z", "updated_at": "2025-02-28T11:21:58.000Z", "deleted_at": null, "code": 0, "output": "1", "is_inverse": false}, {"id": 859, "created_at": "2022-04-08T19:15:15.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "ComputerName", "is_inverse": false}, {"id": 835, "created_at": "2022-03-11T07:28:21.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": 0, "output": "UserName", "is_inverse": false}, {"id": 827, "created_at": "2022-02-25T07:58:23.000Z", "updated_at": "2025-04-08T14:51:05.000Z", "deleted_at": null, "code": null, "output": "[*] Both Completed!", "is_inverse": false}, {"id": 865, "created_at": "2022-05-13T11:36:02.000Z", "updated_at": "2025-05-23T07:12:47.000Z", "deleted_at": null, "code": null, "output": "ALL_SUCCESS", "is_inverse": false}, {"id": 864, "created_at": "2022-05-13T11:36:02.000Z", "updated_at": "2025-05-23T07:12:47.000Z", "deleted_at": null, "code": null, "output": "ALL_ERROR", "is_inverse": true}, {"id": 872, "created_at": "2022-07-02T20:09:18.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "ALL_DONE", "is_inverse": false}, {"id": 863, "created_at": "2022-04-29T10:46:16.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "Risk level = 0 (File is clean)", "is_inverse": false}, {"id": 836, "created_at": "2022-03-11T07:28:21.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "[+] Patched!", "is_inverse": false}, {"id": 746, "created_at": "2021-10-14T11:22:45.000Z", "updated_at": "2025-06-12T11:16:13.000Z", "deleted_at": null, "code": null, "output": "First Byte After Patching: 0xc3", "is_inverse": false}, {"id": 868, "created_at": "2022-06-02T12:59:53.000Z", "updated_at": "2025-06-24T15:34:56.000Z", "deleted_at": null, "code": null, "output": "Currently stored credentials:", "is_inverse": false}, {"id": 515, "created_at": "2021-03-09T06:39:28.000Z", "updated_at": "2025-06-24T15:34:56.000Z", "deleted_at": null, "code": null, "output": "NONE", "is_inverse": true}, {"id": 829, "created_at": "2022-03-04T06:48:36.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "resumed thread successfully.", "is_inverse": false}, {"id": 740, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "Finding", "is_inverse": false}, {"id": 739, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "Looking", "is_inverse": false}, {"id": 738, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "Fetching", "is_inverse": false}, {"id": 737, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "Non", "is_inverse": false}, {"id": 629, "created_at": "2021-06-29T14:01:35.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "=== Basic OS Information ===", "is_inverse": false}, {"id": 621, "created_at": "2021-03-09T07:07:29.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": " EDR Checks Complete", "is_inverse": false}, {"id": 619, "created_at": "2021-03-09T07:07:24.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "successfully deleted self from disk", "is_inverse": false}, {"id": 603, "created_at": "2021-03-09T07:04:26.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "Operation : Installation", "is_inverse": false}, {"id": 591, "created_at": "2021-03-09T07:03:03.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "LsaPid", "is_inverse": false}, {"id": 590, "created_at": "2021-03-09T07:03:00.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "Successfully retrieved", "is_inverse": false}, {"id": 580, "created_at": "2021-03-09T07:01:48.000Z", "updated_at": "2022-10-10T11:24:29.000Z", "deleted_at": null, "code": null, "output": "NPPSpy", "is_inverse": false}, {"id": 566, "created_at": "2021-03-09T06:57:11.000Z", "updated_at": "2022-10-13T07:11:26.000Z", "deleted_at": null, "code": null, "output": "PROXY CHECKER", "is_inverse": false}, {"id": 553, "created_at": "2021-03-09T06:52:33.000Z", "updated_at": "2022-10-13T07:11:26.000Z", "deleted_at": null, "code": null, "output": "Microsoft Windows", "is_inverse": false}, {"id": 541, "created_at": "2021-03-09T06:50:44.000Z", "updated_at": "2022-10-13T07:11:26.000Z", "deleted_at": null, "code": null, "output": "Picus test", "is_inverse": false}, {"id": 540, "created_at": "2021-03-09T06:50:41.000Z", "updated_at": "2022-10-13T07:11:26.000Z", "deleted_at": null, "code": null, "output": "EDR Checks Complete", "is_inverse": false}, {"id": 504, "created_at": "2021-03-09T06:36:04.000Z", "updated_at": "2022-10-17T12:03:03.000Z", "deleted_at": null, "code": 1, "output": "unable to find", "is_inverse": false}, {"id": 496, "created_at": "2021-03-09T06:31:53.000Z", "updated_at": "2022-10-17T12:03:03.000Z", "deleted_at": null, "code": 1, "output": "SESSIONNAME", "is_inverse": false}, {"id": 495, "created_at": "2021-03-09T06:31:51.000Z", "updated_at": "2022-10-17T12:03:03.000Z", "deleted_at": null, "code": 1, "output": "USERNAME", "is_inverse": false}, {"id": 757, "created_at": "2021-11-12T06:55:35.000Z", "updated_at": "2022-10-18T14:16:04.000Z", "deleted_at": null, "code": 0, "output": "<PERSON><PERSON><PERSON><PERSON>", "is_inverse": false}, {"id": 670, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2022-10-18T14:16:10.000Z", "deleted_at": null, "code": null, "output": "transacted_hollowing.txt", "is_inverse": false}, {"id": 622, "created_at": "2021-03-09T07:07:56.000Z", "updated_at": "2022-10-18T14:16:10.000Z", "deleted_at": null, "code": null, "output": "Shellcode injection completed successfully!", "is_inverse": false}, {"id": 616, "created_at": "2021-03-09T07:06:38.000Z", "updated_at": "2022-10-18T19:43:15.000Z", "deleted_at": null, "code": null, "output": "System.IO.MemoryMappedFiles.MemoryMappedFile", "is_inverse": false}, {"id": 558, "created_at": "2021-03-09T06:55:06.000Z", "updated_at": "2022-10-18T14:16:10.000Z", "deleted_at": null, "code": 0, "output": "Calculator.exe", "is_inverse": false}, {"id": 623, "created_at": "2021-04-18T16:51:26.000Z", "updated_at": "2022-10-27T12:43:23.000Z", "deleted_at": null, "code": null, "output": "ERROR", "is_inverse": true}, {"id": 930, "created_at": "2022-10-07T14:16:02.000Z", "updated_at": "2022-11-01T06:34:15.000Z", "deleted_at": null, "code": null, "output": "nslookup", "is_inverse": false}, {"id": 927, "created_at": "2022-10-03T06:18:08.000Z", "updated_at": "2022-11-01T06:34:15.000Z", "deleted_at": null, "code": null, "output": "Default Server", "is_inverse": false}, {"id": 939, "created_at": "2022-11-01T10:39:00.000Z", "updated_at": "2022-11-01T10:39:00.000Z", "deleted_at": null, "code": null, "output": "STOPPABLE", "is_inverse": false}, {"id": 571, "created_at": "2021-03-09T06:58:33.000Z", "updated_at": "2022-11-03T13:38:18.000Z", "deleted_at": null, "code": null, "output": "Created User", "is_inverse": false}, {"id": 564, "created_at": "2021-03-09T06:56:51.000Z", "updated_at": "2022-11-03T13:38:18.000Z", "deleted_at": null, "code": 0, "output": "DLL succesfully created", "is_inverse": false}, {"id": 545, "created_at": "2021-03-09T06:51:25.000Z", "updated_at": "2022-11-04T08:34:51.000Z", "deleted_at": null, "code": null, "output": "Vulnerable driver file removed", "is_inverse": false}, {"id": 501, "created_at": "2021-03-09T06:32:46.000Z", "updated_at": "2022-11-04T14:18:18.000Z", "deleted_at": null, "code": 2, "output": "", "is_inverse": false}, {"id": 937, "created_at": "2022-10-31T05:22:25.000Z", "updated_at": "2022-11-11T15:08:14.000Z", "deleted_at": null, "code": null, "output": "AmsiUtils", "is_inverse": false}, {"id": 880, "created_at": "2022-07-17T16:10:18.000Z", "updated_at": "2022-11-24T15:02:05.000Z", "deleted_at": null, "code": null, "output": "Invalid namespace", "is_inverse": true}, {"id": 532, "created_at": "2021-03-09T06:45:45.000Z", "updated_at": "2022-12-06T01:04:03.000Z", "deleted_at": null, "code": null, "output": "ExclusionPath", "is_inverse": false}, {"id": 1002, "created_at": "2022-12-22T13:18:17.000Z", "updated_at": "2022-12-22T13:18:17.000Z", "deleted_at": null, "code": null, "output": "Yes", "is_inverse": false}, {"id": 3615, "created_at": "2025-03-05T08:13:03.000Z", "updated_at": "2025-03-07T07:44:12.000Z", "deleted_at": null, "code": null, "output": "Hello <PERSON>cus!", "is_inverse": false}, {"id": 3614, "created_at": "2025-03-05T07:58:30.000Z", "updated_at": "2025-03-07T07:44:12.000Z", "deleted_at": null, "code": null, "output": "Install", "is_inverse": false}, {"id": 678, "created_at": "2021-08-18T11:37:59.000Z", "updated_at": "2025-03-06T07:49:29.000Z", "deleted_at": null, "code": 0, "output": "[+] Done...", "is_inverse": false}, {"id": 669, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2025-03-11T11:55:26.000Z", "deleted_at": null, "code": 0, "output": "[$] process hollowing:", "is_inverse": false}, {"id": 500, "created_at": "2021-03-09T06:32:20.000Z", "updated_at": "2023-05-10T19:32:27.000Z", "deleted_at": null, "code": 0, "output": "Volume Shadow Copy", "is_inverse": false}, {"id": 667, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2025-03-06T07:49:00.000Z", "deleted_at": null, "code": null, "output": "Eventlog nuked successfully!", "is_inverse": false}, {"id": 661, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2025-03-06T07:48:46.000Z", "deleted_at": null, "code": null, "output": "[*] Completed collection", "is_inverse": false}, {"id": 655, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2025-03-04T12:55:30.000Z", "deleted_at": null, "code": null, "output": "DisplayName", "is_inverse": false}, {"id": 647, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2025-03-04T12:54:29.000Z", "deleted_at": null, "code": null, "output": "C:\\Windows\\System32\\notepad.exe", "is_inverse": false}, {"id": 1032, "created_at": "2023-01-11T14:05:27.000Z", "updated_at": "2023-01-11T14:05:27.000Z", "deleted_at": null, "code": null, "output": "minersim", "is_inverse": false}, {"id": 1044, "created_at": "2023-01-24T15:26:48.000Z", "updated_at": "2023-01-24T15:26:48.000Z", "deleted_at": null, "code": null, "output": "Can't Find Specific MAC address. Founded MAC Address is:", "is_inverse": false}, {"id": 1043, "created_at": "2023-01-24T13:21:20.000Z", "updated_at": "2023-01-24T15:28:38.000Z", "deleted_at": null, "code": null, "output": "c8:27:cc:c2:37:5a", "is_inverse": true}, {"id": 1047, "created_at": "2023-01-26T15:39:45.000Z", "updated_at": "2023-01-26T15:39:45.000Z", "deleted_at": null, "code": null, "output": "ERROR kuhl_m_lsadump_dcsync", "is_inverse": true}, {"id": 874, "created_at": "2022-07-06T13:27:37.000Z", "updated_at": "2023-01-30T12:33:49.000Z", "deleted_at": null, "code": null, "output": "Exception calling \"ReadAllBytes\"", "is_inverse": true}, {"id": 1049, "created_at": "2023-02-01T10:55:41.000Z", "updated_at": "2023-02-01T11:29:02.000Z", "deleted_at": null, "code": null, "output": "ssh: cannot access 'root@127.0.0.1': No such file or directory", "is_inverse": false}, {"id": 518, "created_at": "2021-03-09T06:41:45.000Z", "updated_at": "2023-02-03T09:22:40.000Z", "deleted_at": null, "code": 0, "output": "copied", "is_inverse": false}, {"id": 1060, "created_at": "2023-02-07T13:28:55.000Z", "updated_at": "2023-02-07T13:28:55.000Z", "deleted_at": null, "code": null, "output": "PicusSamH", "is_inverse": true}, {"id": 644, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2025-03-04T12:52:45.000Z", "deleted_at": null, "code": null, "output": "notmimi 2.2.0 (x64)", "is_inverse": false}, {"id": 640, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2025-03-04T12:52:22.000Z", "deleted_at": null, "code": null, "output": "[*] Calling the Callback Function ...", "is_inverse": false}, {"id": 1053, "created_at": "2023-02-07T08:28:56.000Z", "updated_at": "2023-02-07T08:28:56.000Z", "deleted_at": null, "code": null, "output": "ADMIN$", "is_inverse": false}, {"id": 1065, "created_at": "2023-02-13T06:05:37.000Z", "updated_at": "2023-02-13T06:12:00.000Z", "deleted_at": null, "code": null, "output": "mimikatz 2.2.0", "is_inverse": false}, {"id": 1062, "created_at": "2023-02-10T07:38:45.000Z", "updated_at": "2023-02-21T13:52:27.000Z", "deleted_at": null, "code": null, "output": ".png", "is_inverse": false}, {"id": 547, "created_at": "2021-03-09T06:51:35.000Z", "updated_at": "2025-03-11T15:04:46.000Z", "deleted_at": null, "code": null, "output": "Testing S", "is_inverse": false}, {"id": 1073, "created_at": "2023-02-24T09:43:03.000Z", "updated_at": "2023-02-27T09:26:46.000Z", "deleted_at": null, "code": null, "output": "password:", "is_inverse": false}, {"id": 1092, "created_at": "2023-02-28T10:44:12.000Z", "updated_at": "2023-02-28T10:44:12.000Z", "deleted_at": null, "code": null, "output": "Failed to disable UFW", "is_inverse": true}, {"id": 1075, "created_at": "2023-02-27T06:40:47.000Z", "updated_at": "2024-10-01T07:49:40.000Z", "deleted_at": null, "code": null, "output": "QLite", "is_inverse": false}, {"id": 1091, "created_at": "2023-02-28T10:43:45.000Z", "updated_at": "2023-02-28T10:43:45.000Z", "deleted_at": null, "code": null, "output": "Failed to stop I<PERSON><PERSON>", "is_inverse": true}, {"id": 1009, "created_at": "2022-12-23T16:40:59.000Z", "updated_at": "2025-03-18T08:14:43.000Z", "deleted_at": null, "code": null, "output": "[*] Process Name", "is_inverse": false}, {"id": 1922, "created_at": "2023-08-21T14:45:45.000Z", "updated_at": "2023-08-22T08:28:38.000Z", "deleted_at": null, "code": null, "output": "IPv4", "is_inverse": false}, {"id": 516, "created_at": "2021-03-09T06:39:38.000Z", "updated_at": "2023-02-28T10:46:41.000Z", "deleted_at": null, "code": 0, "output": "Privilege '20' OK", "is_inverse": false}, {"id": 945, "created_at": "2022-11-11T12:32:51.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "Finished at", "is_inverse": false}, {"id": 1110, "created_at": "2023-03-21T14:40:34.000Z", "updated_at": "2023-03-21T14:40:34.000Z", "deleted_at": null, "code": null, "output": "csrss", "is_inverse": false}, {"id": 1106, "created_at": "2023-03-21T13:52:47.000Z", "updated_at": "2023-03-21T13:52:47.000Z", "deleted_at": null, "code": null, "output": "//", "is_inverse": false}, {"id": 1006, "created_at": "2022-12-23T12:31:17.000Z", "updated_at": "2024-11-25T07:15:34.000Z", "deleted_at": null, "code": null, "output": "Hooked", "is_inverse": false}, {"id": 1072, "created_at": "2023-02-23T09:28:04.000Z", "updated_at": "2024-12-04T10:36:19.000Z", "deleted_at": null, "code": null, "output": "Sample Text", "is_inverse": false}, {"id": 1889, "created_at": "2023-08-18T12:14:05.000Z", "updated_at": "2025-01-02T07:05:21.000Z", "deleted_at": null, "code": null, "output": "console", "is_inverse": false}, {"id": 950, "created_at": "2022-11-12T20:50:13.000Z", "updated_at": "2025-02-28T13:10:51.000Z", "deleted_at": null, "code": null, "output": "Successfully enumerated", "is_inverse": false}, {"id": 943, "created_at": "2022-11-07T13:24:52.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "No More Shadow Copies Were Detected", "is_inverse": false}, {"id": 804, "created_at": "2021-12-24T08:09:24.000Z", "updated_at": "2025-02-26T13:46:47.000Z", "deleted_at": null, "code": null, "output": "[+] Search done.", "is_inverse": false}, {"id": 1000003, "created_at": "2025-07-04T06:42:32.666Z", "updated_at": "2025-07-04T06:42:32.666Z", "deleted_at": null, "code": 0, "output": "456", "is_inverse": false}, {"id": 816, "created_at": "2022-02-04T07:39:31.000Z", "updated_at": "2025-04-08T14:51:06.000Z", "deleted_at": null, "code": 0, "output": "[+] All done.", "is_inverse": false}, {"id": 992, "created_at": "2022-12-12T14:13:30.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "Breaking at", "is_inverse": false}, {"id": 1105, "created_at": "2023-03-21T13:37:52.000Z", "updated_at": "2025-06-04T12:56:36.000Z", "deleted_at": null, "code": null, "output": "TRUE", "is_inverse": false}, {"id": 1039, "created_at": "2023-01-16T06:02:47.000Z", "updated_at": "2025-06-04T11:48:34.000Z", "deleted_at": null, "code": null, "output": "[+] Finished", "is_inverse": false}, {"id": 1008, "created_at": "2022-12-23T16:40:59.000Z", "updated_at": "2025-06-04T12:36:23.000Z", "deleted_at": null, "code": null, "output": "[+] Success count: 5", "is_inverse": false}, {"id": 1007, "created_at": "2022-12-23T16:40:59.000Z", "updated_at": "2025-06-04T12:43:07.000Z", "deleted_at": null, "code": null, "output": "[+] Succesfully Mirrored to new PID", "is_inverse": false}, {"id": 858, "created_at": "2022-04-08T19:15:15.000Z", "updated_at": "2025-06-12T11:13:08.000Z", "deleted_at": null, "code": null, "output": "[injectCreateRemoteThread] Injected", "is_inverse": false}, {"id": 826, "created_at": "2022-02-25T07:58:22.000Z", "updated_at": "2025-06-12T11:13:08.000Z", "deleted_at": null, "code": 0, "output": "Successfully executed example shellcode using VEH! Result = 3", "is_inverse": false}, {"id": 795, "created_at": "2021-12-10T09:20:02.000Z", "updated_at": "2025-06-12T11:16:13.000Z", "deleted_at": null, "code": null, "output": "[+] Verified!", "is_inverse": false}, {"id": 634, "created_at": "2021-06-29T14:01:36.000Z", "updated_at": "2025-06-12T11:16:13.000Z", "deleted_at": null, "code": 0, "output": "is SUCCEEDED", "is_inverse": false}, {"id": 614, "created_at": "2021-03-09T07:06:05.000Z", "updated_at": "2025-06-12T11:02:56.000Z", "deleted_at": null, "code": null, "output": "+ Successfully mapped an image to hollow at", "is_inverse": false}, {"id": 573, "created_at": "2021-03-09T06:59:16.000Z", "updated_at": "2025-06-12T11:16:13.000Z", "deleted_at": null, "code": -532462766, "output": "Hello From Golang", "is_inverse": false}, {"id": 556, "created_at": "2021-03-09T06:54:04.000Z", "updated_at": "2025-06-12T11:14:30.000Z", "deleted_at": null, "code": 0, "output": "Executable returned code 0", "is_inverse": false}, {"id": 555, "created_at": "2021-03-09T06:54:04.000Z", "updated_at": "2025-06-12T11:14:30.000Z", "deleted_at": null, "code": 0, "output": "Executable entrypoint found", "is_inverse": false}, {"id": 514, "created_at": "2021-03-09T06:38:55.000Z", "updated_at": "2025-06-16T12:39:14.000Z", "deleted_at": null, "code": 1056, "output": "", "is_inverse": false}, {"id": 4021, "created_at": "2025-06-25T12:21:08.000Z", "updated_at": "2025-06-25T12:21:08.000Z", "deleted_at": null, "code": null, "output": "hello picus", "is_inverse": false}, {"id": 4020, "created_at": "2025-06-24T12:34:29.000Z", "updated_at": "2025-06-24T15:34:56.000Z", "deleted_at": null, "code": 1, "output": "Error", "is_inverse": false}, {"id": 954, "created_at": "2022-11-28T06:14:20.000Z", "updated_at": "2025-06-26T08:26:06.000Z", "deleted_at": null, "code": null, "output": "[*] Done.", "is_inverse": false}, {"id": 750, "created_at": "2021-10-21T10:08:07.000Z", "updated_at": "2025-06-26T08:27:12.000Z", "deleted_at": null, "code": null, "output": "Pid", "is_inverse": false}, {"id": 799, "created_at": "2021-12-17T10:17:04.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "[*] UserName", "is_inverse": false}, {"id": 631, "created_at": "2021-06-29T14:01:35.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "not<PERSON><PERSON>(commandline) #", "is_inverse": false}, {"id": 594, "created_at": "2021-03-09T07:03:26.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "can bypass", "is_inverse": false}, {"id": 584, "created_at": "2021-03-09T07:02:20.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "[*] Token Privileges:", "is_inverse": false}, {"id": 582, "created_at": "2021-03-09T07:02:08.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "whoami", "is_inverse": false}, {"id": 538, "created_at": "2021-03-09T06:50:12.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 1063, "output": "", "is_inverse": false}, {"id": 506, "created_at": "2021-03-09T06:36:35.000Z", "updated_at": "2025-07-16T11:01:07.000Z", "deleted_at": null, "code": 1, "output": "0 match(es) found", "is_inverse": false}, {"id": 505, "created_at": "2021-03-09T06:36:32.000Z", "updated_at": "2025-07-16T11:00:48.000Z", "deleted_at": null, "code": 1, "output": "0 match(es) found.", "is_inverse": false}, {"id": 1108, "created_at": "2023-03-21T14:16:07.000Z", "updated_at": "2023-03-29T14:04:53.000Z", "deleted_at": null, "code": null, "output": "-----------------", "is_inverse": false}, {"id": 1, "created_at": "2020-12-15T13:07:59.000Z", "updated_at": "2023-03-29T14:04:53.000Z", "deleted_at": null, "code": 0, "output": "The command completed successfully", "is_inverse": false}, {"id": 1117, "created_at": "2023-04-03T06:32:07.000Z", "updated_at": "2023-04-03T06:52:10.000Z", "deleted_at": null, "code": null, "output": "blenny", "is_inverse": false}, {"id": 11, "created_at": "2021-01-06T17:35:19.000Z", "updated_at": "2025-03-05T06:46:46.000Z", "deleted_at": null, "code": null, "output": "[*] <PERSON><PERSON> is completed!", "is_inverse": false}, {"id": 1129, "created_at": "2023-04-10T08:38:20.000Z", "updated_at": "2023-04-10T10:56:15.000Z", "deleted_at": null, "code": null, "output": "Getting global account policies", "is_inverse": false}, {"id": 1128, "created_at": "2023-04-10T08:21:33.000Z", "updated_at": "2023-04-10T11:01:54.000Z", "deleted_at": null, "code": null, "output": "List of Share Points", "is_inverse": false}, {"id": 1131, "created_at": "2023-04-18T14:10:55.000Z", "updated_at": "2023-04-18T14:10:55.000Z", "deleted_at": null, "code": null, "output": "Domain                     :", "is_inverse": false}, {"id": 1156, "created_at": "2023-04-30T19:09:09.000Z", "updated_at": "2023-05-02T08:45:50.000Z", "deleted_at": null, "code": null, "output": "PING ", "is_inverse": false}, {"id": 1155, "created_at": "2023-04-30T19:08:12.000Z", "updated_at": "2023-05-02T08:45:53.000Z", "deleted_at": null, "code": null, "output": "Active Internet connections", "is_inverse": false}, {"id": 888, "created_at": "2022-08-29T10:00:01.000Z", "updated_at": "2023-05-09T12:36:32.000Z", "deleted_at": null, "code": 0, "output": "lsass", "is_inverse": false}, {"id": 675, "created_at": "2021-07-08T08:58:56.000Z", "updated_at": "2023-05-09T12:21:20.000Z", "deleted_at": null, "code": 0, "output": "[*] All done. GetLastError: 0", "is_inverse": false}, {"id": 653, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2023-05-10T13:46:02.000Z", "deleted_at": null, "code": null, "output": "notepad.exe", "is_inverse": false}, {"id": 33, "created_at": "2021-01-20T09:45:26.000Z", "updated_at": "2023-05-10T14:14:33.000Z", "deleted_at": null, "code": 1, "output": ":", "is_inverse": false}, {"id": 32, "created_at": "2021-01-20T09:45:21.000Z", "updated_at": "2023-05-10T14:14:33.000Z", "deleted_at": null, "code": 0, "output": ":", "is_inverse": false}, {"id": 951, "created_at": "2022-11-13T00:50:38.000Z", "updated_at": "2023-05-11T19:20:40.000Z", "deleted_at": null, "code": null, "output": "un-registered successfully", "is_inverse": false}, {"id": 819, "created_at": "2022-02-04T07:39:31.000Z", "updated_at": "2023-05-10T19:13:44.000Z", "deleted_at": null, "code": 0, "output": "Reason: Access is denied.", "is_inverse": false}, {"id": 758, "created_at": "2021-11-12T06:55:35.000Z", "updated_at": "2023-05-11T20:21:22.000Z", "deleted_at": null, "code": null, "output": "arbitraryFile.txt", "is_inverse": false}, {"id": 499, "created_at": "2021-03-09T06:32:19.000Z", "updated_at": "2023-05-10T19:32:27.000Z", "deleted_at": null, "code": 1, "output": "Volume Shadow Copy", "is_inverse": false}, {"id": 36, "created_at": "2021-01-21T10:17:14.000Z", "updated_at": "2023-05-10T17:16:00.000Z", "deleted_at": null, "code": null, "output": "RUNNING", "is_inverse": false}, {"id": 6, "created_at": "2021-01-06T15:55:06.000Z", "updated_at": "2023-05-10T19:09:27.000Z", "deleted_at": null, "code": 1, "output": "No Instance", "is_inverse": false}, {"id": 1255, "created_at": "2023-05-12T12:19:52.000Z", "updated_at": "2023-05-12T12:39:42.000Z", "deleted_at": null, "code": null, "output": "Jan  1", "is_inverse": false}, {"id": 610, "created_at": "2021-03-09T07:05:25.000Z", "updated_at": "2023-05-12T13:55:27.000Z", "deleted_at": null, "code": null, "output": "[?] Found 0 object(s)", "is_inverse": true}, {"id": 525, "created_at": "2021-03-09T06:44:39.000Z", "updated_at": "2023-05-12T08:18:09.000Z", "deleted_at": null, "code": 128, "output": "", "is_inverse": false}, {"id": 1289, "created_at": "2023-05-17T08:59:32.000Z", "updated_at": "2023-05-17T08:59:32.000Z", "deleted_at": null, "code": null, "output": "HKEY_LOCAL_MACHINE\\SYSTEM\\", "is_inverse": false}, {"id": 1288, "created_at": "2023-05-17T08:40:48.000Z", "updated_at": "2023-05-17T08:40:48.000Z", "deleted_at": null, "code": null, "output": "Hive: HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run", "is_inverse": false}, {"id": 1320, "created_at": "2023-05-22T10:32:39.000Z", "updated_at": "2023-05-22T10:32:52.000Z", "deleted_at": null, "code": null, "output": "passtest", "is_inverse": false}, {"id": 1335, "created_at": "2023-05-24T10:24:39.000Z", "updated_at": "2023-05-24T10:24:50.000Z", "deleted_at": null, "code": null, "output": "AppCert.dll", "is_inverse": false}, {"id": 1333, "created_at": "2023-05-24T10:22:26.000Z", "updated_at": "2023-05-24T10:23:01.000Z", "deleted_at": null, "code": null, "output": "README_TO_DECRYPT.html", "is_inverse": false}, {"id": 1329, "created_at": "2023-05-24T10:19:26.000Z", "updated_at": "2023-05-24T10:19:35.000Z", "deleted_at": null, "code": null, "output": "ccleaner_v5.33.exe", "is_inverse": false}, {"id": 1326, "created_at": "2023-05-22T13:06:49.000Z", "updated_at": "2023-05-24T09:53:58.000Z", "deleted_at": null, "code": null, "output": "hafnium.aspx", "is_inverse": false}, {"id": 652, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2023-05-24T09:36:17.000Z", "deleted_at": null, "code": null, "output": "Handles  NPM(K)    PM(K)      WS(K)     CPU(s)     Id  SI ProcessName", "is_inverse": false}, {"id": 1339, "created_at": "2023-05-26T10:56:41.000Z", "updated_at": "2023-05-26T10:57:23.000Z", "deleted_at": null, "code": null, "output": "=== Running System Triage Checks ===", "is_inverse": false}, {"id": 735, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2023-05-26T10:16:17.000Z", "deleted_at": null, "code": null, "output": ".exe", "is_inverse": false}, {"id": 1386, "created_at": "2023-06-05T06:45:30.000Z", "updated_at": "2023-06-05T09:17:35.000Z", "deleted_at": null, "code": null, "output": "name:", "is_inverse": false}, {"id": 1388, "created_at": "2023-06-06T10:17:59.000Z", "updated_at": "2023-06-06T10:17:59.000Z", "deleted_at": null, "code": null, "output": "has been blocked", "is_inverse": true}, {"id": 940, "created_at": "2022-11-02T13:24:24.000Z", "updated_at": "2023-06-07T10:28:04.000Z", "deleted_at": null, "code": null, "output": "[+] Done", "is_inverse": false}, {"id": 1419, "created_at": "2023-06-08T11:07:26.000Z", "updated_at": "2023-06-08T11:07:26.000Z", "deleted_at": null, "code": null, "output": "nobody", "is_inverse": false}, {"id": 719, "created_at": "2021-09-23T09:52:26.000Z", "updated_at": "2023-06-22T07:05:09.000Z", "deleted_at": null, "code": null, "output": "Cannot access file", "is_inverse": false}, {"id": 756, "created_at": "2021-11-04T10:31:10.000Z", "updated_at": "2023-06-22T15:06:24.000Z", "deleted_at": null, "code": 0, "output": "[+] New DisableRestrictedAdmin value: not set", "is_inverse": false}, {"id": 755, "created_at": "2021-11-04T10:31:10.000Z", "updated_at": "2023-06-22T15:06:24.000Z", "deleted_at": null, "code": 0, "output": "[+] New DisableRestrictedAdmin value: 0", "is_inverse": false}, {"id": 796, "created_at": "2021-12-10T09:20:02.000Z", "updated_at": "2023-07-03T15:02:07.000Z", "deleted_at": null, "code": null, "output": "Successfully dumped process", "is_inverse": false}, {"id": 1500, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2023-07-13T07:06:43.000Z", "deleted_at": null, "code": 0, "output": "[+] Local Admin", "is_inverse": false}, {"id": 509, "created_at": "2021-03-09T06:37:54.000Z", "updated_at": "2023-07-05T12:21:54.000Z", "deleted_at": null, "code": -1, "output": "User name", "is_inverse": false}, {"id": 1520, "created_at": "2023-07-03T12:34:19.000Z", "updated_at": "2023-07-13T07:44:27.000Z", "deleted_at": null, "code": 0, "output": "successfully.", "is_inverse": false}, {"id": 1499, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2023-07-13T07:06:43.000Z", "deleted_at": null, "code": 0, "output": "[+] Authenticated", "is_inverse": false}, {"id": 1496, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2023-07-13T07:19:01.000Z", "deleted_at": null, "code": null, "output": "No remote share is found!", "is_inverse": false}, {"id": 1495, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2023-07-13T07:19:27.000Z", "deleted_at": null, "code": null, "output": "OID=", "is_inverse": false}, {"id": 1492, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2023-07-13T07:22:59.000Z", "deleted_at": null, "code": 0, "output": "Connection refused", "is_inverse": false}, {"id": 941, "created_at": "2022-11-02T17:46:43.000Z", "updated_at": "2023-07-13T07:34:28.000Z", "deleted_at": null, "code": null, "output": "Service path was restored", "is_inverse": false}, {"id": 800, "created_at": "2021-12-17T10:17:04.000Z", "updated_at": "2023-07-13T07:07:51.000Z", "deleted_at": null, "code": null, "output": "[+] Ticket is short. Target is vulnerable!", "is_inverse": false}, {"id": 718, "created_at": "2021-09-23T09:52:26.000Z", "updated_at": "2023-07-13T07:15:04.000Z", "deleted_at": null, "code": 0, "output": "Process completed", "is_inverse": false}, {"id": 695, "created_at": "2021-08-18T11:38:02.000Z", "updated_at": "2023-07-13T07:17:01.000Z", "deleted_at": null, "code": 0, "output": "[+] Success! Username:", "is_inverse": false}, {"id": 1585, "created_at": "2023-07-14T12:57:56.000Z", "updated_at": "2023-07-17T06:39:54.000Z", "deleted_at": null, "code": null, "output": "has been terminated!", "is_inverse": false}, {"id": 1651, "created_at": "2023-07-18T12:41:18.000Z", "updated_at": "2023-07-18T13:10:58.000Z", "deleted_at": null, "code": null, "output": "SCCM is installed.", "is_inverse": false}, {"id": 1327, "created_at": "2023-05-24T10:18:20.000Z", "updated_at": "2023-11-01T06:39:47.000Z", "deleted_at": null, "code": null, "output": "PsExec64.exe", "is_inverse": false}, {"id": 595, "created_at": "2021-03-09T07:03:30.000Z", "updated_at": "2023-07-19T13:44:11.000Z", "deleted_at": null, "code": null, "output": "Attack failed", "is_inverse": true}, {"id": 861, "created_at": "2022-04-13T11:59:11.000Z", "updated_at": "2023-07-24T14:17:25.000Z", "deleted_at": null, "code": null, "output": "mini.dmp", "is_inverse": false}, {"id": 1488, "created_at": "2023-06-22T14:49:31.000Z", "updated_at": "2023-07-26T10:28:52.000Z", "deleted_at": null, "code": 0, "output": "NOT OK", "is_inverse": true}, {"id": 3621, "created_at": "2025-03-14T06:38:53.000Z", "updated_at": "2025-03-14T07:45:09.000Z", "deleted_at": null, "code": null, "output": "User Profile:", "is_inverse": false}, {"id": 1684, "created_at": "2023-07-20T13:07:11.000Z", "updated_at": "2023-08-02T08:00:18.000Z", "deleted_at": null, "code": null, "output": "bookmark.csv success", "is_inverse": false}, {"id": 1888, "created_at": "2023-08-18T11:40:35.000Z", "updated_at": "2023-08-18T11:40:49.000Z", "deleted_at": null, "code": null, "output": "[S] Admin access", "is_inverse": false}, {"id": 1887, "created_at": "2023-08-18T11:00:42.000Z", "updated_at": "2023-08-18T11:00:56.000Z", "deleted_at": null, "code": null, "output": "Passwords : [BLANK]", "is_inverse": true}, {"id": 1923, "created_at": "2023-08-22T11:08:53.000Z", "updated_at": "2023-08-22T11:12:01.000Z", "deleted_at": null, "code": null, "output": "alc", "is_inverse": false}, {"id": 1924, "created_at": "2023-08-25T10:39:27.000Z", "updated_at": "2023-08-28T06:38:19.000Z", "deleted_at": null, "code": null, "output": "Finder.app", "is_inverse": false}, {"id": 1957, "created_at": "2023-09-01T06:42:27.000Z", "updated_at": "2023-09-01T06:59:23.000Z", "deleted_at": null, "code": null, "output": "Detached from process with PID", "is_inverse": false}, {"id": 1990, "created_at": "2023-09-05T08:19:10.000Z", "updated_at": "2023-09-05T10:36:28.000Z", "deleted_at": null, "code": null, "output": "users: ", "is_inverse": false}, {"id": 3620, "created_at": "2025-03-13T11:48:43.000Z", "updated_at": "2025-03-14T07:45:09.000Z", "deleted_at": null, "code": null, "output": "Hello from picus", "is_inverse": false}, {"id": 1994, "created_at": "2023-09-06T13:53:04.000Z", "updated_at": "2023-09-06T13:54:40.000Z", "deleted_at": null, "code": null, "output": "Hardware:", "is_inverse": false}, {"id": 1717, "created_at": "2023-07-26T08:37:15.000Z", "updated_at": "2023-09-06T07:30:02.000Z", "deleted_at": null, "code": null, "output": "TCP:", "is_inverse": false}, {"id": 1188, "created_at": "2023-05-02T07:35:31.000Z", "updated_at": "2024-11-28T14:21:06.000Z", "deleted_at": null, "code": null, "output": "Successfully launched", "is_inverse": false}, {"id": 1584, "created_at": "2023-07-14T12:57:56.000Z", "updated_at": "2025-05-06T12:02:21.000Z", "deleted_at": null, "code": null, "output": "successfull", "is_inverse": false}, {"id": 1551, "created_at": "2023-07-13T09:10:20.000Z", "updated_at": "2025-05-07T12:29:34.000Z", "deleted_at": null, "code": null, "output": "[+] Searching:", "is_inverse": false}, {"id": 1853, "created_at": "2023-08-10T10:33:55.000Z", "updated_at": "2025-05-21T20:33:24.000Z", "deleted_at": null, "code": null, "output": "rsyncd", "is_inverse": false}, {"id": 1127, "created_at": "2023-04-10T08:19:46.000Z", "updated_at": "2025-01-02T07:06:22.000Z", "deleted_at": null, "code": null, "output": "...STARTING...", "is_inverse": false}, {"id": 962, "created_at": "2022-12-08T08:50:26.000Z", "updated_at": "2025-01-06T15:22:46.000Z", "deleted_at": null, "code": null, "output": "MimiPenguin Results:", "is_inverse": false}, {"id": 3619, "created_at": "2025-03-13T09:21:42.000Z", "updated_at": "2025-03-14T07:45:09.000Z", "deleted_at": null, "code": null, "output": "Completed", "is_inverse": false}, {"id": 611, "created_at": "2021-03-09T07:05:32.000Z", "updated_at": "2025-02-21T12:48:20.000Z", "deleted_at": null, "code": null, "output": "[*]", "is_inverse": false}, {"id": 8, "created_at": "2021-01-06T15:57:51.000Z", "updated_at": "2025-02-21T12:48:20.000Z", "deleted_at": null, "code": 1, "output": "No items found", "is_inverse": false}, {"id": 1486, "created_at": "2023-06-22T07:38:47.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "cannot access the file", "is_inverse": false}, {"id": 1485, "created_at": "2023-06-22T07:07:59.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Access is denied.", "is_inverse": false}, {"id": 715, "created_at": "2021-09-14T10:55:14.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Do not run this as admin", "is_inverse": false}, {"id": 710, "created_at": "2021-08-24T11:20:29.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Name", "is_inverse": false}, {"id": 567, "created_at": "2021-03-09T06:57:14.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Finished", "is_inverse": false}, {"id": 1720, "created_at": "2023-07-28T08:18:49.000Z", "updated_at": "2025-03-14T07:45:09.000Z", "deleted_at": null, "code": null, "output": "Display Name", "is_inverse": false}, {"id": 1650, "created_at": "2023-07-18T11:04:00.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "I can put text", "is_inverse": false}, {"id": 522, "created_at": "2021-03-09T06:43:22.000Z", "updated_at": "2025-04-11T13:17:05.000Z", "deleted_at": null, "code": 0, "output": "The backup catalog has been successfully deleted.", "is_inverse": false}, {"id": 825, "created_at": "2022-02-18T08:20:55.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "[*] Closing symbolic link handle", "is_inverse": false}, {"id": 1854, "created_at": "2023-08-10T11:25:38.000Z", "updated_at": "2025-05-05T14:32:30.000Z", "deleted_at": null, "code": null, "output": "Started rsyncd Service", "is_inverse": false}, {"id": 1336, "created_at": "2023-05-25T12:49:20.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "GUID:", "is_inverse": false}, {"id": 29, "created_at": "2021-01-17T19:44:30.000Z", "updated_at": "2025-06-12T11:00:19.000Z", "deleted_at": null, "code": -2147024809, "output": "", "is_inverse": false}, {"id": 1112, "created_at": "2023-03-29T06:38:37.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "Address patched via 0x74!", "is_inverse": false}, {"id": 949, "created_at": "2022-11-11T14:56:21.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "test.txt's time stamp changed to C:\\Windows\\System32\\cmd.exe's time stamp", "is_inverse": false}, {"id": 723, "created_at": "2021-09-30T09:32:39.000Z", "updated_at": "2025-06-04T12:57:07.000Z", "deleted_at": null, "code": 0, "output": "True", "is_inverse": false}, {"id": 871, "created_at": "2022-06-17T10:36:14.000Z", "updated_at": "2025-06-18T08:47:42.000Z", "deleted_at": null, "code": null, "output": "Failed to clear", "is_inverse": true}, {"id": 490, "created_at": "2021-03-09T06:30:19.000Z", "updated_at": "2025-06-25T13:10:24.000Z", "deleted_at": null, "code": 0, "output": "c:\\", "is_inverse": false}, {"id": 1494, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2025-06-27T12:02:16.000Z", "deleted_at": null, "code": null, "output": "has successfully been created", "is_inverse": false}, {"id": 513, "created_at": "2021-03-09T06:38:45.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 1, "output": "<PERSON>er", "is_inverse": false}, {"id": 512, "created_at": "2021-03-09T06:38:45.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "Packed", "is_inverse": false}, {"id": 498, "created_at": "2021-03-09T06:32:12.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 5, "output": "", "is_inverse": false}, {"id": 1617, "created_at": "2023-07-17T06:02:54.000Z", "updated_at": "2023-09-06T07:32:06.000Z", "deleted_at": null, "code": null, "output": "[!] Random Math Calculation:", "is_inverse": false}, {"id": 1525, "created_at": "2023-07-05T07:29:53.000Z", "updated_at": "2023-09-06T07:32:50.000Z", "deleted_at": null, "code": null, "output": "Path for the removable media is", "is_inverse": false}, {"id": 1287, "created_at": "2023-05-15T08:56:21.000Z", "updated_at": "2023-09-06T07:39:35.000Z", "deleted_at": null, "code": null, "output": "hiddenpcs", "is_inverse": false}, {"id": 1134, "created_at": "2023-04-23T21:18:40.000Z", "updated_at": "2023-09-06T07:43:05.000Z", "deleted_at": null, "code": null, "output": "Malicious", "is_inverse": false}, {"id": 1050, "created_at": "2023-02-02T19:00:01.000Z", "updated_at": "2023-09-06T08:07:49.000Z", "deleted_at": null, "code": null, "output": "decoded", "is_inverse": false}, {"id": 2063, "created_at": "2023-09-11T14:04:01.000Z", "updated_at": "2023-09-11T14:04:01.000Z", "deleted_at": null, "code": null, "output": "sAMAccountName:", "is_inverse": false}, {"id": 2096, "created_at": "2023-09-13T06:51:39.000Z", "updated_at": "2023-09-13T12:18:10.000Z", "deleted_at": null, "code": null, "output": "Sample Service from Picus", "is_inverse": false}, {"id": 2196, "created_at": "2023-09-25T06:33:11.000Z", "updated_at": "2023-09-25T06:33:11.000Z", "deleted_at": null, "code": null, "output": "inet", "is_inverse": false}, {"id": 2199, "created_at": "2023-09-26T12:20:32.000Z", "updated_at": "2023-09-26T13:31:38.000Z", "deleted_at": null, "code": null, "output": "Completed debugger detection", "is_inverse": false}, {"id": 3196, "created_at": "2024-04-02T08:36:23.000Z", "updated_at": "2025-05-22T08:03:28.000Z", "deleted_at": null, "code": null, "output": ".png", "is_inverse": false}, {"id": 1133, "created_at": "2023-04-23T19:57:41.000Z", "updated_at": "2024-05-02T10:31:09.000Z", "deleted_at": null, "code": null, "output": "Calculator.app", "is_inverse": false}, {"id": 2202, "created_at": "2023-09-26T14:30:56.000Z", "updated_at": "2023-10-03T09:04:35.000Z", "deleted_at": null, "code": null, "output": "Service", "is_inverse": false}, {"id": 2246, "created_at": "2023-10-06T08:39:50.000Z", "updated_at": "2023-10-06T11:40:36.000Z", "deleted_at": null, "code": null, "output": "System error", "is_inverse": true}, {"id": 2240, "created_at": "2023-10-02T12:04:14.000Z", "updated_at": "2023-10-06T12:05:40.000Z", "deleted_at": null, "code": null, "output": "Screenshot saved to", "is_inverse": false}, {"id": 2282, "created_at": "2023-10-13T06:47:05.000Z", "updated_at": "2023-10-13T06:47:47.000Z", "deleted_at": null, "code": null, "output": "Segmentation fault", "is_inverse": false}, {"id": 2316, "created_at": "2023-10-16T13:45:02.000Z", "updated_at": "2023-10-16T13:57:18.000Z", "deleted_at": null, "code": null, "output": "NTSTATUS: 0X0 - STATUS_SUCCESS", "is_inverse": false}, {"id": 2317, "created_at": "2023-10-18T07:50:42.000Z", "updated_at": "2023-10-18T08:46:02.000Z", "deleted_at": null, "code": null, "output": "no soundcards found", "is_inverse": true}, {"id": 654, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2025-03-04T12:51:41.000Z", "deleted_at": null, "code": null, "output": "Current LogonID (LUID)", "is_inverse": false}, {"id": 2911, "created_at": "2024-02-14T07:47:49.000Z", "updated_at": "2025-05-22T08:19:28.000Z", "deleted_at": null, "code": null, "output": "_proxy", "is_inverse": false}, {"id": 2953, "created_at": "2024-02-23T08:02:23.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "Terminated Successfully: version reporter applet", "is_inverse": false}, {"id": 2915, "created_at": "2024-02-15T11:56:05.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "[+] Type of", "is_inverse": false}, {"id": 2906, "created_at": "2024-02-13T13:08:47.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "[+] Scan Complete!", "is_inverse": false}, {"id": 2244, "created_at": "2023-10-04T12:09:18.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "File: C:\\Windows\\Installer\\", "is_inverse": false}, {"id": 2, "created_at": "2021-01-05T13:47:32.000Z", "updated_at": "2023-10-30T11:56:42.000Z", "deleted_at": null, "code": null, "output": "[*] Hash", "is_inverse": false}, {"id": 27, "created_at": "2021-01-16T08:18:58.000Z", "updated_at": "2023-10-31T08:32:29.000Z", "deleted_at": null, "code": null, "output": "ProcessName", "is_inverse": false}, {"id": 2203, "created_at": "2023-09-26T14:30:56.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "SystemStartOptions", "is_inverse": false}, {"id": 1093, "created_at": "2023-02-28T10:45:01.000Z", "updated_at": "2023-11-16T14:21:54.000Z", "deleted_at": null, "code": null, "output": "* Directory:", "is_inverse": false}, {"id": 2584, "created_at": "2023-12-08T06:46:34.000Z", "updated_at": "2023-12-08T07:09:39.000Z", "deleted_at": null, "code": null, "output": "c:\\windows\\system32\\amsi.dll", "is_inverse": true}, {"id": 2617, "created_at": "2023-12-13T09:57:36.000Z", "updated_at": "2023-12-13T09:57:36.000Z", "deleted_at": null, "code": null, "output": "audit.d.service could not be found", "is_inverse": true}, {"id": 2619, "created_at": "2023-12-14T12:38:41.000Z", "updated_at": "2023-12-14T12:38:41.000Z", "deleted_at": null, "code": null, "output": "/etc/passwd", "is_inverse": true}, {"id": 2836, "created_at": "2024-01-29T12:41:46.000Z", "updated_at": "2024-01-29T12:41:46.000Z", "deleted_at": null, "code": null, "output": "[*] Clean-up done", "is_inverse": false}, {"id": 2799, "created_at": "2024-01-22T13:06:26.000Z", "updated_at": "2024-01-22T13:06:26.000Z", "deleted_at": null, "code": null, "output": "Program Files", "is_inverse": false}, {"id": 2798, "created_at": "2024-01-22T12:04:33.000Z", "updated_at": "2024-01-22T12:04:33.000Z", "deleted_at": null, "code": null, "output": "Windows", "is_inverse": false}, {"id": 2765, "created_at": "2024-01-19T13:57:12.000Z", "updated_at": "2024-01-19T14:07:01.000Z", "deleted_at": null, "code": null, "output": "[-] No EDR process was detected", "is_inverse": false}, {"id": 2764, "created_at": "2024-01-19T12:51:19.000Z", "updated_at": "2024-01-19T14:07:01.000Z", "deleted_at": null, "code": null, "output": "Deleted custom WFP provider", "is_inverse": false}, {"id": 2763, "created_at": "2024-01-19T12:51:19.000Z", "updated_at": "2024-01-19T14:07:01.000Z", "deleted_at": null, "code": null, "output": "Added WFP filter for", "is_inverse": false}, {"id": 2762, "created_at": "2024-01-17T16:51:40.000Z", "updated_at": "2024-01-17T16:51:40.000Z", "deleted_at": null, "code": null, "output": "[+] Found", "is_inverse": false}, {"id": 2760, "created_at": "2024-01-17T12:46:48.000Z", "updated_at": "2024-01-18T08:15:34.000Z", "deleted_at": null, "code": null, "output": "picus-logo_51067.png", "is_inverse": false}, {"id": 2725, "created_at": "2024-01-12T12:05:13.000Z", "updated_at": "2024-01-12T12:19:30.000Z", "deleted_at": null, "code": null, "output": "[+] Pipe listing:", "is_inverse": false}, {"id": 2950, "created_at": "2024-02-20T13:37:16.000Z", "updated_at": "2024-02-20T13:37:16.000Z", "deleted_at": null, "code": null, "output": "is active", "is_inverse": true}, {"id": 2916, "created_at": "2024-02-16T08:00:39.000Z", "updated_at": "2024-02-16T08:00:39.000Z", "deleted_at": null, "code": null, "output": "finished at", "is_inverse": false}, {"id": 2913, "created_at": "2024-02-15T09:25:31.000Z", "updated_at": "2024-02-15T09:25:31.000Z", "deleted_at": null, "code": null, "output": "Message from client", "is_inverse": false}, {"id": 1074, "created_at": "2023-02-24T12:04:45.000Z", "updated_at": "2024-02-27T07:27:06.000Z", "deleted_at": null, "code": null, "output": "decrypt success", "is_inverse": false}, {"id": 3126, "created_at": "2024-03-22T11:29:39.000Z", "updated_at": "2024-03-22T11:29:39.000Z", "deleted_at": null, "code": null, "output": "[+] Successfully located", "is_inverse": false}, {"id": 3125, "created_at": "2024-03-21T07:33:36.000Z", "updated_at": "2024-03-22T06:44:40.000Z", "deleted_at": null, "code": null, "output": "motd.sh", "is_inverse": false}, {"id": 2351, "created_at": "2023-10-25T11:46:29.000Z", "updated_at": "2024-04-02T06:46:05.000Z", "deleted_at": null, "code": null, "output": "1 of 1 target completed", "is_inverse": false}, {"id": 1111, "created_at": "2023-03-22T08:20:54.000Z", "updated_at": "2024-03-29T14:42:16.000Z", "deleted_at": null, "code": null, "output": "administrator to view exclusions", "is_inverse": true}, {"id": 1048, "created_at": "2023-01-26T15:46:11.000Z", "updated_at": "2024-04-03T07:49:33.000Z", "deleted_at": null, "code": null, "output": "nt authority", "is_inverse": false}, {"id": 3210, "created_at": "2024-04-18T17:01:49.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "http://alphv", "is_inverse": false}, {"id": 3201, "created_at": "2024-04-05T08:10:28.000Z", "updated_at": "2024-04-05T08:10:28.000Z", "deleted_at": null, "code": null, "output": "File content overwritten with zeros and file deleted successfully.", "is_inverse": false}, {"id": 3200, "created_at": "2024-04-05T07:55:17.000Z", "updated_at": "2024-04-05T08:46:42.000Z", "deleted_at": null, "code": null, "output": "Hello World from Picus!", "is_inverse": false}, {"id": 3199, "created_at": "2024-04-04T14:32:21.000Z", "updated_at": "2024-04-05T08:46:42.000Z", "deleted_at": null, "code": null, "output": "Second Tour: First File", "is_inverse": false}, {"id": 3198, "created_at": "2024-04-04T11:57:15.000Z", "updated_at": "2024-04-05T08:46:42.000Z", "deleted_at": null, "code": null, "output": "Executed!", "is_inverse": false}, {"id": 576, "created_at": "2021-03-09T06:59:51.000Z", "updated_at": "2024-04-24T07:17:52.000Z", "deleted_at": null, "code": 0, "output": "<PERSON>pied", "is_inverse": false}, {"id": 2201, "created_at": "2023-09-26T14:30:56.000Z", "updated_at": "2025-05-23T12:47:13.000Z", "deleted_at": null, "code": null, "output": "37 self_delete.bat", "is_inverse": true}, {"id": 2280, "created_at": "2023-10-10T07:52:16.000Z", "updated_at": "2025-05-22T08:12:05.000Z", "deleted_at": null, "code": null, "output": "mount: /media/usbMountPoint: can't find in /etc/fstab", "is_inverse": true}, {"id": 3213, "created_at": "2024-04-26T14:33:27.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "READY threads", "is_inverse": false}, {"id": 601, "created_at": "2021-03-09T07:04:17.000Z", "updated_at": "2025-05-23T12:47:07.000Z", "deleted_at": null, "code": null, "output": "[+] Recording completed", "is_inverse": false}, {"id": 557, "created_at": "2021-03-09T06:54:43.000Z", "updated_at": "2025-06-19T08:15:13.000Z", "deleted_at": null, "code": null, "output": "AppDomainManager is hooked!", "is_inverse": false}, {"id": 2691, "created_at": "2023-12-28T13:32:57.000Z", "updated_at": "2025-01-06T15:22:46.000Z", "deleted_at": null, "code": null, "output": "hello world", "is_inverse": false}, {"id": 2692, "created_at": "2023-12-28T13:34:14.000Z", "updated_at": "2025-05-27T08:50:50.000Z", "deleted_at": null, "code": null, "output": "Infected", "is_inverse": false}, {"id": 3092, "created_at": "2024-03-14T12:36:42.000Z", "updated_at": "2025-05-30T08:30:58.000Z", "deleted_at": null, "code": null, "output": "<PERSON>le Hijacked successfully", "is_inverse": false}, {"id": 1993, "created_at": "2023-09-06T13:18:18.000Z", "updated_at": "2025-01-06T15:22:46.000Z", "deleted_at": null, "code": null, "output": "[!] Successfully escaped container", "is_inverse": false}, {"id": 1064, "created_at": "2023-02-10T12:22:33.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "Successful!", "is_inverse": false}, {"id": 2871, "created_at": "2024-02-01T09:01:23.000Z", "updated_at": "2025-03-26T13:17:07.000Z", "deleted_at": null, "code": null, "output": "Serial Drive Number of C:", "is_inverse": false}, {"id": 2801, "created_at": "2024-01-24T13:01:51.000Z", "updated_at": "2025-03-26T13:17:07.000Z", "deleted_at": null, "code": null, "output": "[!] You are already Domain Admin,", "is_inverse": false}, {"id": 2800, "created_at": "2024-01-24T13:01:51.000Z", "updated_at": "2025-03-26T13:17:07.000Z", "deleted_at": null, "code": null, "output": "[!] Your Remaining Limit:", "is_inverse": false}, {"id": 2908, "created_at": "2024-02-13T13:08:48.000Z", "updated_at": "2025-05-30T08:32:17.000Z", "deleted_at": null, "code": null, "output": "[+] TLS Callback Changed To", "is_inverse": false}, {"id": 2030, "created_at": "2023-09-11T06:53:32.000Z", "updated_at": "2025-03-26T13:17:07.000Z", "deleted_at": null, "code": null, "output": "InstallDate=", "is_inverse": false}, {"id": 2907, "created_at": "2024-02-13T13:08:48.000Z", "updated_at": "2025-05-30T08:32:17.000Z", "deleted_at": null, "code": null, "output": "TLSCallbackDummy", "is_inverse": false}, {"id": 3771, "created_at": "2025-05-13T12:32:26.000Z", "updated_at": "2025-05-13T12:41:45.000Z", "deleted_at": null, "code": null, "output": "d-----", "is_inverse": false}, {"id": 3207, "created_at": "2024-04-17T12:08:05.000Z", "updated_at": "2025-05-21T20:43:56.000Z", "deleted_at": null, "code": null, "output": "\"GroupName\": \"picusgroup\"", "is_inverse": false}, {"id": 3772, "created_at": "2025-05-13T14:17:16.000Z", "updated_at": "2025-05-15T12:34:50.000Z", "deleted_at": null, "code": null, "output": "Installer:rsrc", "is_inverse": false}, {"id": 13, "created_at": "2021-01-07T12:04:54.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": 1, "output": "", "is_inverse": false}, {"id": 3769, "created_at": "2025-05-13T06:37:41.000Z", "updated_at": "2025-05-15T12:34:50.000Z", "deleted_at": null, "code": 42, "output": "", "is_inverse": false}, {"id": 3773, "created_at": "2025-05-14T08:29:12.000Z", "updated_at": "2025-05-20T12:41:14.000Z", "deleted_at": null, "code": null, "output": "<dscl_cmd> DS Error: -14090 (eDSAuthFailed)", "is_inverse": false}, {"id": 3205, "created_at": "2024-04-17T11:47:02.000Z", "updated_at": "2025-05-21T20:43:00.000Z", "deleted_at": null, "code": null, "output": "\"PasswordPolicy\":", "is_inverse": false}, {"id": 3204, "created_at": "2024-04-17T08:12:05.000Z", "updated_at": "2025-05-21T20:42:22.000Z", "deleted_at": null, "code": null, "output": "CONTENTS", "is_inverse": false}, {"id": 2618, "created_at": "2023-12-14T11:03:00.000Z", "updated_at": "2025-05-21T20:40:59.000Z", "deleted_at": null, "code": null, "output": "Active: active (running)", "is_inverse": false}, {"id": 2239, "created_at": "2023-10-02T11:01:03.000Z", "updated_at": "2025-05-21T20:36:28.000Z", "deleted_at": null, "code": null, "output": "Domain:", "is_inverse": false}, {"id": 2904, "created_at": "2024-02-12T11:31:48.000Z", "updated_at": "2025-05-30T08:32:29.000Z", "deleted_at": null, "code": null, "output": "[!] Success", "is_inverse": false}, {"id": 2802, "created_at": "2024-01-25T08:12:12.000Z", "updated_at": "2025-05-30T08:38:31.000Z", "deleted_at": null, "code": null, "output": "[!] Patch is valid!", "is_inverse": false}, {"id": 2621, "created_at": "2023-12-15T11:42:06.000Z", "updated_at": "2025-05-30T08:33:37.000Z", "deleted_at": null, "code": null, "output": "[+] Specified Dll is successfully unlinked from PEB!", "is_inverse": false}, {"id": 929, "created_at": "2022-10-07T14:16:02.000Z", "updated_at": "2025-05-30T08:39:18.000Z", "deleted_at": null, "code": null, "output": "[-]", "is_inverse": true}, {"id": 3912, "created_at": "2025-06-03T13:09:37.000Z", "updated_at": "2025-06-03T13:09:37.000Z", "deleted_at": null, "code": null, "output": "[DEBUG] [*] Calling NtProtectVirtualMemory", "is_inverse": false}, {"id": 3948, "created_at": "2025-06-10T14:45:51.000Z", "updated_at": "2025-06-10T14:45:51.000Z", "deleted_at": null, "code": null, "output": "Terminating other processes whose name is", "is_inverse": false}, {"id": 3947, "created_at": "2025-06-10T14:33:19.000Z", "updated_at": "2025-06-12T14:52:15.000Z", "deleted_at": null, "code": null, "output": "Mac Address:", "is_inverse": false}, {"id": 489, "created_at": "2021-03-09T06:30:19.000Z", "updated_at": "2025-06-25T13:10:24.000Z", "deleted_at": null, "code": 0, "output": "C:\\", "is_inverse": false}, {"id": 488, "created_at": "2021-03-09T06:30:19.000Z", "updated_at": "2025-06-25T13:10:24.000Z", "deleted_at": null, "code": 1, "output": "File Not Found", "is_inverse": false}, {"id": 830, "created_at": "2022-03-04T06:48:37.000Z", "updated_at": "2025-06-26T08:26:22.000Z", "deleted_at": null, "code": null, "output": "[+] Done... Have a nice day!", "is_inverse": false}, {"id": 642, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2025-06-30T13:14:28.000Z", "deleted_at": null, "code": -2147467261, "output": "", "is_inverse": false}, {"id": 2419, "created_at": "2023-11-03T07:48:52.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "Working DLL!", "is_inverse": false}, {"id": 681, "created_at": "2021-08-18T11:38:00.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "[+] Done..", "is_inverse": false}, {"id": 4122, "created_at": "2025-07-10T11:21:00.000Z", "updated_at": "2025-07-10T14:25:06.000Z", "deleted_at": null, "code": null, "output": "Machine        :", "is_inverse": false}, {"id": 4120, "created_at": "2025-07-10T09:37:13.000Z", "updated_at": "2025-07-10T14:25:06.000Z", "deleted_at": null, "code": null, "output": "[COMPLETED]", "is_inverse": false}, {"id": 4119, "created_at": "2025-07-10T07:39:56.000Z", "updated_at": "2025-07-10T14:25:06.000Z", "deleted_at": null, "code": null, "output": "CPU Frequency:", "is_inverse": false}, {"id": 4118, "created_at": "2025-07-09T14:04:27.000Z", "updated_at": "2025-07-10T14:25:06.000Z", "deleted_at": null, "code": null, "output": "safarifontsagent", "is_inverse": false}, {"id": 4085, "created_at": "2025-07-08T10:48:40.000Z", "updated_at": "2025-07-11T10:40:47.000Z", "deleted_at": null, "code": null, "output": "[*] Domain Information", "is_inverse": false}, {"id": 3770, "created_at": "2025-05-13T06:37:47.000Z", "updated_at": "2025-07-10T14:25:06.000Z", "deleted_at": null, "code": 0, "output": "", "is_inverse": false}, {"id": 3162, "created_at": "2024-03-29T14:39:41.000Z", "updated_at": "2024-05-02T12:37:21.000Z", "deleted_at": null, "code": null, "output": "ExclusionPath=", "is_inverse": false}, {"id": 3161, "created_at": "2024-03-29T14:27:25.000Z", "updated_at": "2024-05-02T12:37:21.000Z", "deleted_at": null, "code": null, "output": ":\\", "is_inverse": false}, {"id": 2987, "created_at": "2024-02-28T11:06:23.000Z", "updated_at": "2024-05-02T08:35:26.000Z", "deleted_at": null, "code": null, "output": "inflating: hp.scpt", "is_inverse": false}, {"id": 2912, "created_at": "2024-02-14T08:09:08.000Z", "updated_at": "2025-03-07T07:44:12.000Z", "deleted_at": null, "code": null, "output": "nobody:", "is_inverse": false}, {"id": 1331, "created_at": "2023-05-24T10:21:07.000Z", "updated_at": "2024-05-02T08:29:25.000Z", "deleted_at": null, "code": null, "output": "CVE-2018-4993.pdf", "is_inverse": false}, {"id": 1330, "created_at": "2023-05-24T10:20:39.000Z", "updated_at": "2024-05-02T08:28:59.000Z", "deleted_at": null, "code": null, "output": "CVE-2018-8414.docx", "is_inverse": false}, {"id": 944, "created_at": "2022-11-10T11:52:48.000Z", "updated_at": "2024-04-26T15:06:45.000Z", "deleted_at": null, "code": null, "output": "[+] StopDefenderServices success!", "is_inverse": false}, {"id": 651, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2024-04-29T13:03:03.000Z", "deleted_at": null, "code": null, "output": "High Integrity Process", "is_inverse": false}, {"id": 764, "created_at": "2021-11-19T10:22:16.000Z", "updated_at": "2024-05-08T13:12:08.000Z", "deleted_at": null, "code": null, "output": "[ELEVATE] post_callback_use_self. SYSTEM Token is set", "is_inverse": false}, {"id": 713, "created_at": "2021-09-14T10:55:14.000Z", "updated_at": "2024-05-08T13:49:34.000Z", "deleted_at": null, "code": null, "output": "Spawned", "is_inverse": false}, {"id": 3216, "created_at": "2024-05-27T16:57:36.000Z", "updated_at": "2024-05-27T17:45:16.000Z", "deleted_at": null, "code": null, "output": "Process created successfully with spoofed command line.", "is_inverse": false}, {"id": 3215, "created_at": "2024-05-21T12:46:09.000Z", "updated_at": "2024-05-21T13:51:04.000Z", "deleted_at": null, "code": null, "output": "The screen is currently", "is_inverse": false}, {"id": 3214, "created_at": "2024-05-21T07:51:36.000Z", "updated_at": "2024-05-21T13:51:04.000Z", "deleted_at": null, "code": null, "output": "AppleAccountAssistant.app", "is_inverse": false}, {"id": 1819, "created_at": "2023-08-01T13:04:22.000Z", "updated_at": "2024-06-06T11:59:35.000Z", "deleted_at": null, "code": null, "output": "done", "is_inverse": false}, {"id": 1042, "created_at": "2023-01-23T14:25:35.000Z", "updated_at": "2025-03-05T07:34:04.000Z", "deleted_at": null, "code": null, "output": "self_delete_variant_1.sh", "is_inverse": true}, {"id": 551, "created_at": "2021-03-09T06:52:01.000Z", "updated_at": "2025-03-11T15:04:34.000Z", "deleted_at": null, "code": null, "output": "Hello World", "is_inverse": false}, {"id": 3219, "created_at": "2024-06-06T12:03:50.000Z", "updated_at": "2024-06-07T08:00:52.000Z", "deleted_at": null, "code": null, "output": "Culture:", "is_inverse": false}, {"id": 3225, "created_at": "2024-06-26T11:34:48.000Z", "updated_at": "2024-06-27T07:22:55.000Z", "deleted_at": null, "code": null, "output": "Program is running...", "is_inverse": false}, {"id": 680, "created_at": "2021-08-18T11:37:59.000Z", "updated_at": "2024-06-16T04:44:12.000Z", "deleted_at": null, "code": 0, "output": "[*] Executing Implant's Entry Point:", "is_inverse": false}, {"id": 3229, "created_at": "2024-07-01T14:50:16.000Z", "updated_at": "2024-07-08T13:54:24.000Z", "deleted_at": null, "code": null, "output": "Full Info", "is_inverse": false}, {"id": 3228, "created_at": "2024-07-01T12:01:23.000Z", "updated_at": "2024-07-01T12:01:23.000Z", "deleted_at": null, "code": null, "output": "xmrig", "is_inverse": false}, {"id": 3227, "created_at": "2024-07-01T09:05:54.000Z", "updated_at": "2024-07-01T09:05:54.000Z", "deleted_at": null, "code": null, "output": "POOL #3", "is_inverse": false}, {"id": 3020, "created_at": "2024-03-01T07:38:01.000Z", "updated_at": "2024-07-08T13:54:24.000Z", "deleted_at": null, "code": null, "output": "removeself", "is_inverse": true}, {"id": 2952, "created_at": "2024-02-22T11:36:24.000Z", "updated_at": "2024-07-08T13:54:24.000Z", "deleted_at": null, "code": null, "output": "exfil.tar.gz", "is_inverse": false}, {"id": 3237, "created_at": "2024-07-09T13:59:30.000Z", "updated_at": "2024-07-09T14:04:17.000Z", "deleted_at": null, "code": null, "output": "UID: 0", "is_inverse": false}, {"id": 3236, "created_at": "2024-07-09T13:31:49.000Z", "updated_at": "2024-07-09T13:32:56.000Z", "deleted_at": null, "code": null, "output": "# Sample /etc/sudoers file.", "is_inverse": false}, {"id": 3235, "created_at": "2024-07-09T13:31:37.000Z", "updated_at": "2024-07-09T13:32:56.000Z", "deleted_at": null, "code": null, "output": "Matching Defaults entries", "is_inverse": false}, {"id": 3242, "created_at": "2024-07-18T14:21:41.000Z", "updated_at": "2024-07-18T14:28:56.000Z", "deleted_at": null, "code": null, "output": "Name (Version) UUID <Linked Against>", "is_inverse": false}, {"id": 3241, "created_at": "2024-07-17T09:08:04.000Z", "updated_at": "2024-07-17T09:16:16.000Z", "deleted_at": null, "code": null, "output": "/var/log/", "is_inverse": false}, {"id": 3209, "created_at": "2024-04-18T16:55:22.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "HandshakeOk", "is_inverse": false}, {"id": 3203, "created_at": "2024-04-16T12:53:09.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "http://lockbit", "is_inverse": false}, {"id": 3195, "created_at": "2024-04-02T06:24:11.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "X0-lock", "is_inverse": false}, {"id": 3058, "created_at": "2024-03-08T09:24:07.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "kworker/3:1-events", "is_inverse": false}, {"id": 3056, "created_at": "2024-03-08T06:53:18.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "No such device", "is_inverse": true}, {"id": 2951, "created_at": "2024-02-22T07:40:16.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Virtualization:", "is_inverse": false}, {"id": 2838, "created_at": "2024-01-31T13:37:34.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "none", "is_inverse": true}, {"id": 2837, "created_at": "2024-01-31T13:36:42.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Hypervisor detected", "is_inverse": false}, {"id": 2835, "created_at": "2024-01-29T12:13:40.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "openssl enc'd data with salted password", "is_inverse": false}, {"id": 2688, "created_at": "2023-12-26T12:08:56.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "1970", "is_inverse": false}, {"id": 2352, "created_at": "2023-10-25T14:50:43.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "xaa", "is_inverse": false}, {"id": 2281, "created_at": "2023-10-12T08:15:36.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "uid=", "is_inverse": false}, {"id": 2245, "created_at": "2023-10-05T09:27:01.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "200", "is_inverse": false}, {"id": 2242, "created_at": "2023-10-03T13:01:30.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "stageData.txt", "is_inverse": false}, {"id": 2241, "created_at": "2023-10-02T13:01:28.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "sudoers.bak", "is_inverse": false}, {"id": 2206, "created_at": "2023-09-29T09:31:59.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Defaults !tty_tickets", "is_inverse": false}, {"id": 2200, "created_at": "2023-09-26T13:26:38.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "test.txt", "is_inverse": false}, {"id": 1996, "created_at": "2023-09-07T11:39:23.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "deleted", "is_inverse": false}, {"id": 1392, "created_at": "2023-06-08T08:58:24.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "picussimuser L", "is_inverse": false}, {"id": 1391, "created_at": "2023-06-08T08:57:19.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "picussimuser P", "is_inverse": false}, {"id": 1353, "created_at": "2023-05-31T12:08:43.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "PASS_MIN_DAYS", "is_inverse": false}, {"id": 1254, "created_at": "2023-05-10T13:45:48.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "/tmp/Simulation", "is_inverse": false}, {"id": 1137, "created_at": "2023-04-26T10:22:14.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "cookies.sqlite", "is_inverse": false}, {"id": 1136, "created_at": "2023-04-25T11:51:32.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "2215383", "is_inverse": false}, {"id": 1135, "created_at": "2023-04-25T07:14:19.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "cm9vdDp4O", "is_inverse": false}, {"id": 1126, "created_at": "2023-04-07T11:25:37.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "This file can store", "is_inverse": false}, {"id": 1125, "created_at": "2023-04-06T10:22:29.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Groups:", "is_inverse": false}, {"id": 1122, "created_at": "2023-04-05T13:51:20.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "ptrace: [32mPASS[39m", "is_inverse": false}, {"id": 1121, "created_at": "2023-04-05T13:09:41.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Current Output Verbosity Level:", "is_inverse": false}, {"id": 1119, "created_at": "2023-04-05T08:20:47.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "0", "is_inverse": false}, {"id": 1118, "created_at": "2023-04-05T07:31:42.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "picus<PERSON><PERSON>", "is_inverse": true}, {"id": 1101, "created_at": "2023-02-28T12:25:47.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "find any peripheral device!", "is_inverse": false}, {"id": 1099, "created_at": "2023-02-28T12:03:49.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "UFW is already disabled", "is_inverse": false}, {"id": 1098, "created_at": "2023-02-28T12:03:31.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "UFW is now disabled", "is_inverse": false}, {"id": 1097, "created_at": "2023-02-28T12:02:59.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Iptables is already stopped", "is_inverse": false}, {"id": 1096, "created_at": "2023-02-28T12:02:21.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Iptables is now stopped", "is_inverse": false}, {"id": 1094, "created_at": "2023-02-28T11:30:28.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "cannot remove", "is_inverse": false}, {"id": 1090, "created_at": "2023-02-28T08:51:12.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "/dev", "is_inverse": false}, {"id": 1069, "created_at": "2023-02-16T12:06:41.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": ":::CONTACT EMAIL:::", "is_inverse": false}, {"id": 1067, "created_at": "2023-02-16T10:01:28.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "umask.file", "is_inverse": false}, {"id": 1061, "created_at": "2023-02-10T07:16:12.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Executing do_vslist", "is_inverse": false}, {"id": 1057, "created_at": "2023-02-07T12:33:05.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "***********************TCP Packet*************************", "is_inverse": false}, {"id": 1041, "created_at": "2023-01-23T10:35:41.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "This will never get printed...!", "is_inverse": true}, {"id": 1040, "created_at": "2023-01-19T09:09:37.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "/tmp/Test_file.txt", "is_inverse": false}, {"id": 1038, "created_at": "2023-01-12T11:25:14.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON>", "is_inverse": false}, {"id": 1037, "created_at": "2023-01-12T11:19:23.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "processor", "is_inverse": false}, {"id": 1036, "created_at": "2023-01-12T10:37:33.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "Picus Test Message", "is_inverse": false}, {"id": 1035, "created_at": "2023-01-12T10:37:27.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "&q?)<:2v<F:bv<<*|v", "is_inverse": false}, {"id": 1719, "created_at": "2023-07-26T10:41:28.000Z", "updated_at": "2025-05-23T12:47:13.000Z", "deleted_at": null, "code": null, "output": "IPv4 Address", "is_inverse": false}, {"id": 2655, "created_at": "2023-12-22T06:36:11.000Z", "updated_at": "2024-12-06T07:24:28.000Z", "deleted_at": null, "code": null, "output": "Done", "is_inverse": false}, {"id": 3218, "created_at": "2024-06-06T12:03:50.000Z", "updated_at": "2024-12-13T07:39:43.000Z", "deleted_at": null, "code": null, "output": "winlogon.exe", "is_inverse": false}, {"id": 3224, "created_at": "2024-06-14T13:12:41.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Everything is Ok", "is_inverse": false}, {"id": 3238, "created_at": "2024-07-10T13:44:15.000Z", "updated_at": "2025-03-26T13:17:07.000Z", "deleted_at": null, "code": null, "output": "Secure Socket Funneling 3.0.0", "is_inverse": false}, {"id": 3217, "created_at": "2024-06-06T12:01:46.000Z", "updated_at": "2025-05-01T14:05:42.000Z", "deleted_at": null, "code": null, "output": "PID", "is_inverse": false}, {"id": 2197, "created_at": "2023-09-25T10:39:55.000Z", "updated_at": "2025-05-05T14:33:18.000Z", "deleted_at": null, "code": null, "output": "/tmp/test.txt", "is_inverse": false}, {"id": 701, "created_at": "2021-08-24T11:20:25.000Z", "updated_at": "2025-02-21T12:48:20.000Z", "deleted_at": null, "code": 1, "output": "The system cannot find", "is_inverse": false}, {"id": 3223, "created_at": "2024-06-14T13:04:05.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Files created", "is_inverse": false}, {"id": 3230, "created_at": "2024-07-04T13:01:41.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "ComputerName : 127.0.0.1", "is_inverse": false}, {"id": 3220, "created_at": "2024-06-06T12:03:50.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "Name of Browser:", "is_inverse": false}, {"id": 3233, "created_at": "2024-07-04T13:01:41.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "The keyword was found", "is_inverse": false}, {"id": 838, "created_at": "2022-03-18T07:08:50.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": null, "output": "FullLanguage", "is_inverse": false}, {"id": 3240, "created_at": "2024-07-11T13:52:30.000Z", "updated_at": "2025-05-29T12:53:42.000Z", "deleted_at": null, "code": null, "output": "app", "is_inverse": false}, {"id": 1113, "created_at": "2023-03-29T10:42:47.000Z", "updated_at": "2025-03-26T13:41:32.000Z", "deleted_at": null, "code": 1, "output": "CLIXML", "is_inverse": false}, {"id": 1100, "created_at": "2023-02-28T12:25:25.000Z", "updated_at": "2025-05-21T20:34:03.000Z", "deleted_at": null, "code": null, "output": "Successfully", "is_inverse": false}, {"id": 3239, "created_at": "2024-07-11T13:08:09.000Z", "updated_at": "2025-05-29T12:55:32.000Z", "deleted_at": null, "code": null, "output": "ginerd", "is_inverse": false}, {"id": 3057, "created_at": "2024-03-08T08:09:29.000Z", "updated_at": "2025-05-30T08:40:45.000Z", "deleted_at": null, "code": null, "output": "[*] Syscall Name   :", "is_inverse": false}, {"id": 531, "created_at": "2021-03-09T06:45:26.000Z", "updated_at": "2025-06-19T08:15:13.000Z", "deleted_at": null, "code": 0, "output": "<PERSON><PERSON><PERSON><PERSON>(commandline) #", "is_inverse": false}, {"id": 530, "created_at": "2021-03-09T06:44:56.000Z", "updated_at": "2025-06-24T10:47:50.000Z", "deleted_at": null, "code": null, "output": "Listening OK", "is_inverse": false}, {"id": 822, "created_at": "2022-02-11T07:04:13.000Z", "updated_at": "2025-06-26T08:26:35.000Z", "deleted_at": null, "code": null, "output": "Untrusted Integrity Process", "is_inverse": false}, {"id": 813, "created_at": "2022-01-28T07:20:32.000Z", "updated_at": "2025-06-26T08:26:45.000Z", "deleted_at": null, "code": 0, "output": "NT SERVICE\\TrustedInstaller", "is_inverse": false}, {"id": 807, "created_at": "2022-01-07T07:34:24.000Z", "updated_at": "2025-06-26T08:26:50.000Z", "deleted_at": null, "code": 0, "output": "Enjoy your new privileges: SeDebugPrivilege", "is_inverse": false}, {"id": 1328, "created_at": "2023-05-24T10:18:55.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "AllTheThingsx64.dll", "is_inverse": false}, {"id": 712, "created_at": "2021-08-27T06:15:45.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "mi<PERSON><PERSON><PERSON>(commandline) # coffee", "is_inverse": false}, {"id": 548, "created_at": "2021-03-09T06:51:38.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "FullyQualifiedErrorId", "is_inverse": true}, {"id": 534, "created_at": "2021-03-09T06:47:05.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": -1073741819, "output": "", "is_inverse": false}, {"id": 1015, "created_at": "2022-12-29T10:58:02.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "/bin/cat", "is_inverse": false}, {"id": 1012, "created_at": "2022-12-26T08:07:14.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "midaemon_pcs", "is_inverse": false}, {"id": 3251, "created_at": "2024-07-31T12:00:08.000Z", "updated_at": "2025-03-06T13:26:43.000Z", "deleted_at": null, "code": null, "output": "###", "is_inverse": false}, {"id": 1000, "created_at": "2022-12-22T07:09:56.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "### SCAN COMPLETE ####################################", "is_inverse": false}, {"id": 965, "created_at": "2022-12-09T07:23:56.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "-rwxr-xr-x", "is_inverse": false}, {"id": 964, "created_at": "2022-12-09T07:18:07.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "done", "is_inverse": false}, {"id": 963, "created_at": "2022-12-08T11:35:27.000Z", "updated_at": "2024-07-18T12:01:52.000Z", "deleted_at": null, "code": null, "output": "VGVzdCBNZXNzYWdlIDEyMzEyMzEyMy4uLCEr\nTest Message 123123123..,!+", "is_inverse": false}, {"id": 3294, "created_at": "2024-09-03T20:03:58.000Z", "updated_at": "2025-03-14T07:45:09.000Z", "deleted_at": null, "code": null, "output": "PsExec v2.2 ", "is_inverse": false}, {"id": 3232, "created_at": "2024-07-04T13:01:41.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON><PERSON>", "is_inverse": false}, {"id": 3231, "created_at": "2024-07-04T13:01:41.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "DNSHostName", "is_inverse": false}, {"id": 3245, "created_at": "2024-07-24T14:48:11.000Z", "updated_at": "2024-07-25T13:08:37.000Z", "deleted_at": null, "code": null, "output": "Hello from smux server!", "is_inverse": false}, {"id": 3244, "created_at": "2024-07-24T07:24:10.000Z", "updated_at": "2024-07-25T13:08:37.000Z", "deleted_at": null, "code": null, "output": "\"Gateway\":false}", "is_inverse": false}, {"id": 3243, "created_at": "2024-07-23T12:30:06.000Z", "updated_at": "2024-07-25T13:08:37.000Z", "deleted_at": null, "code": null, "output": "Go-<PERSON> is running...", "is_inverse": false}, {"id": 2905, "created_at": "2024-02-13T08:32:32.000Z", "updated_at": "2024-07-25T13:08:37.000Z", "deleted_at": null, "code": null, "output": "test.txt", "is_inverse": false}, {"id": 2690, "created_at": "2023-12-26T13:38:58.000Z", "updated_at": "2024-07-24T13:07:02.000Z", "deleted_at": null, "code": null, "output": "close all ETW Consumer handles", "is_inverse": false}, {"id": 2689, "created_at": "2023-12-26T13:38:58.000Z", "updated_at": "2024-07-24T13:07:02.000Z", "deleted_at": null, "code": null, "output": "Successfully closed ETW Consumer Handle", "is_inverse": false}, {"id": 1022, "created_at": "2022-12-30T12:09:04.000Z", "updated_at": "2024-07-31T09:38:47.000Z", "deleted_at": null, "code": null, "output": "SELINUX=disabled #SAMPLETEXT", "is_inverse": false}, {"id": 3252, "created_at": "2024-08-01T11:39:51.000Z", "updated_at": "2024-08-02T08:15:06.000Z", "deleted_at": null, "code": null, "output": "/System/Library/", "is_inverse": false}, {"id": 3250, "created_at": "2024-07-31T10:58:08.000Z", "updated_at": "2024-08-02T08:15:06.000Z", "deleted_at": null, "code": null, "output": "release:", "is_inverse": false}, {"id": 3248, "created_at": "2024-07-31T08:50:27.000Z", "updated_at": "2024-08-02T08:15:06.000Z", "deleted_at": null, "code": null, "output": "False\nFalse", "is_inverse": false}, {"id": 3289, "created_at": "2024-08-23T11:37:58.000Z", "updated_at": "2024-08-23T19:14:52.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON>", "is_inverse": false}, {"id": 3197, "created_at": "2024-04-03T12:00:02.000Z", "updated_at": "2024-08-20T07:59:37.000Z", "deleted_at": null, "code": null, "output": "testSpeed.py", "is_inverse": false}, {"id": 3291, "created_at": "2024-08-28T11:19:27.000Z", "updated_at": "2024-08-30T07:40:44.000Z", "deleted_at": null, "code": null, "output": "human2", "is_inverse": false}, {"id": 2761, "created_at": "2024-01-17T13:57:47.000Z", "updated_at": "2024-08-20T07:59:42.000Z", "deleted_at": null, "code": null, "output": "Download successful. File saved to: ", "is_inverse": false}, {"id": 1063, "created_at": "2023-02-10T11:42:46.000Z", "updated_at": "2024-08-27T13:48:00.000Z", "deleted_at": null, "code": null, "output": "ERROR", "is_inverse": true}, {"id": 1021, "created_at": "2022-12-30T10:17:45.000Z", "updated_at": "2024-08-20T07:59:56.000Z", "deleted_at": null, "code": null, "output": "dummys.sh", "is_inverse": false}, {"id": 1016, "created_at": "2022-12-29T13:54:06.000Z", "updated_at": "2024-08-20T08:00:01.000Z", "deleted_at": null, "code": null, "output": "Lindummy.txt", "is_inverse": false}, {"id": 994, "created_at": "2022-12-14T14:25:58.000Z", "updated_at": "2024-08-27T13:48:05.000Z", "deleted_at": null, "code": null, "output": "terminated", "is_inverse": false}, {"id": 993, "created_at": "2022-12-14T14:23:00.000Z", "updated_at": "2024-08-27T13:48:05.000Z", "deleted_at": null, "code": null, "output": "world", "is_inverse": false}, {"id": 649, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2024-08-27T10:44:56.000Z", "deleted_at": null, "code": null, "output": "Ran/parsed the coff", "is_inverse": false}, {"id": 636, "created_at": "2021-06-29T14:01:36.000Z", "updated_at": "2024-08-23T11:39:06.000Z", "deleted_at": null, "code": null, "output": "Info:", "is_inverse": false}, {"id": 552, "created_at": "2021-03-09T06:52:29.000Z", "updated_at": "2024-08-27T08:51:37.000Z", "deleted_at": null, "code": null, "output": "Connecting to: LDAP", "is_inverse": false}, {"id": 3293, "created_at": "2024-09-03T20:03:58.000Z", "updated_at": "2024-09-03T20:17:55.000Z", "deleted_at": null, "code": null, "output": "AngryIPScanner.exe", "is_inverse": false}, {"id": 3253, "created_at": "2024-08-01T14:29:38.000Z", "updated_at": "2024-09-04T13:05:55.000Z", "deleted_at": null, "code": null, "output": "1——-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>————————", "is_inverse": false}, {"id": 2551, "created_at": "2023-11-27T14:29:08.000Z", "updated_at": "2024-09-04T13:08:05.000Z", "deleted_at": null, "code": null, "output": "[+] SID", "is_inverse": false}, {"id": 1325, "created_at": "2023-05-22T13:04:11.000Z", "updated_at": "2024-09-04T13:19:57.000Z", "deleted_at": null, "code": null, "output": "antak.aspx", "is_inverse": false}, {"id": 1324, "created_at": "2023-05-22T13:01:57.000Z", "updated_at": "2024-09-04T13:19:43.000Z", "deleted_at": null, "code": null, "output": "c99.php", "is_inverse": false}, {"id": 1323, "created_at": "2023-05-22T13:00:02.000Z", "updated_at": "2024-09-04T13:19:30.000Z", "deleted_at": null, "code": null, "output": "r57.php", "is_inverse": false}, {"id": 1322, "created_at": "2023-05-22T12:56:53.000Z", "updated_at": "2024-09-04T13:19:00.000Z", "deleted_at": null, "code": null, "output": "pouya.asp", "is_inverse": false}, {"id": 1321, "created_at": "2023-05-22T12:53:47.000Z", "updated_at": "2024-09-04T13:19:16.000Z", "deleted_at": null, "code": null, "output": "b374k.php", "is_inverse": false}, {"id": 1031, "created_at": "2023-01-11T13:35:58.000Z", "updated_at": "2024-09-04T13:11:27.000Z", "deleted_at": null, "code": null, "output": "changenametest", "is_inverse": false}, {"id": 996, "created_at": "2022-12-15T12:22:46.000Z", "updated_at": "2024-09-04T13:13:36.000Z", "deleted_at": null, "code": null, "output": "DATA", "is_inverse": false}, {"id": 995, "created_at": "2022-12-15T10:44:01.000Z", "updated_at": "2024-09-04T13:13:36.000Z", "deleted_at": null, "code": null, "output": "Permission denied", "is_inverse": true}, {"id": 685, "created_at": "2021-08-18T11:38:00.000Z", "updated_at": "2024-08-29T07:47:09.000Z", "deleted_at": null, "code": null, "output": "SERVICE_NAME", "is_inverse": false}, {"id": 683, "created_at": "2021-08-18T11:38:00.000Z", "updated_at": "2024-08-29T07:34:33.000Z", "deleted_at": null, "code": 0, "output": "Great!", "is_inverse": false}, {"id": 643, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2024-08-29T07:13:44.000Z", "deleted_at": null, "code": null, "output": "[+] Injected the", "is_inverse": false}, {"id": 618, "created_at": "2021-03-09T07:07:07.000Z", "updated_at": "2024-08-29T07:08:46.000Z", "deleted_at": null, "code": null, "output": "[+] Clipboard history Contents:", "is_inverse": false}, {"id": 583, "created_at": "2021-03-09T07:02:12.000Z", "updated_at": "2024-08-29T07:47:40.000Z", "deleted_at": null, "code": null, "output": "Junction created for ", "is_inverse": false}, {"id": 572, "created_at": "2021-03-09T06:58:41.000Z", "updated_at": "2024-08-29T07:03:54.000Z", "deleted_at": null, "code": null, "output": "Searching all connected", "is_inverse": false}, {"id": 563, "created_at": "2021-03-09T06:56:23.000Z", "updated_at": "2024-08-29T07:13:55.000Z", "deleted_at": null, "code": 0, "output": "Screenshot captured at", "is_inverse": false}, {"id": 2620, "created_at": "2023-12-15T11:42:06.000Z", "updated_at": "2024-09-09T14:39:18.000Z", "deleted_at": null, "code": null, "output": "[+] Session logoff!", "is_inverse": false}, {"id": 1786, "created_at": "2023-07-31T14:05:29.000Z", "updated_at": "2024-09-09T14:40:07.000Z", "deleted_at": null, "code": null, "output": "[+] Uploading file:", "is_inverse": false}, {"id": 1718, "created_at": "2023-07-26T09:56:51.000Z", "updated_at": "2024-09-09T14:38:59.000Z", "deleted_at": null, "code": null, "output": "[+] Connected to", "is_inverse": false}, {"id": 1526, "created_at": "2023-07-05T12:35:49.000Z", "updated_at": "2024-09-09T14:39:48.000Z", "deleted_at": null, "code": null, "output": "Windows IP Configuration", "is_inverse": false}, {"id": 1523, "created_at": "2023-07-03T12:34:19.000Z", "updated_at": "2024-09-09T14:39:50.000Z", "deleted_at": null, "code": null, "output": "LastWriteTime", "is_inverse": false}, {"id": 1522, "created_at": "2023-07-03T12:34:19.000Z", "updated_at": "2024-09-09T14:39:53.000Z", "deleted_at": null, "code": null, "output": "successfully authenticated", "is_inverse": false}, {"id": 1521, "created_at": "2023-07-03T12:34:19.000Z", "updated_at": "2024-09-09T14:39:56.000Z", "deleted_at": null, "code": null, "output": "Command executed with service", "is_inverse": false}, {"id": 1519, "created_at": "2023-07-03T12:34:19.000Z", "updated_at": "2024-09-09T14:40:02.000Z", "deleted_at": null, "code": -1, "output": "LSA Process is now R/W", "is_inverse": false}, {"id": 1518, "created_at": "2023-07-03T12:34:19.000Z", "updated_at": "2024-09-09T14:38:53.000Z", "deleted_at": null, "code": 0, "output": "ProcessId", "is_inverse": false}, {"id": 1497, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2024-09-09T14:39:23.000Z", "deleted_at": null, "code": null, "output": "[>] Restoring service configuration.", "is_inverse": false}, {"id": 1493, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2024-09-09T14:39:42.000Z", "deleted_at": null, "code": null, "output": "Executind", "is_inverse": false}, {"id": 1491, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2024-09-09T14:38:46.000Z", "deleted_at": null, "code": null, "output": "[*] SAM hashes", "is_inverse": false}, {"id": 831, "created_at": "2022-03-07T07:22:43.000Z", "updated_at": "2024-09-09T14:39:58.000Z", "deleted_at": null, "code": null, "output": "Command executed with process ID", "is_inverse": false}, {"id": 821, "created_at": "2022-02-04T07:39:32.000Z", "updated_at": "2024-09-09T14:38:43.000Z", "deleted_at": null, "code": null, "output": "[CREDENTIAL]", "is_inverse": false}, {"id": 820, "created_at": "2022-02-04T07:39:32.000Z", "updated_at": "2024-09-09T14:38:43.000Z", "deleted_at": null, "code": null, "output": "INFO [*] No ADMIN account on", "is_inverse": true}, {"id": 3212, "created_at": "2024-04-19T13:21:55.000Z", "updated_at": "2024-09-23T08:42:56.000Z", "deleted_at": null, "code": null, "output": "::", "is_inverse": false}, {"id": 744, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2024-09-09T14:39:33.000Z", "deleted_at": null, "code": null, "output": "Successfully killed", "is_inverse": false}, {"id": 743, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2024-09-09T14:39:33.000Z", "deleted_at": null, "code": null, "output": "Results from ps:", "is_inverse": false}, {"id": 742, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2024-09-09T14:39:33.000Z", "deleted_at": null, "code": null, "output": "has been successfully created", "is_inverse": false}, {"id": 741, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2024-09-09T14:40:07.000Z", "deleted_at": null, "code": null, "output": "[+] Results from ls_domain_admins", "is_inverse": false}, {"id": 703, "created_at": "2021-08-24T11:20:27.000Z", "updated_at": "2024-09-09T14:39:15.000Z", "deleted_at": null, "code": null, "output": "execution successful", "is_inverse": false}, {"id": 3310, "created_at": "2024-10-02T10:54:53.000Z", "updated_at": "2024-10-04T14:05:55.000Z", "deleted_at": null, "code": null, "output": "/tmp/Installer", "is_inverse": false}, {"id": 3308, "created_at": "2024-10-01T13:56:31.000Z", "updated_at": "2024-10-01T14:05:14.000Z", "deleted_at": null, "code": null, "output": "123.php", "is_inverse": false}, {"id": 3305, "created_at": "2024-09-30T13:23:56.000Z", "updated_at": "2024-09-30T13:25:03.000Z", "deleted_at": null, "code": null, "output": "cannot", "is_inverse": true}, {"id": 3304, "created_at": "2024-09-28T14:53:48.000Z", "updated_at": "2024-09-28T14:53:48.000Z", "deleted_at": null, "code": null, "output": "All done", "is_inverse": false}, {"id": 3303, "created_at": "2024-09-27T23:35:45.000Z", "updated_at": "2024-09-27T23:37:36.000Z", "deleted_at": null, "code": null, "output": "prc64.exe", "is_inverse": false}, {"id": 3302, "created_at": "2024-09-27T23:20:47.000Z", "updated_at": "2024-09-30T12:06:26.000Z", "deleted_at": null, "code": null, "output": "mimi", "is_inverse": false}, {"id": 991, "created_at": "2022-12-12T14:13:30.000Z", "updated_at": "2024-10-16T11:44:39.000Z", "deleted_at": null, "code": null, "output": "Got <PERSON>", "is_inverse": false}, {"id": 1014, "created_at": "2022-12-26T10:32:56.000Z", "updated_at": "2024-11-25T07:21:39.000Z", "deleted_at": null, "code": null, "output": "TEST PROCESS", "is_inverse": false}, {"id": 1338, "created_at": "2023-05-26T10:45:50.000Z", "updated_at": "2024-12-04T07:46:05.000Z", "deleted_at": null, "code": null, "output": "UserDomain", "is_inverse": false}, {"id": 3290, "created_at": "2024-08-28T08:03:01.000Z", "updated_at": "2025-01-07T11:53:40.000Z", "deleted_at": null, "code": null, "output": "P1CUS4.ldf", "is_inverse": false}, {"id": 2418, "created_at": "2023-11-02T10:07:06.000Z", "updated_at": "2025-05-30T08:35:37.000Z", "deleted_at": null, "code": null, "output": "SspiUacBypass", "is_inverse": false}, {"id": 3296, "created_at": "2024-09-10T11:00:43.000Z", "updated_at": "2025-03-03T16:33:13.000Z", "deleted_at": null, "code": null, "output": "saved to", "is_inverse": false}, {"id": 2163, "created_at": "2023-09-21T07:37:19.000Z", "updated_at": "2024-12-16T13:32:14.000Z", "deleted_at": null, "code": null, "output": "Operation completed successfully", "is_inverse": false}, {"id": 3307, "created_at": "2024-10-01T13:07:49.000Z", "updated_at": "2025-05-15T12:34:50.000Z", "deleted_at": null, "code": null, "output": "Install.command", "is_inverse": false}, {"id": 3247, "created_at": "2024-07-30T07:35:56.000Z", "updated_at": "2025-05-05T14:26:08.000Z", "deleted_at": null, "code": null, "output": "is not auto updateable", "is_inverse": false}, {"id": 1034, "created_at": "2023-01-11T15:22:18.000Z", "updated_at": "2025-05-22T08:18:56.000Z", "deleted_at": null, "code": null, "output": "Found", "is_inverse": false}, {"id": 2914, "created_at": "2024-02-15T09:26:14.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Done", "is_inverse": false}, {"id": 754, "created_at": "2021-11-04T10:31:10.000Z", "updated_at": "2025-02-28T13:10:51.000Z", "deleted_at": null, "code": 0, "output": "Information collected to file", "is_inverse": false}, {"id": 817, "created_at": "2022-02-04T07:39:31.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": 0, "output": "<PERSON>", "is_inverse": false}, {"id": 934, "created_at": "2022-10-17T08:43:43.000Z", "updated_at": "2025-03-18T10:45:17.000Z", "deleted_at": null, "code": null, "output": "Successfull", "is_inverse": false}, {"id": 860, "created_at": "2022-04-13T11:34:50.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": 0, "output": "DomainControllerName", "is_inverse": false}, {"id": 662, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": 0, "output": "Ran/parsed the coff", "is_inverse": false}, {"id": 3306, "created_at": "2024-10-01T08:06:36.000Z", "updated_at": "2025-03-27T11:39:30.000Z", "deleted_at": null, "code": null, "output": "-> [", "is_inverse": false}, {"id": 1068, "created_at": "2023-02-16T11:29:23.000Z", "updated_at": "2025-05-05T14:30:15.000Z", "deleted_at": null, "code": null, "output": "Started Sample Service", "is_inverse": false}, {"id": 3255, "created_at": "2024-08-15T16:04:38.000Z", "updated_at": "2025-05-06T13:27:19.000Z", "deleted_at": null, "code": null, "output": "System", "is_inverse": false}, {"id": 1051, "created_at": "2023-02-03T12:13:13.000Z", "updated_at": "2025-05-21T20:33:49.000Z", "deleted_at": null, "code": null, "output": "root:", "is_inverse": false}, {"id": 625, "created_at": "2021-04-21T14:57:05.000Z", "updated_at": "2025-06-19T21:50:26.000Z", "deleted_at": null, "code": null, "output": "SamAccountName", "is_inverse": false}, {"id": 630, "created_at": "2021-06-29T14:01:35.000Z", "updated_at": "2025-07-02T13:24:36.000Z", "deleted_at": null, "code": null, "output": "adinformation***", "is_inverse": false}, {"id": 5, "created_at": "2021-01-06T15:53:22.000Z", "updated_at": "2025-07-02T09:07:57.000Z", "deleted_at": null, "code": null, "output": "Initializing BloodHound", "is_inverse": false}, {"id": 1390, "created_at": "2023-06-06T14:01:53.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": null, "output": "is greater than 0KB", "is_inverse": false}, {"id": 656, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": null, "output": "REG_SZ", "is_inverse": false}, {"id": 3301, "created_at": "2024-09-27T20:48:36.000Z", "updated_at": "2024-09-27T22:58:32.000Z", "deleted_at": null, "code": null, "output": "WMIEXEC : Share deleted sucess.", "is_inverse": false}, {"id": 3300, "created_at": "2024-09-27T20:25:40.000Z", "updated_at": "2024-09-28T12:32:43.000Z", "deleted_at": null, "code": null, "output": "DC=", "is_inverse": false}, {"id": 3295, "created_at": "2024-09-04T12:28:27.000Z", "updated_at": "2024-09-30T13:25:03.000Z", "deleted_at": null, "code": null, "output": "not digitally signed", "is_inverse": true}, {"id": 3292, "created_at": "2024-09-03T20:03:58.000Z", "updated_at": "2024-09-20T07:17:11.000Z", "deleted_at": null, "code": null, "output": "encrypted_file.bin", "is_inverse": false}, {"id": 3249, "created_at": "2024-07-31T09:22:21.000Z", "updated_at": "2024-09-17T07:29:08.000Z", "deleted_at": null, "code": null, "output": "apple.scriptzxy.plist", "is_inverse": false}, {"id": 3226, "created_at": "2024-06-26T12:55:56.000Z", "updated_at": "2024-09-17T07:29:12.000Z", "deleted_at": null, "code": null, "output": "No such file or directory", "is_inverse": true}, {"id": 3211, "created_at": "2024-04-19T13:21:25.000Z", "updated_at": "2024-09-23T08:42:56.000Z", "deleted_at": null, "code": null, "output": "Decrypting file in memory", "is_inverse": false}, {"id": 3208, "created_at": "2024-04-18T16:47:28.000Z", "updated_at": "2024-09-20T07:17:24.000Z", "deleted_at": null, "code": null, "output": ".4rc3twm", "is_inverse": false}, {"id": 3202, "created_at": "2024-04-16T12:28:17.000Z", "updated_at": "2024-09-20T07:17:31.000Z", "deleted_at": null, "code": null, "output": ".lockbit", "is_inverse": false}, {"id": 2385, "created_at": "2023-11-01T13:37:59.000Z", "updated_at": "2024-09-30T13:25:03.000Z", "deleted_at": null, "code": null, "output": "Unable to connect to the remote server", "is_inverse": true}, {"id": 2205, "created_at": "2023-09-27T13:23:43.000Z", "updated_at": "2024-09-23T08:38:26.000Z", "deleted_at": null, "code": null, "output": "nobody:$ml$$$", "is_inverse": false}, {"id": 2130, "created_at": "2023-09-15T09:58:08.000Z", "updated_at": "2024-09-17T13:08:23.000Z", "deleted_at": null, "code": null, "output": "DUMMY TXT", "is_inverse": true}, {"id": 2129, "created_at": "2023-09-14T14:55:52.000Z", "updated_at": "2024-10-01T13:01:25.000Z", "deleted_at": null, "code": null, "output": "1 added", "is_inverse": false}, {"id": 1852, "created_at": "2023-08-09T11:11:41.000Z", "updated_at": "2024-09-30T13:25:03.000Z", "deleted_at": null, "code": null, "output": "error", "is_inverse": true}, {"id": 1490, "created_at": "2023-06-22T14:49:32.000Z", "updated_at": "2024-09-23T08:43:48.000Z", "deleted_at": null, "code": null, "output": "Username", "is_inverse": false}, {"id": 1387, "created_at": "2023-06-05T06:59:28.000Z", "updated_at": "2024-09-17T07:34:17.000Z", "deleted_at": null, "code": null, "output": "pcstest", "is_inverse": false}, {"id": 1221, "created_at": "2023-05-04T12:31:50.000Z", "updated_at": "2024-09-23T08:46:26.000Z", "deleted_at": null, "code": null, "output": "keychain", "is_inverse": false}, {"id": 1120, "created_at": "2023-04-05T12:10:34.000Z", "updated_at": "2024-09-23T08:38:42.000Z", "deleted_at": null, "code": null, "output": "Unshadowed file saved as", "is_inverse": false}, {"id": 1109, "created_at": "2023-03-21T14:39:56.000Z", "updated_at": "2024-09-23T08:42:15.000Z", "deleted_at": null, "code": null, "output": "* Password", "is_inverse": false}, {"id": 1095, "created_at": "2023-02-28T11:31:08.000Z", "updated_at": "2024-09-23T08:43:59.000Z", "deleted_at": null, "code": null, "output": "^ Mimikatz coffee ASCII art.", "is_inverse": false}, {"id": 1066, "created_at": "2023-02-15T07:39:49.000Z", "updated_at": "2024-09-20T07:17:48.000Z", "deleted_at": null, "code": null, "output": "dummy.txt.clop", "is_inverse": false}, {"id": 3297, "created_at": "2024-09-10T11:40:43.000Z", "updated_at": "2025-06-25T13:03:53.000Z", "deleted_at": null, "code": null, "output": "Overwrote", "is_inverse": false}, {"id": 1029, "created_at": "2023-01-11T12:49:24.000Z", "updated_at": "2024-09-17T07:35:23.000Z", "deleted_at": null, "code": null, "output": "monerodaemon", "is_inverse": false}, {"id": 1024, "created_at": "2023-01-05T15:10:15.000Z", "updated_at": "2024-09-20T07:17:52.000Z", "deleted_at": null, "code": null, "output": "Sample Picus Ransomware Test File. It just a test file. Pls do not remove \nthis poor file!", "is_inverse": false}, {"id": 999, "created_at": "2022-12-16T12:07:25.000Z", "updated_at": "2024-09-17T07:15:14.000Z", "deleted_at": null, "code": null, "output": "dangerous", "is_inverse": false}, {"id": 997, "created_at": "2022-12-15T12:31:03.000Z", "updated_at": "2024-10-01T13:43:06.000Z", "deleted_at": null, "code": null, "output": "Done! Popping shell...", "is_inverse": false}, {"id": 761, "created_at": "2021-11-19T10:22:15.000Z", "updated_at": "2025-03-11T15:05:59.000Z", "deleted_at": null, "code": null, "output": "[!] Done", "is_inverse": false}, {"id": 3299, "created_at": "2024-09-19T08:26:21.000Z", "updated_at": "2025-03-13T12:02:24.000Z", "deleted_at": null, "code": null, "output": "AES state key", "is_inverse": false}, {"id": 1023, "created_at": "2023-01-05T12:49:39.000Z", "updated_at": "2025-03-19T09:57:51.000Z", "deleted_at": null, "code": null, "output": "[+]", "is_inverse": false}, {"id": 3234, "created_at": "2024-07-04T13:01:41.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "Enveloping process completed successfully.", "is_inverse": false}, {"id": 798, "created_at": "2021-12-10T09:20:02.000Z", "updated_at": "2024-09-23T08:40:56.000Z", "deleted_at": null, "code": null, "output": "Successfully dumped forked process", "is_inverse": false}, {"id": 2949, "created_at": "2024-02-20T07:20:29.000Z", "updated_at": "2025-03-21T08:30:59.000Z", "deleted_at": null, "code": null, "output": "Is Encrypted Successfully", "is_inverse": false}, {"id": 811, "created_at": "2022-01-14T08:04:58.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": null, "output": "[+] The scheduled task is deleted.", "is_inverse": false}, {"id": 810, "created_at": "2022-01-14T08:04:58.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": null, "output": "[+] The scheduled task is started.", "is_inverse": false}, {"id": 809, "created_at": "2022-01-14T08:04:58.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": null, "output": "[+] The scheduled task is created.", "is_inverse": false}, {"id": 660, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2024-09-23T08:41:36.000Z", "deleted_at": null, "code": null, "output": "[+] Dumping completed.", "is_inverse": false}, {"id": 659, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2024-09-23T08:41:38.000Z", "deleted_at": null, "code": 0, "output": "reverting back to self", "is_inverse": false}, {"id": 648, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2024-09-23T08:41:43.000Z", "deleted_at": null, "code": null, "output": "[+] Dump successfull! :)", "is_inverse": false}, {"id": 635, "created_at": "2021-06-29T14:01:36.000Z", "updated_at": "2024-09-23T08:41:47.000Z", "deleted_at": null, "code": null, "output": "[+] Base64 Length In Bytes =", "is_inverse": false}, {"id": 627, "created_at": "2021-06-29T14:01:35.000Z", "updated_at": "2024-09-23T08:41:53.000Z", "deleted_at": null, "code": null, "output": "with process ID", "is_inverse": false}, {"id": 617, "created_at": "2021-03-09T07:06:55.000Z", "updated_at": "2024-09-23T08:42:03.000Z", "deleted_at": null, "code": null, "output": "found !!!", "is_inverse": false}, {"id": 604, "created_at": "2021-03-09T07:04:33.000Z", "updated_at": "2024-09-17T07:30:26.000Z", "deleted_at": null, "code": null, "output": "RUNNING", "is_inverse": false}, {"id": 598, "created_at": "2021-03-09T07:04:05.000Z", "updated_at": "2024-09-23T08:42:08.000Z", "deleted_at": null, "code": null, "output": "Exported", "is_inverse": false}, {"id": 586, "created_at": "2021-03-09T07:02:39.000Z", "updated_at": "2024-09-23T08:42:21.000Z", "deleted_at": null, "code": null, "output": "Password", "is_inverse": false}, {"id": 581, "created_at": "2021-03-09T07:01:58.000Z", "updated_at": "2024-09-23T08:42:30.000Z", "deleted_at": null, "code": null, "output": "OK!!!", "is_inverse": false}, {"id": 579, "created_at": "2021-03-09T07:01:26.000Z", "updated_at": "2024-09-23T08:42:24.000Z", "deleted_at": null, "code": null, "output": "IuoUYrAi", "is_inverse": false}, {"id": 568, "created_at": "2021-03-09T06:57:26.000Z", "updated_at": "2024-09-23T08:42:36.000Z", "deleted_at": null, "code": null, "output": "1 file(s) copied", "is_inverse": false}, {"id": 528, "created_at": "2021-03-09T06:44:53.000Z", "updated_at": "2024-09-23T08:41:56.000Z", "deleted_at": null, "code": null, "output": "Failed", "is_inverse": true}, {"id": 526, "created_at": "2021-03-09T06:44:49.000Z", "updated_at": "2024-09-23T08:43:51.000Z", "deleted_at": null, "code": 0, "output": "Dump succesful", "is_inverse": false}, {"id": 519, "created_at": "2021-03-09T06:42:01.000Z", "updated_at": "2024-09-20T07:18:31.000Z", "deleted_at": null, "code": -1, "output": "encryptor", "is_inverse": false}, {"id": 511, "created_at": "2021-03-09T06:38:06.000Z", "updated_at": "2024-09-20T07:24:07.000Z", "deleted_at": null, "code": 0, "output": "unatten", "is_inverse": false}, {"id": 510, "created_at": "2021-03-09T06:38:00.000Z", "updated_at": "2024-09-20T07:24:11.000Z", "deleted_at": null, "code": 0, "output": "syspre", "is_inverse": false}, {"id": 3841, "created_at": "2025-05-22T11:59:19.000Z", "updated_at": "2025-05-23T07:12:47.000Z", "deleted_at": null, "code": null, "output": "[!] Done", "is_inverse": false}, {"id": 39, "created_at": "2021-01-21T12:18:34.000Z", "updated_at": "2024-09-20T07:18:35.000Z", "deleted_at": null, "code": null, "output": "encryptor", "is_inverse": false}, {"id": 3313, "created_at": "2024-10-15T07:40:14.000Z", "updated_at": "2024-10-15T07:40:47.000Z", "deleted_at": null, "code": null, "output": "Connectivity OK.", "is_inverse": false}, {"id": 3312, "created_at": "2024-10-15T07:39:56.000Z", "updated_at": "2024-10-15T07:40:47.000Z", "deleted_at": null, "code": null, "output": "Unexpected success, but OK.", "is_inverse": false}, {"id": 3311, "created_at": "2024-10-15T07:39:14.000Z", "updated_at": "2024-10-15T07:40:47.000Z", "deleted_at": null, "code": null, "output": "Unable to connect to the remote server", "is_inverse": false}, {"id": 3309, "created_at": "2024-10-02T08:47:49.000Z", "updated_at": "2024-10-15T14:06:04.000Z", "deleted_at": null, "code": null, "output": "Hello from Picus", "is_inverse": false}, {"id": 2909, "created_at": "2024-02-13T13:08:48.000Z", "updated_at": "2024-10-14T11:17:24.000Z", "deleted_at": null, "code": null, "output": "[TLS][!] Entry Point Is Patched With", "is_inverse": false}, {"id": 1452, "created_at": "2023-06-16T14:05:48.000Z", "updated_at": "2024-10-14T12:30:41.000Z", "deleted_at": null, "code": null, "output": "[+] Execution complete", "is_inverse": false}, {"id": 1052, "created_at": "2023-02-06T08:15:57.000Z", "updated_at": "2025-04-03T14:12:17.000Z", "deleted_at": null, "code": 0, "output": "The command completed successfully.", "is_inverse": false}, {"id": 1103, "created_at": "2023-03-10T13:33:14.000Z", "updated_at": "2024-10-14T12:50:55.000Z", "deleted_at": null, "code": null, "output": "AMSI still patched", "is_inverse": false}, {"id": 2759, "created_at": "2024-01-17T12:05:06.000Z", "updated_at": "2025-07-10T14:25:06.000Z", "deleted_at": null, "code": null, "output": "Sample Service from Picus", "is_inverse": false}, {"id": 3298, "created_at": "2024-09-17T08:35:53.000Z", "updated_at": "2025-05-23T12:47:07.000Z", "deleted_at": null, "code": 0, "output": "TestSnapshot.dat", "is_inverse": false}, {"id": 3775, "created_at": "2025-05-15T12:18:11.000Z", "updated_at": "2025-05-20T12:41:09.000Z", "deleted_at": null, "code": null, "output": "HIDDEN", "is_inverse": false}, {"id": 1116, "created_at": "2023-03-29T10:42:47.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "Done!", "is_inverse": false}, {"id": 1552, "created_at": "2023-07-14T10:44:34.000Z", "updated_at": "2024-11-29T07:18:02.000Z", "deleted_at": null, "code": 0, "output": "Inveigh 2.0.10 [Started", "is_inverse": false}, {"id": 1256, "created_at": "2023-05-12T13:44:10.000Z", "updated_at": "2024-11-28T14:25:04.000Z", "deleted_at": null, "code": null, "output": "VM21-6-8.log", "is_inverse": false}, {"id": 1189, "created_at": "2023-05-02T07:35:31.000Z", "updated_at": "2024-11-28T14:22:13.000Z", "deleted_at": null, "code": null, "output": "[*] Minidump generated in", "is_inverse": false}, {"id": 704, "created_at": "2021-08-24T11:20:27.000Z", "updated_at": "2025-04-18T07:58:23.000Z", "deleted_at": null, "code": null, "output": "success", "is_inverse": false}, {"id": 3206, "created_at": "2024-04-17T11:58:03.000Z", "updated_at": "2025-05-21T20:43:56.000Z", "deleted_at": null, "code": null, "output": "\"UserName\": \"picususer\"", "is_inverse": false}, {"id": 1107, "created_at": "2023-03-21T14:03:14.000Z", "updated_at": "2025-05-23T12:47:13.000Z", "deleted_at": null, "code": null, "output": "BANG BANG", "is_inverse": false}, {"id": 1337, "created_at": "2023-05-26T09:11:42.000Z", "updated_at": "2024-12-04T10:29:36.000Z", "deleted_at": null, "code": null, "output": "[+] Dump successful!", "is_inverse": false}, {"id": 1089, "created_at": "2023-02-28T07:08:20.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "Hash NTLM:", "is_inverse": false}, {"id": 2552, "created_at": "2023-11-29T14:07:47.000Z", "updated_at": "2025-05-30T08:34:20.000Z", "deleted_at": null, "code": null, "output": "[+] AMSI Patched:", "is_inverse": false}, {"id": 3879, "created_at": "2025-05-30T11:57:03.000Z", "updated_at": "2025-07-17T08:27:08.000Z", "deleted_at": null, "code": null, "output": "Not an IoT device", "is_inverse": false}, {"id": 3160, "created_at": "2024-03-26T13:07:26.000Z", "updated_at": "2025-02-28T11:21:58.000Z", "deleted_at": null, "code": null, "output": "dump.dmp", "is_inverse": false}, {"id": 3022, "created_at": "2024-03-01T12:16:36.000Z", "updated_at": "2025-05-21T20:41:52.000Z", "deleted_at": null, "code": null, "output": "PicusXdgAutoStart", "is_inverse": false}, {"id": 3021, "created_at": "2024-03-01T12:15:19.000Z", "updated_at": "2025-05-21T20:41:52.000Z", "deleted_at": null, "code": null, "output": "directory exist", "is_inverse": false}, {"id": 3774, "created_at": "2025-05-15T07:02:39.000Z", "updated_at": "2025-05-15T12:34:50.000Z", "deleted_at": null, "code": null, "output": "EXIST", "is_inverse": false}, {"id": 3877, "created_at": "2025-05-29T07:49:39.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "Failed to scan with error code 0x80070103. Reason: No more data is available.", "is_inverse": false}, {"id": 2204, "created_at": "2023-09-26T14:30:56.000Z", "updated_at": "2025-05-29T12:54:58.000Z", "deleted_at": null, "code": 0, "output": "successfully", "is_inverse": false}, {"id": 990, "created_at": "2022-12-12T14:13:29.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "GetDriveTypeW", "is_inverse": false}, {"id": 953, "created_at": "2022-11-28T06:14:20.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "[+] Now ImagePathName", "is_inverse": false}, {"id": 936, "created_at": "2022-10-31T05:22:25.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "[+] AMSI patched !!", "is_inverse": false}, {"id": 933, "created_at": "2022-10-17T08:43:42.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "Couldn't get a handle", "is_inverse": true}, {"id": 932, "created_at": "2022-10-17T08:43:42.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "[+] Success count: 4", "is_inverse": false}, {"id": 862, "created_at": "2022-04-22T08:39:09.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": null, "output": "Build Engine version", "is_inverse": false}, {"id": 2986, "created_at": "2024-02-26T09:30:37.000Z", "updated_at": "2025-07-18T11:47:16.000Z", "deleted_at": null, "code": null, "output": "[+] Generic Password Record", "is_inverse": false}, {"id": 2519, "created_at": "2023-11-23T14:04:42.000Z", "updated_at": "2025-05-30T08:34:30.000Z", "deleted_at": null, "code": null, "output": "Done...", "is_inverse": false}, {"id": 2518, "created_at": "2023-11-21T13:51:51.000Z", "updated_at": "2025-05-30T08:34:57.000Z", "deleted_at": null, "code": null, "output": "<PERSON>", "is_inverse": false}, {"id": 561, "created_at": "2021-03-09T06:55:52.000Z", "updated_at": "2025-05-30T08:33:48.000Z", "deleted_at": null, "code": null, "output": "[+] Done", "is_inverse": false}, {"id": 3878, "created_at": "2025-05-29T08:52:53.000Z", "updated_at": "2025-06-03T08:33:22.000Z", "deleted_at": null, "code": null, "output": "Decrypted AES key", "is_inverse": false}, {"id": 1332, "created_at": "2023-05-24T10:22:07.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "Extraction was successful", "is_inverse": false}, {"id": 605, "created_at": "2021-03-09T07:04:34.000Z", "updated_at": "2025-06-25T06:43:53.000Z", "deleted_at": null, "code": null, "output": "STOPPED", "is_inverse": false}, {"id": 4189, "created_at": "2025-07-23T12:30:25.000Z", "updated_at": "2025-07-23T12:30:25.000Z", "deleted_at": null, "code": null, "output": "TinyShell", "is_inverse": false}, {"id": 873, "created_at": "2022-07-06T12:18:53.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "Error", "is_inverse": true}, {"id": 714, "created_at": "2021-09-14T10:55:14.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "Success", "is_inverse": false}, {"id": 4188, "created_at": "2025-07-23T07:42:13.000Z", "updated_at": "2025-07-23T08:59:18.000Z", "deleted_at": null, "code": null, "output": "DMSAs:", "is_inverse": false}, {"id": 3318, "created_at": "2024-10-23T13:04:56.000Z", "updated_at": "2024-10-23T13:46:59.000Z", "deleted_at": null, "code": null, "output": "File encrypted successfully!", "is_inverse": false}, {"id": 3316, "created_at": "2024-10-18T09:32:26.000Z", "updated_at": "2024-10-18T11:43:50.000Z", "deleted_at": null, "code": null, "output": "rustive.dmp", "is_inverse": false}, {"id": 3315, "created_at": "2024-10-17T14:03:56.000Z", "updated_at": "2024-10-17T14:28:48.000Z", "deleted_at": null, "code": null, "output": "Access is denied", "is_inverse": true}, {"id": 3314, "created_at": "2024-10-17T14:03:28.000Z", "updated_at": "2024-10-17T14:03:28.000Z", "deleted_at": null, "code": null, "output": "denied", "is_inverse": true}, {"id": 833, "created_at": "2022-03-07T07:22:44.000Z", "updated_at": "2024-10-23T08:25:58.000Z", "deleted_at": null, "code": null, "output": "404", "is_inverse": false}, {"id": 3324, "created_at": "2024-11-07T14:33:18.000Z", "updated_at": "2024-11-07T14:50:05.000Z", "deleted_at": null, "code": null, "output": "Combined total count", "is_inverse": false}, {"id": 3323, "created_at": "2024-11-07T14:33:18.000Z", "updated_at": "2024-11-07T14:50:05.000Z", "deleted_at": null, "code": null, "output": "Entry Type:", "is_inverse": false}, {"id": 3322, "created_at": "2024-11-07T14:33:18.000Z", "updated_at": "2024-11-07T14:50:05.000Z", "deleted_at": null, "code": null, "output": "Global mutex created successfully.", "is_inverse": false}, {"id": 3321, "created_at": "2024-11-07T14:33:18.000Z", "updated_at": "2024-11-07T14:50:05.000Z", "deleted_at": null, "code": null, "output": "Started from file '", "is_inverse": false}, {"id": 3320, "created_at": "2024-11-01T09:43:39.000Z", "updated_at": "2024-11-01T10:06:36.000Z", "deleted_at": null, "code": null, "output": "Got Hash!", "is_inverse": false}, {"id": 3319, "created_at": "2024-10-25T09:36:12.000Z", "updated_at": "2024-10-31T06:49:35.000Z", "deleted_at": null, "code": null, "output": "f.elif", "is_inverse": false}, {"id": 3317, "created_at": "2024-10-23T13:04:56.000Z", "updated_at": "2024-11-07T14:48:14.000Z", "deleted_at": null, "code": null, "output": "Enabled            : True", "is_inverse": false}, {"id": 3246, "created_at": "2024-07-25T08:01:01.000Z", "updated_at": "2024-11-05T08:18:07.000Z", "deleted_at": null, "code": null, "output": "Running CONNECT scan with non root privileges", "is_inverse": false}, {"id": 961, "created_at": "2022-12-05T06:41:02.000Z", "updated_at": "2024-10-31T12:59:31.000Z", "deleted_at": null, "code": null, "output": "service stopped", "is_inverse": false}, {"id": 960, "created_at": "2022-12-05T06:41:02.000Z", "updated_at": "2024-10-31T12:59:31.000Z", "deleted_at": null, "code": null, "output": "System process token", "is_inverse": false}, {"id": 959, "created_at": "2022-12-05T06:41:02.000Z", "updated_at": "2024-10-31T12:59:31.000Z", "deleted_at": null, "code": null, "output": "[+] 'RTCore64' service started", "is_inverse": false}, {"id": 597, "created_at": "2021-03-09T07:03:41.000Z", "updated_at": "2024-11-08T13:12:05.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON><PERSON><PERSON>(powershell)", "is_inverse": false}, {"id": 696, "created_at": "2021-08-18T11:38:02.000Z", "updated_at": "2024-11-11T18:28:56.000Z", "deleted_at": null, "code": null, "output": "[*] LogonId:", "is_inverse": false}, {"id": 676, "created_at": "2021-08-18T11:37:59.000Z", "updated_at": "2024-11-12T10:41:14.000Z", "deleted_at": null, "code": null, "output": "[+] authresult 0", "is_inverse": false}, {"id": 650, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2024-11-12T11:45:55.000Z", "deleted_at": null, "code": null, "output": "[+] *** Exploit successful.", "is_inverse": false}, {"id": 646, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2024-11-12T12:01:26.000Z", "deleted_at": null, "code": null, "output": "User name                    FooBar", "is_inverse": false}, {"id": 641, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2024-11-12T12:11:24.000Z", "deleted_at": null, "code": null, "output": "hTargetWnd == 0", "is_inverse": true}, {"id": 612, "created_at": "2021-03-09T07:05:51.000Z", "updated_at": "2024-11-12T12:56:58.000Z", "deleted_at": null, "code": null, "output": "[!] You should have 'full control' permission on folders/files within the target folder", "is_inverse": false}, {"id": 521, "created_at": "2021-03-09T06:42:21.000Z", "updated_at": "2024-11-12T13:53:39.000Z", "deleted_at": null, "code": 0, "output": "NT AUTHORITY\\SYSTEM", "is_inverse": false}, {"id": 520, "created_at": "2021-03-09T06:42:20.000Z", "updated_at": "2024-11-12T13:53:39.000Z", "deleted_at": null, "code": 0, "output": "SeDebugPrivilege", "is_inverse": false}, {"id": 2910, "created_at": "2024-02-13T14:42:49.000Z", "updated_at": "2025-05-22T08:04:18.000Z", "deleted_at": null, "code": null, "output": "Root window id", "is_inverse": false}, {"id": 585, "created_at": "2021-03-09T07:02:31.000Z", "updated_at": "2025-07-03T11:34:01.000Z", "deleted_at": null, "code": null, "output": "True", "is_inverse": false}, {"id": 628, "created_at": "2021-06-29T14:01:35.000Z", "updated_at": "2024-11-25T12:37:55.000Z", "deleted_at": null, "code": 0, "output": "nt authority\\system", "is_inverse": false}, {"id": 1389, "created_at": "2023-06-06T10:22:45.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": null, "output": "permissiondenied", "is_inverse": true}, {"id": 608, "created_at": "2021-03-09T07:04:48.000Z", "updated_at": "2024-11-19T10:10:45.000Z", "deleted_at": null, "code": null, "output": "Image", "is_inverse": false}, {"id": 931, "created_at": "2022-10-17T08:43:42.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "Volume Size", "is_inverse": false}, {"id": 3625, "created_at": "2025-03-27T11:46:44.000Z", "updated_at": "2025-03-27T11:49:08.000Z", "deleted_at": null, "code": null, "output": "SharpChrome completed in", "is_inverse": false}, {"id": 828, "created_at": "2022-02-25T07:58:23.000Z", "updated_at": "2025-03-18T08:14:43.000Z", "deleted_at": null, "code": 0, "output": "PsProtectedSignerWinTcb", "is_inverse": false}, {"id": 3626, "created_at": "2025-03-28T10:41:36.000Z", "updated_at": "2025-03-28T12:10:58.000Z", "deleted_at": null, "code": null, "output": "Inappropriate", "is_inverse": true}, {"id": 1334, "created_at": "2023-05-24T10:23:43.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": null, "output": "jsc.js", "is_inverse": false}, {"id": 1030, "created_at": "2023-01-11T13:25:19.000Z", "updated_at": "2025-05-14T11:43:26.000Z", "deleted_at": null, "code": null, "output": "Started Monero Service", "is_inverse": false}, {"id": 1132, "created_at": "2023-04-23T16:21:57.000Z", "updated_at": "2025-03-21T11:23:28.000Z", "deleted_at": null, "code": null, "output": "Picus", "is_inverse": false}, {"id": 877, "created_at": "2022-07-06T14:57:48.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": null, "output": "FullyQualifiedErrorId :", "is_inverse": true}, {"id": 702, "created_at": "2021-08-24T11:20:27.000Z", "updated_at": "2025-07-18T15:07:02.000Z", "deleted_at": null, "code": null, "output": "The operation completed successfully.", "is_inverse": false}, {"id": 20, "created_at": "2021-01-11T15:08:11.000Z", "updated_at": "2024-12-03T14:05:58.000Z", "deleted_at": null, "code": null, "output": "The operation completed successfully", "is_inverse": false}, {"id": 3288, "created_at": "2024-08-23T07:38:26.000Z", "updated_at": "2025-06-30T13:04:32.000Z", "deleted_at": null, "code": null, "output": "nt authority\\system", "is_inverse": false}, {"id": 689, "created_at": "2021-08-18T11:38:01.000Z", "updated_at": "2025-06-18T08:49:18.000Z", "deleted_at": null, "code": null, "output": " Dump count reached.", "is_inverse": false}, {"id": 958, "created_at": "2022-12-01T15:31:07.000Z", "updated_at": "2024-11-21T12:18:36.000Z", "deleted_at": null, "code": null, "output": "winver", "is_inverse": false}, {"id": 688, "created_at": "2021-08-18T11:38:01.000Z", "updated_at": "2025-06-30T13:09:39.000Z", "deleted_at": null, "code": null, "output": "The command completed successfully", "is_inverse": false}, {"id": 3327, "created_at": "2024-11-22T08:04:20.000Z", "updated_at": "2024-11-22T14:35:14.000Z", "deleted_at": null, "code": null, "output": "\\SYSTEM", "is_inverse": false}, {"id": 889, "created_at": "2022-09-02T13:43:30.000Z", "updated_at": "2025-05-29T13:00:44.000Z", "deleted_at": null, "code": 0, "output": "<PERSON><PERSON><PERSON><PERSON>(commandline)", "is_inverse": false}, {"id": 797, "created_at": "2021-12-10T09:20:02.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "fork.bin", "is_inverse": false}, {"id": 1011, "created_at": "2022-12-26T07:50:37.000Z", "updated_at": "2024-11-25T07:19:27.000Z", "deleted_at": null, "code": null, "output": "1471229795", "is_inverse": false}, {"id": 1010, "created_at": "2022-12-23T20:39:56.000Z", "updated_at": "2024-11-25T07:17:46.000Z", "deleted_at": null, "code": null, "output": "fopen hooked", "is_inverse": false}, {"id": 1005, "created_at": "2022-12-23T12:24:14.000Z", "updated_at": "2024-11-25T07:14:03.000Z", "deleted_at": null, "code": null, "output": "No such file or directory", "is_inverse": false}, {"id": 1004, "created_at": "2022-12-23T11:58:31.000Z", "updated_at": "2024-11-25T07:12:16.000Z", "deleted_at": null, "code": null, "output": "dummy", "is_inverse": true}, {"id": 1003, "created_at": "2022-12-23T11:13:17.000Z", "updated_at": "2024-11-25T07:10:13.000Z", "deleted_at": null, "code": null, "output": "test.file", "is_inverse": true}, {"id": 957, "created_at": "2022-12-01T14:39:16.000Z", "updated_at": "2024-11-25T07:24:21.000Z", "deleted_at": null, "code": 0, "output": "df3a831a805ada51ce56e32a46a07b51", "is_inverse": false}, {"id": 955, "created_at": "2022-12-01T14:26:40.000Z", "updated_at": "2024-11-25T07:24:21.000Z", "deleted_at": null, "code": 0, "output": "560771", "is_inverse": false}, {"id": 3661, "created_at": "2025-04-11T10:56:19.000Z", "updated_at": "2025-04-11T11:33:41.000Z", "deleted_at": null, "code": null, "output": "C:\\ProgramData\\RndFileName.dmp exist.", "is_inverse": false}, {"id": 3325, "created_at": "2024-11-14T07:01:10.000Z", "updated_at": "2024-12-06T10:40:34.000Z", "deleted_at": null, "code": null, "output": "callback.elf", "is_inverse": false}, {"id": 716, "created_at": "2021-09-14T10:55:14.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "Finished! Check the contents of", "is_inverse": false}, {"id": 3660, "created_at": "2025-04-11T09:25:37.000Z", "updated_at": "2025-04-11T13:06:50.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON> created successfully", "is_inverse": false}, {"id": 947, "created_at": "2022-11-11T14:56:20.000Z", "updated_at": "2025-05-30T08:36:06.000Z", "deleted_at": null, "code": null, "output": "[+] Process created", "is_inverse": false}, {"id": 596, "created_at": "2021-03-09T07:03:34.000Z", "updated_at": "2025-05-23T12:47:13.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON><PERSON><PERSON>(commandline)", "is_inverse": false}, {"id": 3659, "created_at": "2025-04-11T08:57:14.000Z", "updated_at": "2025-04-11T12:11:39.000Z", "deleted_at": null, "code": null, "output": "C:\\ProgramData\\SkyPDF\\PDUDrv.blf exist.", "is_inverse": false}, {"id": 3624, "created_at": "2025-03-17T13:52:59.000Z", "updated_at": "2025-03-19T09:57:51.000Z", "deleted_at": null, "code": null, "output": "0 successes", "is_inverse": true}, {"id": 3623, "created_at": "2025-03-17T12:59:19.000Z", "updated_at": "2025-03-19T09:57:51.000Z", "deleted_at": null, "code": null, "output": "Saving ticket in", "is_inverse": false}, {"id": 3622, "created_at": "2025-03-17T12:53:44.000Z", "updated_at": "2025-03-19T09:57:51.000Z", "deleted_at": null, "code": null, "output": "SessionError", "is_inverse": true}, {"id": 1753, "created_at": "2023-07-31T12:28:03.000Z", "updated_at": "2025-03-18T08:51:13.000Z", "deleted_at": null, "code": null, "output": "Analysis done", "is_inverse": false}, {"id": 956, "created_at": "2022-12-01T14:31:41.000Z", "updated_at": "2024-12-09T12:09:58.000Z", "deleted_at": null, "code": 0, "output": "test", "is_inverse": false}, {"id": 699, "created_at": "2021-08-20T08:08:11.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": 0, "output": "Hash", "is_inverse": false}, {"id": 1453, "created_at": "2023-06-16T14:05:48.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "System Locale:", "is_inverse": false}, {"id": 693, "created_at": "2021-08-18T11:38:02.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "Certify completed in", "is_inverse": false}, {"id": 3055, "created_at": "2024-03-06T15:39:05.000Z", "updated_at": "2024-12-12T14:55:15.000Z", "deleted_at": null, "code": null, "output": "winver.exe", "is_inverse": false}, {"id": 938, "created_at": "2022-10-31T05:22:25.000Z", "updated_at": "2024-12-12T13:48:53.000Z", "deleted_at": null, "code": 0, "output": "calc", "is_inverse": false}, {"id": 3326, "created_at": "2024-11-18T13:50:59.000Z", "updated_at": "2025-01-22T07:19:19.000Z", "deleted_at": null, "code": null, "output": "AGOVAccess", "is_inverse": false}, {"id": 3254, "created_at": "2024-08-09T07:44:57.000Z", "updated_at": "2025-07-04T08:11:57.000Z", "deleted_at": null, "code": null, "output": "nt authority\\local service", "is_inverse": false}, {"id": 763, "created_at": "2021-11-19T10:22:15.000Z", "updated_at": "2025-05-30T08:37:14.000Z", "deleted_at": null, "code": null, "output": "SUCCESS:", "is_inverse": false}, {"id": 1104, "created_at": "2023-03-21T11:31:48.000Z", "updated_at": "2025-03-19T10:08:50.000Z", "deleted_at": null, "code": null, "output": "COMPLETED", "is_inverse": false}, {"id": 602, "created_at": "2021-03-09T07:04:21.000Z", "updated_at": "2025-07-02T13:25:31.000Z", "deleted_at": null, "code": null, "output": "[+] Screenshot successful", "is_inverse": false}, {"id": 1056, "created_at": "2023-02-07T08:32:10.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "Remote", "is_inverse": false}, {"id": 1055, "created_at": "2023-02-07T08:30:36.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "IPC$", "is_inverse": false}, {"id": 679, "created_at": "2021-08-18T11:37:59.000Z", "updated_at": "2025-02-26T11:54:19.000Z", "deleted_at": null, "code": null, "output": "[*] All done. GetLastError: 0", "is_inverse": false}, {"id": 1054, "created_at": "2023-02-07T08:29:40.000Z", "updated_at": "2025-03-18T08:14:44.000Z", "deleted_at": null, "code": null, "output": "C$", "is_inverse": false}, {"id": 1013, "created_at": "2022-12-26T08:59:32.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "dummy", "is_inverse": false}, {"id": 935, "created_at": "2022-10-24T06:33:57.000Z", "updated_at": "2025-06-26T08:26:10.000Z", "deleted_at": null, "code": null, "output": "[+] Process spawned!", "is_inverse": false}, {"id": 593, "created_at": "2021-03-09T07:03:18.000Z", "updated_at": "2025-02-21T12:48:20.000Z", "deleted_at": null, "code": null, "output": "Success! DC can be fully compromised by a Zerologon attack.", "is_inverse": false}, {"id": 832, "created_at": "2022-03-07T07:22:43.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "1 file(s) copied.", "is_inverse": false}, {"id": 720, "created_at": "2021-09-23T09:52:26.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": -1, "output": "", "is_inverse": true}, {"id": 1115, "created_at": "2023-03-29T10:42:47.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Files read from disk: 6", "is_inverse": false}, {"id": 3699, "created_at": "2025-04-24T13:11:44.000Z", "updated_at": "2025-04-24T13:11:44.000Z", "deleted_at": null, "code": null, "output": "webdata.db", "is_inverse": false}, {"id": 3698, "created_at": "2025-04-24T13:11:35.000Z", "updated_at": "2025-04-24T13:11:35.000Z", "deleted_at": null, "code": null, "output": "LoginData.db", "is_inverse": false}, {"id": 882, "created_at": "2022-07-29T08:40:04.000Z", "updated_at": "2025-05-06T12:02:21.000Z", "deleted_at": null, "code": null, "output": "otepad.exe", "is_inverse": false}, {"id": 638, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2025-06-26T08:27:09.000Z", "deleted_at": null, "code": null, "output": "Created thread:", "is_inverse": false}, {"id": 700, "created_at": "2021-08-23T11:20:57.000Z", "updated_at": "2025-07-04T10:17:33.000Z", "deleted_at": null, "code": null, "output": "OK", "is_inverse": false}, {"id": 527, "created_at": "2021-03-09T06:44:53.000Z", "updated_at": "2025-06-26T08:27:05.000Z", "deleted_at": null, "code": null, "output": "nt authority\\system", "is_inverse": false}, {"id": 694, "created_at": "2021-08-18T11:38:02.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "System Level Integrity Process", "is_inverse": false}, {"id": 494, "created_at": "2021-03-09T06:31:42.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 1, "output": "", "is_inverse": false}, {"id": 12, "created_at": "2021-01-07T08:50:20.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "", "is_inverse": false}, {"id": 870, "created_at": "2022-06-17T08:39:36.000Z", "updated_at": "2025-03-26T13:41:32.000Z", "deleted_at": null, "code": null, "output": "* Username :", "is_inverse": false}, {"id": 536, "created_at": "2021-03-09T06:49:47.000Z", "updated_at": "2025-04-03T14:12:17.000Z", "deleted_at": null, "code": null, "output": "successfully", "is_inverse": false}, {"id": 3695, "created_at": "2025-04-24T08:54:00.000Z", "updated_at": "2025-04-25T11:06:54.000Z", "deleted_at": null, "code": null, "output": "{'uuid':", "is_inverse": false}, {"id": 2279, "created_at": "2023-10-10T06:17:17.000Z", "updated_at": "2025-05-21T20:37:29.000Z", "deleted_at": null, "code": null, "output": "samba: command not found", "is_inverse": true}, {"id": 2243, "created_at": "2023-10-03T14:52:33.000Z", "updated_at": "2025-05-22T08:03:45.000Z", "deleted_at": null, "code": null, "output": "some", "is_inverse": false}, {"id": 3876, "created_at": "2025-05-27T18:48:35.000Z", "updated_at": "2025-05-28T11:09:36.000Z", "deleted_at": null, "code": null, "output": "INFECTED", "is_inverse": false}, {"id": 3875, "created_at": "2025-05-27T07:50:45.000Z", "updated_at": "2025-05-27T08:50:50.000Z", "deleted_at": null, "code": null, "output": "\"test\"", "is_inverse": false}, {"id": 3874, "created_at": "2025-05-27T06:52:54.000Z", "updated_at": "2025-05-27T06:52:54.000Z", "deleted_at": null, "code": null, "output": "Connection from", "is_inverse": false}, {"id": 2485, "created_at": "2023-11-17T12:40:37.000Z", "updated_at": "2025-05-30T08:40:58.000Z", "deleted_at": null, "code": null, "output": "Scheduled task has been created", "is_inverse": false}, {"id": 2452, "created_at": "2023-11-09T14:28:57.000Z", "updated_at": "2025-05-30T08:35:10.000Z", "deleted_at": null, "code": null, "output": "Ticket requests and renewals:", "is_inverse": false}, {"id": 1026, "created_at": "2023-01-06T13:18:36.000Z", "updated_at": "2025-05-30T08:39:03.000Z", "deleted_at": null, "code": null, "output": "[+] [HELL HALL] pAddress", "is_inverse": false}, {"id": 948, "created_at": "2022-11-11T14:56:20.000Z", "updated_at": "2025-05-30T08:36:06.000Z", "deleted_at": null, "code": null, "output": "nt authority", "is_inverse": false}, {"id": 840, "created_at": "2022-04-01T07:21:09.000Z", "updated_at": "2025-05-30T08:40:07.000Z", "deleted_at": null, "code": null, "output": "[+] All done!", "is_inverse": false}, {"id": 668, "created_at": "2021-06-29T14:01:39.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": null, "output": "mi<PERSON><PERSON><PERSON>(powershell) # coffee", "is_inverse": false}, {"id": 4155, "created_at": "2025-07-18T11:54:01.000Z", "updated_at": "2025-07-18T11:54:01.000Z", "deleted_at": null, "code": null, "output": "==> Checking for", "is_inverse": false}, {"id": 926, "created_at": "2022-10-03T06:18:08.000Z", "updated_at": "2024-11-28T14:13:10.000Z", "deleted_at": null, "code": null, "output": "1$a$$ Dl_lmp in", "is_inverse": false}, {"id": 891, "created_at": "2022-09-19T06:14:02.000Z", "updated_at": "2024-11-28T14:07:32.000Z", "deleted_at": null, "code": null, "output": "Memory dump written", "is_inverse": false}, {"id": 890, "created_at": "2022-09-12T06:14:28.000Z", "updated_at": "2024-11-28T14:05:59.000Z", "deleted_at": null, "code": null, "output": "[*] Creating memory dump", "is_inverse": false}, {"id": 887, "created_at": "2022-08-24T14:18:00.000Z", "updated_at": "2024-11-27T07:29:50.000Z", "deleted_at": null, "code": null, "output": "Dump successful!", "is_inverse": false}, {"id": 881, "created_at": "2022-07-29T08:40:04.000Z", "updated_at": "2024-11-28T14:09:24.000Z", "deleted_at": null, "code": null, "output": "Dumpfile successfully created!", "is_inverse": false}, {"id": 878, "created_at": "2022-07-08T07:42:46.000Z", "updated_at": "2024-11-28T13:04:51.000Z", "deleted_at": null, "code": null, "output": "[SUCCESS] All data dumped", "is_inverse": false}, {"id": 759, "created_at": "2021-11-12T06:55:35.000Z", "updated_at": "2024-11-28T11:03:18.000Z", "deleted_at": null, "code": null, "output": " logon sessions", "is_inverse": false}, {"id": 3159, "created_at": "2024-03-26T08:12:30.000Z", "updated_at": "2024-12-04T10:26:50.000Z", "deleted_at": null, "code": null, "output": "gnome-shell-ext", "is_inverse": false}, {"id": 3059, "created_at": "2024-03-08T10:05:49.000Z", "updated_at": "2024-12-04T10:30:00.000Z", "deleted_at": null, "code": null, "output": "data.bz2", "is_inverse": false}, {"id": 2318, "created_at": "2023-10-18T10:18:26.000Z", "updated_at": "2024-12-04T10:26:50.000Z", "deleted_at": null, "code": null, "output": "record.wav", "is_inverse": false}, {"id": 1045, "created_at": "2023-01-26T15:14:09.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "MsCacheV2", "is_inverse": false}, {"id": 886, "created_at": "2022-08-19T12:35:18.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": 0, "output": "[*] Input username", "is_inverse": false}, {"id": 884, "created_at": "2022-08-14T10:30:17.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "successful", "is_inverse": false}, {"id": 879, "created_at": "2022-07-08T07:42:46.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": 0, "output": "[*] Finished processing.", "is_inverse": false}, {"id": 792, "created_at": "2021-12-03T10:52:14.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "Deleting ", "is_inverse": false}, {"id": 762, "created_at": "2021-11-19T10:22:15.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "Done,", "is_inverse": false}, {"id": 749, "created_at": "2021-10-21T10:08:07.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "[+] Lsass dump is complete", "is_inverse": false}, {"id": 673, "created_at": "2021-06-29T14:01:40.000Z", "updated_at": "2025-02-21T13:23:57.000Z", "deleted_at": null, "code": null, "output": "[+] Done!", "is_inverse": false}, {"id": 1124, "created_at": "2023-04-06T08:47:12.000Z", "updated_at": "2024-12-04T10:33:44.000Z", "deleted_at": null, "code": null, "output": "commands will be executed", "is_inverse": false}, {"id": 1123, "created_at": "2023-04-06T08:47:03.000Z", "updated_at": "2024-12-04T10:33:44.000Z", "deleted_at": null, "code": null, "output": "at_test_file.txt", "is_inverse": false}, {"id": 1102, "created_at": "2023-02-28T13:09:21.000Z", "updated_at": "2024-12-04T10:34:43.000Z", "deleted_at": null, "code": null, "output": "Scanning 1 hosts", "is_inverse": false}, {"id": 1071, "created_at": "2023-02-23T09:27:43.000Z", "updated_at": "2024-12-04T10:36:19.000Z", "deleted_at": null, "code": null, "output": "file", "is_inverse": false}, {"id": 1059, "created_at": "2023-02-07T13:28:45.000Z", "updated_at": "2024-12-04T10:36:00.000Z", "deleted_at": null, "code": null, "output": "EDITED_STRING", "is_inverse": false}, {"id": 1058, "created_at": "2023-02-07T13:28:37.000Z", "updated_at": "2024-12-04T10:36:00.000Z", "deleted_at": null, "code": null, "output": "PicusSamH", "is_inverse": false}, {"id": 1027, "created_at": "2023-01-09T12:21:03.000Z", "updated_at": "2024-12-04T10:36:39.000Z", "deleted_at": null, "code": null, "output": "0.0.0.0:19999", "is_inverse": false}, {"id": 1019, "created_at": "2022-12-29T14:29:47.000Z", "updated_at": "2024-12-04T10:40:35.000Z", "deleted_at": null, "code": null, "output": "Disabled", "is_inverse": false}, {"id": 1018, "created_at": "2022-12-29T14:29:36.000Z", "updated_at": "2024-12-04T10:40:35.000Z", "deleted_at": null, "code": null, "output": "Permissive", "is_inverse": false}, {"id": 1017, "created_at": "2022-12-29T14:29:28.000Z", "updated_at": "2024-12-04T10:40:35.000Z", "deleted_at": null, "code": null, "output": "Enforcing", "is_inverse": false}, {"id": 3364, "created_at": "2024-12-06T13:07:47.000Z", "updated_at": "2024-12-06T13:31:21.000Z", "deleted_at": null, "code": null, "output": "InterestingProcesses", "is_inverse": false}, {"id": 3982, "created_at": "2025-06-19T07:31:32.000Z", "updated_at": "2025-06-20T13:37:28.000Z", "deleted_at": null, "code": null, "output": "Received: hello picus", "is_inverse": false}, {"id": 1000001, "created_at": "2024-12-17T09:28:35.607Z", "updated_at": "2024-12-17T09:28:35.607Z", "deleted_at": null, "code": null, "output": "111", "is_inverse": false}, {"id": 998, "created_at": "2022-12-16T08:28:32.000Z", "updated_at": "2025-05-22T08:16:26.000Z", "deleted_at": null, "code": null, "output": "No such file or directory", "is_inverse": true}, {"id": 3700, "created_at": "2025-04-24T14:29:31.000Z", "updated_at": "2025-05-06T12:41:17.000Z", "deleted_at": null, "code": null, "output": "AnyDesk.app", "is_inverse": false}, {"id": 3694, "created_at": "2025-04-21T12:12:00.000Z", "updated_at": "2025-05-06T13:19:30.000Z", "deleted_at": null, "code": null, "output": "keychain:", "is_inverse": false}, {"id": 3430, "created_at": "2024-12-26T07:45:13.000Z", "updated_at": "2024-12-27T11:19:11.000Z", "deleted_at": null, "code": null, "output": "[INFO] Found ", "is_inverse": false}, {"id": 3463, "created_at": "2025-01-07T07:06:27.000Z", "updated_at": "2025-01-07T12:35:40.000Z", "deleted_at": null, "code": null, "output": "picus1.txt", "is_inverse": false}, {"id": 3464, "created_at": "2025-01-09T14:41:25.000Z", "updated_at": "2025-01-10T14:50:08.000Z", "deleted_at": null, "code": null, "output": "--------------[Opened new Window]--------------", "is_inverse": false}, {"id": 3497, "created_at": "2025-01-22T07:17:13.000Z", "updated_at": "2025-01-22T07:17:13.000Z", "deleted_at": null, "code": null, "output": "install_flash_player_osx", "is_inverse": false}, {"id": 3533, "created_at": "2025-01-24T12:48:06.000Z", "updated_at": "2025-01-24T12:51:07.000Z", "deleted_at": null, "code": null, "output": "Listening on port", "is_inverse": false}, {"id": 3500, "created_at": "2025-01-24T08:51:33.000Z", "updated_at": "2025-01-24T12:51:07.000Z", "deleted_at": null, "code": null, "output": "Listening on UDP port", "is_inverse": false}, {"id": 3499, "created_at": "2025-01-23T13:51:35.000Z", "updated_at": "2025-01-24T12:51:07.000Z", "deleted_at": null, "code": null, "output": "UserAgent 2019", "is_inverse": false}, {"id": 3361, "created_at": "2024-12-05T15:08:22.000Z", "updated_at": "2025-05-06T12:07:10.000Z", "deleted_at": null, "code": null, "output": "c:\\", "is_inverse": false}, {"id": 3360, "created_at": "2024-12-05T15:08:22.000Z", "updated_at": "2025-05-06T12:07:10.000Z", "deleted_at": null, "code": null, "output": "File Not Found", "is_inverse": false}, {"id": 3534, "created_at": "2025-02-03T12:09:24.000Z", "updated_at": "2025-02-05T08:05:09.000Z", "deleted_at": null, "code": null, "output": "ChromeInstaller.command", "is_inverse": false}, {"id": 3573, "created_at": "2025-02-11T13:54:36.000Z", "updated_at": "2025-02-11T13:54:36.000Z", "deleted_at": null, "code": null, "output": "file not found", "is_inverse": false}, {"id": 3572, "created_at": "2025-02-11T13:51:08.000Z", "updated_at": "2025-02-11T13:51:08.000Z", "deleted_at": null, "code": null, "output": "", "is_inverse": true}, {"id": 3571, "created_at": "2025-02-11T13:44:27.000Z", "updated_at": "2025-02-11T13:44:27.000Z", "deleted_at": null, "code": null, "output": "delete-self.exe", "is_inverse": true}, {"id": 3574, "created_at": "2025-02-12T16:07:42.000Z", "updated_at": "2025-02-12T16:09:48.000Z", "deleted_at": null, "code": null, "output": ":8444", "is_inverse": false}, {"id": 952, "created_at": "2022-11-18T12:29:10.000Z", "updated_at": "2025-05-07T10:49:14.000Z", "deleted_at": null, "code": null, "output": "API Keys Regex", "is_inverse": false}, {"id": 3537, "created_at": "2025-02-05T10:24:48.000Z", "updated_at": "2025-02-14T13:20:19.000Z", "deleted_at": null, "code": null, "output": "antsword.jsp", "is_inverse": false}, {"id": 3535, "created_at": "2025-02-03T13:36:43.000Z", "updated_at": "2025-02-14T13:20:19.000Z", "deleted_at": null, "code": null, "output": "icesword.jsp", "is_inverse": false}, {"id": 3575, "created_at": "2025-02-13T11:15:26.000Z", "updated_at": "2025-02-17T06:41:27.000Z", "deleted_at": null, "code": null, "output": "disable<PERSON><PERSON><PERSON><PERSON>", "is_inverse": false}, {"id": 1130, "created_at": "2023-04-17T12:04:22.000Z", "updated_at": "2025-05-08T07:21:07.000Z", "deleted_at": null, "code": null, "output": "picustestfile.txt", "is_inverse": false}, {"id": 7, "created_at": "2021-01-06T15:55:49.000Z", "updated_at": "2025-06-25T13:10:24.000Z", "deleted_at": null, "code": 0, "output": "", "is_inverse": false}, {"id": 624, "created_at": "2021-04-20T13:16:11.000Z", "updated_at": "2025-07-11T14:50:57.000Z", "deleted_at": null, "code": null, "output": "Unrestricted", "is_inverse": false}, {"id": 925, "created_at": "2022-09-27T15:45:58.000Z", "updated_at": "2025-02-21T12:48:20.000Z", "deleted_at": null, "code": null, "output": "Authentication Id", "is_inverse": false}, {"id": 1028, "created_at": "2023-01-09T12:22:54.000Z", "updated_at": "2025-03-19T09:57:51.000Z", "deleted_at": null, "code": null, "output": "Connection refused", "is_inverse": true}, {"id": 3608, "created_at": "2025-02-25T11:27:14.000Z", "updated_at": "2025-02-25T11:34:17.000Z", "deleted_at": null, "code": null, "output": "[+] Job's done! Press Enter to continue.", "is_inverse": false}, {"id": 677, "created_at": "2021-08-18T11:37:59.000Z", "updated_at": "2025-02-26T13:20:24.000Z", "deleted_at": null, "code": null, "output": "Kernelmode debugging", "is_inverse": false}, {"id": 3613, "created_at": "2025-02-28T15:26:57.000Z", "updated_at": "2025-02-28T15:26:57.000Z", "deleted_at": null, "code": null, "output": "Linux", "is_inverse": false}, {"id": 3610, "created_at": "2025-02-28T11:52:40.000Z", "updated_at": "2025-02-28T11:52:40.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON><PERSON>_host", "is_inverse": false}, {"id": 3222, "created_at": "2024-06-07T07:54:47.000Z", "updated_at": "2025-02-28T12:09:24.000Z", "deleted_at": null, "code": null, "output": "Desktop vertical height (in pixels):", "is_inverse": false}, {"id": 752, "created_at": "2021-10-21T10:08:08.000Z", "updated_at": "2025-02-28T13:10:51.000Z", "deleted_at": null, "code": null, "output": "All <PERSON><PERSON> finished.", "is_inverse": false}, {"id": 3611, "created_at": "2025-02-28T11:52:56.000Z", "updated_at": "2025-03-03T12:28:29.000Z", "deleted_at": null, "code": null, "output": "Rekoobe", "is_inverse": false}, {"id": 3397, "created_at": "2024-12-17T12:40:17.000Z", "updated_at": "2025-03-27T13:59:05.000Z", "deleted_at": null, "code": null, "output": "Device ID:", "is_inverse": false}, {"id": 3363, "created_at": "2024-12-06T06:57:25.000Z", "updated_at": "2025-03-27T13:55:32.000Z", "deleted_at": null, "code": null, "output": "] Writable:", "is_inverse": false}, {"id": 3612, "created_at": "2025-02-28T12:09:05.000Z", "updated_at": "2025-03-28T12:10:58.000Z", "deleted_at": null, "code": null, "output": "LISTEN", "is_inverse": false}, {"id": 3808, "created_at": "2025-05-20T13:08:46.000Z", "updated_at": "2025-05-20T13:08:46.000Z", "deleted_at": null, "code": null, "output": "nsenter: setns(): can't reassociate to namespace 'ipc': Operation not permitted", "is_inverse": true}, {"id": 3702, "created_at": "2025-04-25T10:43:31.000Z", "updated_at": "2025-04-25T10:44:16.000Z", "deleted_at": null, "code": null, "output": "SelfForward", "is_inverse": false}, {"id": 3701, "created_at": "2025-04-24T19:46:49.000Z", "updated_at": "2025-04-25T11:06:54.000Z", "deleted_at": null, "code": null, "output": "pay", "is_inverse": false}, {"id": 3697, "created_at": "2025-04-24T13:10:19.000Z", "updated_at": "2025-04-25T11:06:54.000Z", "deleted_at": null, "code": null, "output": "is completed!", "is_inverse": false}, {"id": 3696, "created_at": "2025-04-24T11:15:14.000Z", "updated_at": "2025-04-25T11:06:54.000Z", "deleted_at": null, "code": null, "output": "Chrome & Browser are terminated", "is_inverse": false}, {"id": 3498, "created_at": "2025-01-23T13:11:45.000Z", "updated_at": "2025-04-25T11:06:54.000Z", "deleted_at": null, "code": null, "output": "cpu_info=", "is_inverse": false}, {"id": 3736, "created_at": "2025-04-28T17:54:12.000Z", "updated_at": "2025-05-01T13:50:34.000Z", "deleted_at": null, "code": null, "output": "intel_audio.ko", "is_inverse": false}, {"id": 3735, "created_at": "2025-04-28T15:13:22.000Z", "updated_at": "2025-05-01T14:05:42.000Z", "deleted_at": null, "code": null, "output": "pong", "is_inverse": false}, {"id": 3536, "created_at": "2025-02-04T14:04:43.000Z", "updated_at": "2025-05-05T14:26:05.000Z", "deleted_at": null, "code": null, "output": "Done for", "is_inverse": false}, {"id": 1033, "created_at": "2023-01-11T14:49:45.000Z", "updated_at": "2025-05-05T14:29:46.000Z", "deleted_at": null, "code": null, "output": "Started bioset Service", "is_inverse": false}, {"id": 1997, "created_at": "2023-09-08T09:39:45.000Z", "updated_at": "2025-05-21T20:35:47.000Z", "deleted_at": null, "code": null, "output": "has successfully loaded", "is_inverse": false}, {"id": 1995, "created_at": "2023-09-06T16:32:47.000Z", "updated_at": "2025-05-21T20:35:03.000Z", "deleted_at": null, "code": null, "output": "[+] Password found !!!", "is_inverse": false}, {"id": 1992, "created_at": "2023-09-05T12:00:16.000Z", "updated_at": "2025-05-21T20:33:03.000Z", "deleted_at": null, "code": null, "output": "checksum", "is_inverse": false}, {"id": 1991, "created_at": "2023-09-05T12:00:00.000Z", "updated_at": "2025-05-21T20:33:03.000Z", "deleted_at": null, "code": null, "output": "places.sqlite", "is_inverse": false}, {"id": 1001, "created_at": "2022-12-22T12:57:58.000Z", "updated_at": "2025-05-21T20:39:40.000Z", "deleted_at": null, "code": null, "output": "Not found grsecurity", "is_inverse": true}, {"id": 1070, "created_at": "2023-02-22T10:17:15.000Z", "updated_at": "2025-05-27T15:06:23.000Z", "deleted_at": null, "code": null, "output": "root", "is_inverse": false}, {"id": 1524, "created_at": "2023-07-04T10:16:20.000Z", "updated_at": "2025-05-22T08:11:21.000Z", "deleted_at": null, "code": null, "output": "Driver=usb-storage", "is_inverse": false}, {"id": 1025, "created_at": "2023-01-06T09:15:14.000Z", "updated_at": "2025-05-22T08:04:01.000Z", "deleted_at": null, "code": null, "output": "/bin/pwd: ELF 64-bit", "is_inverse": false}, {"id": 2198, "created_at": "2023-09-25T13:51:26.000Z", "updated_at": "2025-06-04T12:56:07.000Z", "deleted_at": null, "code": null, "output": "[+] 0 passwords have been found.", "is_inverse": true}, {"id": 1020, "created_at": "2022-12-30T09:00:30.000Z", "updated_at": "2025-06-04T12:56:12.000Z", "deleted_at": null, "code": null, "output": "PRIVATE KEY", "is_inverse": false}, {"id": 3946, "created_at": "2025-06-10T12:32:47.000Z", "updated_at": "2025-06-12T14:52:15.000Z", "deleted_at": null, "code": null, "output": "CPU ID:", "is_inverse": false}, {"id": 3945, "created_at": "2025-06-10T12:09:57.000Z", "updated_at": "2025-06-12T14:52:15.000Z", "deleted_at": null, "code": null, "output": "Serial Number:", "is_inverse": false}, {"id": 3981, "created_at": "2025-06-17T14:32:22.000Z", "updated_at": "2025-06-20T13:38:39.000Z", "deleted_at": null, "code": null, "output": "eTrader.app", "is_inverse": false}, {"id": 823, "created_at": "2022-02-18T08:20:54.000Z", "updated_at": "2025-06-12T11:13:08.000Z", "deleted_at": null, "code": null, "output": "[+] Shellcode is executed successfully", "is_inverse": false}, {"id": 674, "created_at": "2021-06-29T14:01:40.000Z", "updated_at": "2025-06-12T11:14:03.000Z", "deleted_at": null, "code": null, "output": "This is a dummy text from dummy.dll", "is_inverse": false}, {"id": 645, "created_at": "2021-06-29T14:01:38.000Z", "updated_at": "2025-06-12T11:14:03.000Z", "deleted_at": null, "code": null, "output": "Delaying remote thread", "is_inverse": false}, {"id": 620, "created_at": "2021-03-09T07:07:25.000Z", "updated_at": "2025-06-12T11:13:08.000Z", "deleted_at": null, "code": 0, "output": "", "is_inverse": true}, {"id": 3985, "created_at": "2025-06-20T09:13:45.000Z", "updated_at": "2025-06-20T13:35:59.000Z", "deleted_at": null, "code": null, "output": "Video Devices:", "is_inverse": false}, {"id": 3984, "created_at": "2025-06-20T07:12:52.000Z", "updated_at": "2025-06-20T13:37:14.000Z", "deleted_at": null, "code": null, "output": "= 1e9b459db058019aa310cb05ceab6ef6", "is_inverse": false}, {"id": 3983, "created_at": "2025-06-19T07:31:44.000Z", "updated_at": "2025-06-20T13:37:28.000Z", "deleted_at": null, "code": null, "output": "Received: hello p<PERSON>er", "is_inverse": false}, {"id": 3362, "created_at": "2024-12-05T15:08:23.000Z", "updated_at": "2025-06-26T07:47:40.000Z", "deleted_at": null, "code": null, "output": "Admin Privileges:", "is_inverse": false}, {"id": 745, "created_at": "2021-10-14T11:22:45.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "Successful Count: 5", "is_inverse": false}, {"id": 3609, "created_at": "2025-02-27T07:01:44.000Z", "updated_at": "2025-06-27T14:25:20.000Z", "deleted_at": null, "code": null, "output": "displayName", "is_inverse": false}, {"id": 4018, "created_at": "2025-06-23T14:00:44.000Z", "updated_at": "2025-06-24T10:07:11.000Z", "deleted_at": null, "code": null, "output": "Logon", "is_inverse": false}, {"id": 736, "created_at": "2021-10-07T17:29:31.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "PRIVILEGES INFORMATION", "is_inverse": false}, {"id": 4052, "created_at": "2025-07-01T08:30:43.000Z", "updated_at": "2025-07-02T09:07:57.000Z", "deleted_at": null, "code": null, "output": "Process ID:", "is_inverse": false}, {"id": 721, "created_at": "2021-09-30T08:50:22.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 0, "output": "[*] Encrypting heap with XOR", "is_inverse": false}, {"id": 711, "created_at": "2021-08-24T11:20:29.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "started", "is_inverse": false}, {"id": 4051, "created_at": "2025-07-01T07:54:12.000Z", "updated_at": "2025-07-01T07:54:12.000Z", "deleted_at": null, "code": null, "output": "[!] Detection done!", "is_inverse": false}, {"id": 1000002, "created_at": "2025-07-04T06:41:07.670Z", "updated_at": "2025-07-04T06:41:07.670Z", "deleted_at": null, "code": 0, "output": "123", "is_inverse": false}, {"id": 691, "created_at": "2021-08-18T11:38:02.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "Coldfire Framework Ends", "is_inverse": false}, {"id": 639, "created_at": "2021-06-29T14:01:37.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "System Information", "is_inverse": false}, {"id": 493, "created_at": "2021-03-09T06:31:42.000Z", "updated_at": "2025-07-16T11:01:07.000Z", "deleted_at": null, "code": 0, "output": "", "is_inverse": false}, {"id": 570, "created_at": "2021-03-09T06:58:29.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "has been created", "is_inverse": false}, {"id": 543, "created_at": "2021-03-09T06:51:08.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "<PERSON><PERSON><PERSON><PERSON>(commandline) #", "is_inverse": false}, {"id": 497, "created_at": "2021-03-09T06:31:59.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": -1, "output": "", "is_inverse": false}, {"id": 37, "created_at": "2021-01-21T11:26:35.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "-decode command completed successfully.", "is_inverse": false}, {"id": 30, "created_at": "2021-01-19T08:38:01.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": null, "output": "SUCCESS", "is_inverse": false}, {"id": 18, "created_at": "2021-01-08T15:12:04.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": 5, "output": "", "is_inverse": false}, {"id": 3570, "created_at": "2025-02-11T12:54:19.000Z", "updated_at": "2025-07-23T13:26:09.000Z", "deleted_at": null, "code": 2, "output": "", "is_inverse": true}, {"id": 4, "created_at": "2021-01-06T15:40:07.000Z", "updated_at": "2025-07-04T12:23:26.000Z", "deleted_at": null, "code": -1, "output": "", "is_inverse": false}]}