{"action": {"name": "(自定义规则)主机命令行 - Gather Information about Target Domain using ADRecon - 4", "desc": "In this action, an attacker is trying together information about the target domain by using ADRecon tool.[blackcat_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "ADRecon.ps1", "file_owner": "system", "file_transfer_library": 998002297, "randomize_file": null, "file_name": "ADRecon.ps1"}], "steps": [{"step_order": 1, "command": "powershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Unrestricted", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ADRecon.ps1'; \"& 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ADRecon.ps1' -OutputType 'CSV' -OutputDir 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ADRecon-Report';\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "powershell.exe -c \"$files = Get-ChildItem -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ADRecon-Report*' -Recurse | Where-Object { $_.Length -gt 0KB }; if ($files) { $files | ForEach-Object { Write-Output \\\"\"$($_.Name) is greater than 0KB\\\"\" } } else { Write-Output 'There is a glitch in the Matrix' }\"\n", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "is\\ greater\\ than\\ 0KB", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1018"], "mitre_mitigation": []}}, "picus_id": 10550, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Possible Active Directory Enumeration via PowerShell", "ruleId": "5845", "description": "Detects attempts to load kerberos.dll from powershell.exe. This technique is commonly utilized for enumeration active directory and discovery.", "releaseDate": 1625788800000, "updateDate": 1625788800000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Possible Active Directory Enumeration via PowerShell\nstatus: stable\ndescription: Detects attempts to load kerberos.dll from powershell.exe. This technique is commonly utilized for enumeration active directory and discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/techniques/T1087/\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 7\n        Image|endswith:\n            - 'powershell.exe'\n        ImageLoaded|endswith:\n            - 'kerberos.dll'\n    condition: selection\nfalsepositives: \n    - Legitimate administrative activities\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.ta0007\n    - attack.t1087", "logSource": {"productName": "windows", "service": "sysmon", "policies": null}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}]}, {"ruleName": "LDAP Access via PowerShell", "ruleId": "4067", "description": "Detects the attempt to use LDAP based functions via PowerShell. This technique is commonly utilized for enumeration to victim environment.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: LDAP Access via PowerShell\nstatus: experimental\ndescription: Detects the attempt to use LDAP based functions via PowerShell. This technique is commonly utilized for enumeration to victim environment.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1087/002/\n    - https://attack.mitre.org/tactics/TA0007/\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: 4104\n        Message|contains:\n            - 'LDAP://'\n    condition: selection\nfalsepositives: \n    - Administrative tools/scripts\n    - Legitimate administrative activities \nlevel: medium\ntags:\n    - attack.discovery\n    - attack.t1087.002\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Administrative tools/scripts", "Legitimate administrative activities"], "mitre": [{"name": "Domain Account", "mitreId": "T1087.002", "url": "https://attack.mitre.org/techniques/T1087/002/", "type": "sub_technique"}]}, {"ruleName": "Gathering Credential Access via PowerShell Script", "ruleId": "2262", "description": "Detects the attempt to gather credential access via PowerShell script. This technique is commonly utilized for execution.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Gathering Credential Access via PowerShell Script\nstatus: experimental\ndescription: Detects the attempt to gather credential access via PowerShell script. This technique is commonly utilized for execution.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1056/002/\n    - https://attack.mitre.org/tactics/TA0006/\n    - https://github.com/securemode/Invoke-Apex/blob/master/modules/Invoke-Creds.ps1\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: \n            - 4103\n            - 4104\n        Message|contains|all:\n            - 'username'\n            - 'password'\n            - 'GetNetworkCredential'\n    condition: selection\nfalsepositives: \n    - Penetration testing\nlevel: high\ntags:\n    - attack.credential_access\n    - attack.t1056.002\n    - attack.ta0006", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Penetration testing"], "mitre": [{"name": "GUI Input Capture", "mitreId": "T1056.002", "url": "https://attack.mitre.org/techniques/T1056/002/", "type": "sub_technique"}]}, {"ruleName": "Execution of C# Compiler", "ruleId": "3707", "description": "Detects execution of C# compiler (csc.exe). Some payloads are dropped as source code. This technique is commonly utilized for defense evasion.", "releaseDate": 1558742400000, "updateDate": 1664755200000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Execution of C# Compiler\nstatus: experimental\ndescription: Detects execution of C# compiler (csc.exe). Some payloads are dropped as source code. This technique is commonly utilized for defense evasion.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1027/004/\n   - https://attack.mitre.org/tactics/TA0005/\n   - https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/compiler-options/command-line-building-with-csc-exe\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n   selection:\n        EventID: 4688\n        NewProcessName: '*\\csc.exe'\n   condition: selection\nfalsepositives:\n    - Software installations/updates\nlevel: medium\ntags:\n    - attack.defense_evasion\n    - attack.t1027.004\n    - attack.ta0005", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Software installations/updates"], "mitre": [{"name": "Compile After Delivery", "mitreId": "T1027.004", "url": "https://attack.mitre.org/techniques/T1027/004/", "type": "sub_technique"}]}]}, "uuid": "cc2ab50a-f555-4cee-973e-15b68c64aeb8", "sigma_device_query": ""}