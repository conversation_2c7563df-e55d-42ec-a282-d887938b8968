{"action": {"name": "(自定义规则)主机命令行 - Discover Network Shares on the Lateral Target via Invoke-ShareFinder - 8", "desc": "In this action, an attacker is trying to execute Invoke-ShareFinder script to discover network shares on the lateral target.[bumblebee_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "PowerView.ps1", "file_owner": "system", "file_transfer_library": 998002336, "randomize_file": null, "file_name": "PowerView.ps1"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\PowerView.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\PowerView.ps1'; Invoke-ShareFinder -ComputerName 127.0.0.1\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Remote", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1135"], "mitre_mitigation": ["ATT&CK:M1028"]}}, "picus_id": 23065, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Suspicious PowerView Tool Function Calls", "ruleId": "8075", "description": "Detects the attempt to execute PowerView tool which is used by adversaries to enumarate network, domains and users.", "releaseDate": 1570233600000, "updateDate": 1664755200000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Suspicious PowerView Tool Function Calls\nstatus: experimental\ndescription: Detects the attempt to execute PowerView tool which is used by adversaries to enumarate network, domains and users.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1087/\n   - https://attack.mitre.org/techniques/T1561/002\n   - https://attack.mitre.org/techniques/T1135/   \n   - https://attack.mitre.org/tactics/TA0007/\n   - https://github.com/PowerShellEmpire/PowerTools/tree/ba37b01c478e9009e5fc4ccccc7f50c0aa7d9f24\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: \n            - 4103\n            - 4104\n    domaintrust:    \n        Message|contains|all:\n            - 'Get-NetDomainTrust'\n            - 'Get-NetForestTrust'\n    net_functions:\n        Message|contains|all:\n            - 'Get-NetDomainController'\n            - 'Get-NetUser'\n            - 'Get-NetSession'\n    misc:\n        Message|contains|all:\n            - 'Invoke-CheckLocalAdminAccess'\n            - 'Get-DomainPolicy'        \n    condition: selection and (domaintrust or net_functions or misc)\nfalsepositives:\n    - Penetration testing\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.t1087\n    - attack.t1561.002\n    - attack.t1135\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Penetration testing"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}, {"name": "Network Share Discovery", "mitreId": "T1135", "url": "https://attack.mitre.org/techniques/T1135/", "type": "technique"}, {"name": "Disk Structure Wipe", "mitreId": "T1561.002", "url": "https://attack.mitre.org/techniques/T1561/002/", "type": "sub_technique"}]}]}, "uuid": "077dbb15-f55e-4a06-b498-dcf612d2b858"}