{"action": {"name": "(自定义规则)主机命令行 - Disable the Windows Firewall with All Profiles Settings - 9", "desc": "In this action, the netsh command is using to disable the windows firewall with all profiles settings.[apt_c_34_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "netsh advfirewall set allprofiles state off", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1562"], "mitre_mitigation": ["ATT&CK:M1038", "ATT&CK:M1054", "ATT&CK:M1024", "ATT&CK:M1018", "ATT&CK:M1047", "ATT&CK:M1022"]}}, "picus_id": 26783, "uuid": "91405ad4-095a-4e8a-92ef-5899fba8666b", "sigma_device_query": "title: Windows Firewall Deactivation\nstatus: stable\ndescription: Detects the disablement of Windows Firewall. Adversaries and malwares mostly use this method to permit or control communication.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1562/004/\n   - https://attack.mitre.org/tactics/TA0005/\n   - https://techgenix.com/quicklyturnonoffwindowsfirewallusingcommandline/\nlogsource:\n    product: windows\n    service: windows-advanced-security-firewall\ndetection:\n    selection:\n        EventID: 2003\n        Type: 'Enable Windows Defender Firewall'  \n        Value: 'No'  \n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: medium\ntags:\n    - attack.defense_evasion\n    - attack.t1562.004\n    - attack.ta0005"}