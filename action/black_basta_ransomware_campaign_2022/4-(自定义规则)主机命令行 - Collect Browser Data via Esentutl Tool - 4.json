{"action": {"name": "(自定义规则)主机命令行 - Collect Browser Data via Esentutl Tool - 4", "desc": "In this action, an attacker is try to collect browser data from Internet Explorer and Microsoft Edge via the esentutl tool.[black_basta_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c esentutl.exe /r V01 /l\"C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\WebCache\" /s\"C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\WebCache\" /d\"C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\WebCache\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Initiating\\ RECOVERY\\ mode", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Collection", "ATT&CK:T1005"], "mitre_mitigation": ["ATT&CK:M1057"]}}, "picus_id": 21771, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Gathering Browser Information via Esentutl", "ruleId": "6181", "description": "Adversaries steals sensitive information is by extracting browser data from Internet Explorer and Microsoft Edge by using the esentutl tool.", "releaseDate": 1670198400000, "updateDate": 1670371200000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Gathering Browser Information via Esentutl\nstatus: experimental\ndescription: Adversaries steals sensitive information is by extracting browser data from Internet Explorer and Microsoft Edge by using the esentutl tool.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0009/\n   - https://attack.mitre.org/techniques/T1005/\n   - https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-r2-and-2012/hh875546(v=ws.11)\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName|endswith: '\\esentutl.exe'\n        ProcessCommandLine|contains:\n            - '/r'\n            - '/y'\n            - '/vss'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: high\ntags:\n    - attack.collection\n    - attack.ta0009\n    - attack.t1005", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Data from Local System", "mitreId": "T1005", "url": "https://attack.mitre.org/techniques/T1005/", "type": "technique"}]}]}, "uuid": "25a48e4f-da87-4c5e-a5d8-9895cc0a0978", "sigma_device_query": ""}