{"actions": {"78bb658a-54fb-431f-8f72-ec8c3e25c147": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 2, "uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "ver": 16, "vid": "382255", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 加载并隐藏受感染的驱动程序", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略，监控可疑命令执行；针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略。中长期建议：加强应用程序的代码签名检测；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图在受害者系统中加载并隐藏受感染的驱动程序。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:42", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "mitre_mitigation", "tag": "M1045", "tag_en": "M1045", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "mitre_techniques", "tag": "T1554 - 篡改客户端二进制文件", "tag_en": "T1554 - Compromise Host Software Binary", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "78bb658a-54fb-431f-8f72-ec8c3e25c147", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1045"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1554", "ATT&CK:Persistence"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "rootkit_load.bat", "file_name": "rootkit_load.bat", "file_owner": "system", "file_transfer_library": 999051138, "file_type": "batch", "filesize": 215, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "da0cab9420b07d66ae51d7114ce4fdf4", "sha1sum": "f02fefc05b4c292794a94a13a5e07de187d125a7", "sha256sum": "e8ab235693ab99ca3195065e8fa29076758cfa50c49dda99cb1906994a6b73ae"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "DrvHide.sys", "file_name": "DrvHide.sys", "file_owner": "system", "file_transfer_library": 999051139, "file_type": "bin", "filesize": 6400, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "f2b903076d01f078ecf1a6d98cb4681f", "sha1sum": "8541f9465ac77a911b732cb0a8f4062bad18cdf6", "sha256sum": "4c3bce3a12cb696019f8c6bb3b45b80e8e21e51f12e0c0b0a46527a38888cd53"}], "monitor_connections": "off", "raw_text": "cd \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\" & echo Y | rootkit_load.bat\r\nsc.exe query DrvHide\r\nsc.exe delete DrvHide & del /F /Q C:\\Windows\\system32\\drivers\\DrvHide.sys & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "cd C:\\Users\\<USER>\\Documents\\{{v_action_id}} && echo Y | rootkit_load.bat", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "sc.exe query DrvHide", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "sc.exe stop DrvHide", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "sc.exe delete DrvHide", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del /F /Q C:\\Windows\\system32\\drivers\\DrvHide.sys", "prompt": "auto", "sleep": 4, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "cd C:\\Users\\<USER>\\Documents & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}