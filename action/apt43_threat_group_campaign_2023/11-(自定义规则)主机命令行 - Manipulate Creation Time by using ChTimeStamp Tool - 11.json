{"action": {"name": "(自定义规则)主机命令行 - Manipulate Creation Time by using ChTimeStamp Tool - 11", "desc": "In this action, an attacker is trying to manipulate the creation time of a file by using ChTimeStamp tool.[apt43_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "test.txt", "file_owner": "system", "file_transfer_library": 998002185, "randomize_file": null, "file_name": "test1.txt"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "ChTimeStamp.exe", "file_owner": "system", "file_transfer_library": 998002186, "randomize_file": null, "file_name": "ChTimeStamp.exe"}], "steps": [{"step_order": 1, "command": "powershell.exe /c $a=(Get-ChildItem -Path \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\test.txt\" | select CreationTime); \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ChTimeStamp.exe\" \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\test.txt\" C:\\Windows\\System32\\cmd.exe; $b=(Get-ChildItem -Path C:\\Windows\\System32\\cmd.exe | select CreationTime);", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "test\\.txt's\\ time\\ stamp\\ changed\\ to\\ C:\\\\Windows\\\\System32\\\\cmd\\.exe's\\ time\\ stamp", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1070"], "mitre_mitigation": ["ATT&CK:M1041", "ATT&CK:M1022", "ATT&CK:M1029"]}}, "picus_id": 23301, "uuid": "f16b0745-a5f1-4e5d-80a8-18539cdf4284", "sigma_device_query": ""}