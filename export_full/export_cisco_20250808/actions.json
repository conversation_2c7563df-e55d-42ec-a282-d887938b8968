{"actions": {"b6885774-b831-4972-9ccf-4a05f739283b": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "linux", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "ver": 16, "vid": "8001318", "timeout_ms": 1000, "notes": "塞讯验证建议在OS linuxOS AI攻防机器人上以Root或非管理员用户身份运行此验证动作。", "name": "主机命令行 - Cisco IOS 和 IOS XE 集群管理协议（CMP），CVE-2017-3881，远程代码执行，漏洞利用", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了 Cisco IOS 和 IOS XE 软件中的集群管理协议（Cluster Management Protocol, CMP）远程代码执行漏洞利用过程。该漏洞存在于启用 CMP 功能的设备中，CMP 协议通过 Telnet 会话进行通信。由于对 CMP 数据包长度的校验不当，攻击者可构造特定的 Telnet 数据包，触发缓冲区溢出，从而在目标设备上执行任意代码，最终实现完全控制。此漏洞无需认证，攻击门槛低，具备蠕虫式传播能力。受影响设备需同时启用 Telnet 和集群功能。\n\n影响固件版本：12.2(55)SE1  C2960-LANBASEK9-M\n官方公告详见：https://www.cisco.com/c/en/us/support/docs/csa/cisco-sa-20170317-cmp.html。", "created_at": "2025-08-05 11:37:17", "updated_at": "2025-08-01 16:33:19", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "os", "tag": "Linux:CentOS", "tag_en": "Linux:CentOS", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "os", "tag": "Linux:Redhat", "tag_en": "Linux:Redhat", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "os", "tag": "Linux:Debian", "tag_en": "Linux:Debian", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "os", "tag": "Linux:Ubuntu", "tag_en": "Linux:Ubuntu", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_techniques", "tag": "T1190 - 公开应用程序利用", "tag_en": "T1190 - Exploit Public-Facing Application", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1048", "tag_en": "M1048", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1050", "tag_en": "M1050", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1035", "tag_en": "M1035", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1030", "tag_en": "M1030", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1051", "tag_en": "M1051", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "mitre_mitigation", "tag": "M1016", "tag_en": "M1016", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "src_destination", "tag": "Src:Internal:Trusted", "tag_en": "Src:Internal:Trusted", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "run_as", "tag": "Root", "tag_en": "Root", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "b6885774-b831-4972-9ccf-4a05f739283b", "tag_type": "cve", "tag": "CVE-2017-3881", "tag_en": "CVE-2017-3881", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>OS", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>linux", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "/tmp/{{v_action_id}}", "destination_name": "CVE-2017-3881-SE1-S", "file_name": "CVE-2017-3881-SE1-S", "file_owner": "system", "file_transfer_library": 998000722, "filesize": 8446960, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "48ba26630a93c4dc140e8f66dd209645", "sha1sum": "5f87b43b1a00ce5aa56c47717de8632a695a2825", "sha256sum": "d28c91d11908f083a983dcbaaca3542900b3eb694e70293a85e229cea2ca70d2"}], "monitor_connections": "off", "raw_text": "/tmp/{{v_action_id}}/CVE-2017-3881-SE1-S {{v_host}} --set\r\n/tmp/{{v_action_id}}/CVE-2017-3881-SE1-S {{v_host}} --unset\r\nrm -rf /tmp/{{v_action_id}}\r\n", "shell": "bash", "steps": [{"check_events": true, "command": "/tmp/{{v_action_id}}/CVE-2017-3881-SE1-S {{v_host}} --set", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "Current privilege level is 15", "success_order": 1, "timeout": 120}, {"check_events": true, "cleanup": true, "command": "/tmp/{{v_action_id}}/CVE-2017-3881-SE1-S {{v_host}} --unset", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rm -rf /tmp/{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": ["v_host", "v_host"], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "e349cece-da54-480a-b286-0cd0941e2488": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "linux", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "e349cece-da54-480a-b286-0cd0941e2488", "ver": 16, "vid": "8001319", "timeout_ms": 1000, "notes": "塞讯验证建议在OS linuxOS AI攻防机器人上以Root或非管理员用户身份运行此验证动作。", "name": "主机命令行 - Cisco IOS 和 IOS XE 集群管理协议（CMP），CVE-2017-3881，远程代码执行，漏洞利用，变种 #1", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了 Cisco IOS 和 IOS XE 软件中的集群管理协议（Cluster Management Protocol, CMP）远程代码执行漏洞利用过程。该漏洞存在于启用 CMP 功能的设备中，CMP 协议通过 Telnet 会话进行通信。由于对 CMP 数据包长度的校验不当，攻击者可构造特定的 Telnet 数据包，触发缓冲区溢出，从而在目标设备上执行任意代码，最终实现完全控制。此漏洞无需认证，攻击门槛低，具备蠕虫式传播能力。受影响设备需同时启用 Telnet 和集群功能。\n\n影响固件版本：12.2(55)SE11  C2960-LANBASEK9-M\n官方公告详见：https://www.cisco.com/c/en/us/support/docs/csa/cisco-sa-20170317-cmp.html。", "created_at": "2025-08-05 11:37:18", "updated_at": "2025-08-01 16:17:17", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "os", "tag": "Linux:CentOS", "tag_en": "Linux:CentOS", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "os", "tag": "Linux:Redhat", "tag_en": "Linux:Redhat", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "os", "tag": "Linux:Debian", "tag_en": "Linux:Debian", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "os", "tag": "Linux:Ubuntu", "tag_en": "Linux:Ubuntu", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_techniques", "tag": "T1190 - 公开应用程序利用", "tag_en": "T1190 - Exploit Public-Facing Application", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1048", "tag_en": "M1048", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1050", "tag_en": "M1050", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1035", "tag_en": "M1035", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1030", "tag_en": "M1030", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1051", "tag_en": "M1051", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "mitre_mitigation", "tag": "M1016", "tag_en": "M1016", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "src_destination", "tag": "Src:Internal:Trusted", "tag_en": "Src:Internal:Trusted", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "run_as", "tag": "Root", "tag_en": "Root", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "e349cece-da54-480a-b286-0cd0941e2488", "tag_type": "cve", "tag": "CVE-2017-3881", "tag_en": "CVE-2017-3881", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>OS", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>linux", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "/tmp/{{v_action_id}}", "destination_name": "CVE-2017-3881-SE11-S", "file_name": "CVE-2017-3881-SE11-S", "file_owner": "system", "file_transfer_library": 998000723, "filesize": 8447248, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "e5260c7bd7daafb6c8b15d1f077b456d", "sha1sum": "c87e90c0df27ddb794c8e7f3fcbe8228e6b0a80f", "sha256sum": "14e9e08d96e08d4545fac2f3f5b56b1ae67c0778238666d14cf1a0bb9e84f724"}], "monitor_connections": "off", "raw_text": "/tmp/{{v_action_id}}/CVE-2017-3881-SE11-S {{v_host}} --set\r\n/tmp/{{v_action_id}}/CVE-2017-3881-SE11-S {{v_host}} --unset\r\nrm -rf /tmp/{{v_action_id}}\r\n", "shell": "bash", "steps": [{"check_events": true, "command": "/tmp/{{v_action_id}}/CVE-2017-3881-SE11-S {{v_host}} --set", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "Current privilege level is 15", "success_order": 1, "timeout": 120}, {"check_events": true, "cleanup": true, "command": "/tmp/{{v_action_id}}/CVE-2017-3881-SE11-S {{v_host}} --unset", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rm -rf /tmp/{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": ["v_host", "v_host"], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "linux", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "ver": 16, "vid": "8001320", "timeout_ms": 1000, "notes": "塞讯验证建议在OS linuxOS AI攻防机器人上以Root或非管理员用户身份运行此验证动作。", "name": "主机命令行 - Cisco IOS 和 IOS XE Smart Install，CVE-2018-0171，远程代码执行，漏洞利用", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了 Cisco IOS 和 IOS XE Smart Install 远程代码执行漏洞利用过程。CVE-2018-0171 是 Cisco IOS 和 IOS XE 软件中 Smart Install 客户端功能存在的一个高危远程代码执行漏洞。该功能默认启用，允许在交换机自动配置部署中通过 TCP 4786 端口接收控制消息。由于对入站数据包缺乏有效的边界检查，攻击者可以向该端口发送特制的恶意消息，触发堆栈缓冲区溢出，从而在目标设备上远程执行任意代码，获取系统完全控制权限。\n\n官方公告详见：https://www.cisco.com/c/en/us/support/docs/csa/cisco-sa-20180328-smi2.html", "created_at": "2025-08-05 11:37:18", "updated_at": "2025-08-01 16:14:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "os", "tag": "Linux:CentOS", "tag_en": "Linux:CentOS", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "os", "tag": "Linux:Redhat", "tag_en": "Linux:Redhat", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "os", "tag": "Linux:Debian", "tag_en": "Linux:Debian", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "os", "tag": "Linux:Ubuntu", "tag_en": "Linux:Ubuntu", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_techniques", "tag": "T1190 - 公开应用程序利用", "tag_en": "T1190 - Exploit Public-Facing Application", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1048", "tag_en": "M1048", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1050", "tag_en": "M1050", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1035", "tag_en": "M1035", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1030", "tag_en": "M1030", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1051", "tag_en": "M1051", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "mitre_mitigation", "tag": "M1016", "tag_en": "M1016", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "src_destination", "tag": "Src:Internal:Trusted", "tag_en": "Src:Internal:Trusted", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "run_as", "tag": "Root", "tag_en": "Root", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "e5cd7b36-cd8b-4e95-ba4e-54c7dc19dbf6", "tag_type": "cve", "tag": "CVE-2018-0171", "tag_en": "CVE-2018-0171", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>OS", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>linux", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "/tmp/{{v_action_id}}", "destination_name": "CVE-2018-0171-S", "file_name": "CVE-2018-0171-S", "file_owner": "system", "file_transfer_library": 998000724, "filesize": 7425312, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "cf41c68248fdeb426cd546ea9dcc9cda", "sha1sum": "538ce3ca8c8798b44530c29d973fc9be1dc3327f", "sha256sum": "5c29b0cb7b5c50a9d4acbec404126d819d7b04b01794078b5fbf60215c98c4d4"}], "monitor_connections": "off", "raw_text": "/tmp/{{v_action_id}}/CVE-2018-0171-S -i {{v_host}}\r\nrm -rf /tmp/{{v_action_id}}\r\n", "shell": "bash", "steps": [{"check_events": true, "command": "/tmp/{{v_action_id}}/CVE-2018-0171-S -i {{v_host}}", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "\\[+\\] Found", "success_order": 1, "timeout": 120}, {"check_events": true, "cleanup": true, "command": "rm -rf /tmp/{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": ["v_host"], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}