{"actions": {"fbcf90e1-8020-4c6d-adfd-865356f1f5c4": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "ver": 16, "vid": "377922", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 在系统上为 Trickbot 创建计划任务", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强用户在系统内文件和目录的权限控制；针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略，监控可疑命令执行。中长期建议：增加特权帐号管理产品以加强对身份信息系统的监测与保护；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图为 Trickbot创建计划任务以确保在受害机器上的持久化。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:55", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [{"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "source": "奇安信天擎", "message": "QDE.V2.3.36SQ9EIO8Y.VX", "source_identifier": "", "cn_show": true, "product_version": "", "signature_version": ""}], "tags": [{"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1018", "tag_en": "ATT&CK:M1018", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1022", "tag_en": "ATT&CK:M1022", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1028", "tag_en": "ATT&CK:M1028", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_tactics", "tag": "TA0002 - 攻击执行", "tag_en": "TA0002 - Execution", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "mitre_techniques", "tag": "T1053 - 计划任务/作业", "tag_en": "T1053 - Scheduled Task/Job", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "fbcf90e1-8020-4c6d-adfd-865356f1f5c4", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_mitigation ATT&CK:M1026", "mitre_mitigation ATT&CK:M1022", "run_as SYSTEM", "os Windows:Server 2019", "os Windows:Server 2016", "control EDR/XDR", "mitre_mitigation ATT&CK:M1047", "mitre_mitigation ATT&CK:M1018", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "mitre_tactics TA0002 - 攻击执行", "os Windows:Server 2022", "mitre_techniques T1053 - 计划任务/作业", "control AV", "mitre_mitigation ATT&CK:M1028", "run_as Admin", "os Windows:11", "os Windows:10", "control HIDS"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "trickbot.exe", "file_name": "trickbot.exe", "file_owner": "system", "file_transfer_library": 999053815, "file_type": "exe", "filesize": 6144, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "cb07359946d58ff9c1469865b1624bf6", "sha1sum": "b0b48c8f63a95725b1569a8488632f8703a8eb68", "sha256sum": "f1f55e91f0d005cd43b75229dfff68aa88cbc0efbb570ee1737bfccbf5afad1d"}], "monitor_connections": "off", "raw_text": "copy \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\trickbot.exe\" \"C:\\Users\\<USER>\\AppData\\Roaming\\mfjdieks.exe\"\r\nschtasks.exe /CREATE /SC DAILY /TN \"MyTasks\\mfjdieks482601 task\" /TR \"C:\\Users\\<USER>\\AppData\\Roaming\\mfjdieks.exe\" /ST 11:00 /RU SYSTEM\r\nschtasks.exe /DELETE /TN \"MyTasks\\mfjdieks482601 task\" /F\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}} & del \"C:\\Users\\<USER>\\AppData\\Roaming\\mfjdieks.exe\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "copy \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\trickbot.exe\" \"C:\\Users\\<USER>\\AppData\\Roaming\\mfjdieks.exe\"", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "schtasks.exe /CREATE /SC DAILY /TN \"MyTasks\\mfjdieks482601 task\" /TR \"C:\\Users\\<USER>\\AppData\\Roaming\\mfjdieks.exe\" /ST 11:00 /RU SYSTEM", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "schtasks.exe /DELETE /TN \"MyTasks\\mfjdieks482601 task\" /F", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}} & del \"C:\\Users\\<USER>\\AppData\\Roaming\\mfjdieks.exe\"", "prompt": "auto", "sleep": 4, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}