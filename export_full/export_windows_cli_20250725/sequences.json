{"sequences": [{"profiles": [{"name": null, "steps": [{"desc": "In this action, an attacker is trying to learn the current system date via powershell.", "name": "主机命令行 - Get Current Date of the System", "sim_actions": ["be72ef3f-a0ff-44d8-8bd9-5370458f05c8"], "order": 0}, {"desc": "\"fsutil fsinfo drives\" command lists all drives and queries the drive type, volume information, NTFS-specific volume information, or filesystem statistics.", "name": "主机命令行 - Display information about all drives using \"fsutil fsinfo drives\" command", "sim_actions": ["90cf9b69-ecf2-46c0-8da8-2de543cebdc5"], "order": 1}, {"desc": "In this action, an attacker is trying to list all processes that are currently running via the WTSEnumerateProcesses API.", "name": "主机命令行 - List Currently Running Processes via WTSEnumerateProcesses", "sim_actions": ["9fc3b0d2-864b-48a0-8771-0b629f78ccf2"], "order": 2}, {"desc": "In this action, an attacker is trying to list the computers registered to the domain by using the Powershell Active Directory module.", "name": "主机命令行 - Display a List of Domain Computers Using Powershell Active Directory Module", "sim_actions": ["4c520484-a5f7-4c91-a824-f5b2fd29eb2b"], "order": 3}, {"desc": "In this action, an attacker is trying to list the user accounts registered to the domain by using the Powershell Active Directory module.", "name": "主机命令行 - Display a List of Domain Users Using Powershell Active Directory Module", "sim_actions": ["7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37"], "order": 4}, {"desc": "In this action, an attacker is trying together information about the target domain and OS by using Adfind.exe.", "name": "主机命令行 - Gather Information about Target Domain and OS using Adfind", "sim_actions": ["77ea3de5-b2b9-465c-994a-7d19c573a682"], "order": 5}, {"desc": "In this action, an attacker is trying to execute Invoke-BloodHound function by using BloodHound tool's Ingestor.", "name": "主机命令行 - Execute BloodHound Tool's Ingestor (Invoke-BloodHound) Function Variant-2", "sim_actions": ["20c0f136-5612-4a2c-9674-d7e45a763a38"], "order": 6}, {"desc": "In this action, an attacker is trying to disable the Real-Time Monitoring service to evading the protection mechanism.", "name": "主机命令行 - Disable the Real Time Monitoring Service of Windows Defender", "sim_actions": ["9a2f41d5-61a4-4a99-9950-f4d0b4db792f"], "order": 7}, {"desc": "In this action, an attacker tries to add an RDP rule called \"rdp\" to allow it on Windows Firewall.", "name": "主机命令行 - Add RDP Allow Rule called \"rdp\" on Windows Firewall", "sim_actions": ["7c8d95ec-caa7-4f05-97c4-7c4cd04b5271"], "order": 8}, {"desc": "In this action, an attacker is trying to dump lsass.exe by executing a native comsvcs.dll DLL in Windows\\system32 with rundll32\n.", "name": "主机命令行 - Dump Credentials by executing comsvcs.dll Minidump", "sim_actions": ["4c7dacdd-011e-404e-aa70-6654f3983589"], "order": 9}, {"desc": "Mimikatz is a credential dumper tool capable of obtaining plaintext Windows account logins and passwords, along with many other features that make it useful for testing the security of networks.", "name": "主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) <PERSON>l", "sim_actions": ["59c709cd-92e6-4270-a50b-76d4f8bc358b"], "order": 10}, {"desc": "In this action, an attacker is trying to download the portable version of AnyDesk.", "name": "主机命令行 - Download the AnyDesk Portable Version", "sim_actions": ["737bb456-2ab9-44c6-88cc-158fb1ac068d"], "order": 11}, {"desc": "In this attack, an attacker can add a new user with the same rights.", "name": "主机命令行 - Add a New User", "sim_actions": ["0e544603-9417-4e3a-974b-9d74d1df2937"], "order": 12}, {"desc": "In this action, shadow copy is deleted by using Powershell.", "name": "主机命令行 - Delete Shadow Copy using Powershell", "sim_actions": ["8dd4e17b-8631-457e-a3dc-2dea26a685b0"], "order": 13}, {"desc": "In this action, an attacker is trying to find files with .PDF, .DOC, .XLS extensions.", "name": "主机命令行 - Find Files with Specific Extensions", "sim_actions": ["7a720608-7b66-4d6a-9c8d-fc0b46588cc3"], "order": 14}, {"desc": "In this action, an encryptor.exe encrypts a text file using AES.", "name": "主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe", "sim_actions": ["dd1ab321-1f79-4962-b5dd-76f337b64746"], "order": 15}, {"desc": "In this action, <PERSON> is trying to write a file in the victim's C:\\Users\\<USER>\\Documents folder to inform the victim and open it using notepad.exe.", "name": "主机命令行 - Write a File that Contains Akira Ransom Note and Open It", "sim_actions": ["b3f364c4-d95a-47af-b3c8-7c4d786e3015"], "order": 16}]}], "name": "Akira <PERSON> Campaign", "desc": "<PERSON> is a prolific ransomware that has been operating since March 2023 and has targeted multiple industries, primarily in North America, the UK, and Australia. It functions as a Ransomware as a Service (RaaS) and exfiltrates data prior to encryption, achieving double extortion. According to the group’s leak site, they have infected over 196 organizations.\n\n", "emergency": false, "metadata_version": 1, "is_apt": false, "product": "base", "sequence_type": "sequences", "stop_on_error": true, "uuid": "6c530bc4-5a63-45e3-86b4-f74eafcf751f", "vid": "", "ver": "", "remendation": 2, "protected_endpoint_rollback": "no", "tags": ["ATT&CK:T1018", "ATT&CK:T1490", "ATT&CK:Discovery", "ATT&CK:T1562", "ATT&CK:Persistence", "ATT&CK:T1486", "ATT&CK:Impact", "ATT&CK:T1124", "ATT&CK:T1087.002", "ATT&CK:T1491", "ATT&CK:T1003", "ATT&CK:T1016", "ATT&CK:Defense Evasion", "ATT&CK:T1133", "ATT&CK:T1003.001", "ATT&CK:Credential Access", "ATT&CK:T1083", "ATT&CK:T1057", "ATT&CK:T1136.001"]}, {"profiles": [{"name": null, "steps": [{"desc": "In this action, An attacker uses Powershell DownloadString() method to download a malicious file and display it in a PowerShell console.", "name": "主机命令行 - Execute Powershell Script by using DownloadString and Invoke-Expression", "sim_actions": ["d46c48ea-00ba-40a5-a844-d21b471868fb"], "order": 0}, {"desc": "In this action, an attacker is trying to execute a BAT script to run a process via the Windows Management Instrumentation Command-line(WMIC) utility.", "name": "主机命令行 - Execute a BAT Script via WMIC", "sim_actions": ["962e73c0-8dfd-4788-aa29-6d87bb5d5c7c"], "order": 1}, {"desc": "In this action, an attacker is trying to list domain controllers in the target environment by using nltest command.", "name": "主机命令行 - List Domain Controllers using nltest", "sim_actions": ["16c65914-8e3f-4563-a9bb-41ac6a1c1124"], "order": 2}, {"desc": "In this action, an attacker is trying to gather information on domain trusts with nltest command", "name": "主机命令行 - Gather Trusted Domains via Nltest Command Variant-1", "sim_actions": ["f3fb6ea7-29b9-4c87-86b4-d31a0c68910b"], "order": 3}, {"desc": "In this action, an attacker is trying to execute a WMI command by using a Powershell script.", "name": "主机命令行 - Execute a WMI Event Subscription Command using Powershell Script", "sim_actions": ["2067b09f-0093-441c-bb52-715717d624e1"], "order": 4}, {"desc": "In this action, an attacker is trying to inject shellcode into mspaint.exe by using GoPurple tool RtlCreateUserThread method.", "name": "主机命令行 - Inject Shellcode by using GoPurple RtlCreateUserThread Method", "sim_actions": ["9f9a2033-930c-485c-864b-b5fe6fb34854"], "order": 5}, {"desc": "In this action, an attacker is trying to execute an encrypted shellcode which is created by Exocet Tool.", "name": "主机命令行 - Execute Encrypted Shellcode by using Exocet Tool", "sim_actions": ["e2ae63a3-b79e-4951-a93f-e2f05587b59b"], "order": 6}, {"desc": "In this action, an attacker is trying to execute a shellcode by injecting it into the \"runonce.exe\" process via the Early Bird APC Queue Technique.", "name": "主机命令行 - Inject Shellcode to \"runonce.exe\" Process via Early Bird APC Queue Technique", "sim_actions": ["ac28de25-a7b1-47d7-a7bc-fbcd1434ec03"], "order": 7}, {"desc": "In this action, an attacker executes a notorious Invoke-Mimikatz Powershell script which was obfuscated by the Powershell-Obfuscation tool.", "name": "主机命令行 - Execute Obfuscated Mimikatz via Powershell-Obfuscation Tool", "sim_actions": ["d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506"], "order": 8}, {"desc": "In this action, an attacker is trying to create a SYSTEM account privileged process via the SharpToken tool.", "name": "主机命令行 - Elevate to SYSTEM by using SharpToken Tool", "sim_actions": ["********-98d0-42b9-b211-c597e7f3ee19"], "order": 9}]}], "name": "FIN8 Threat Group Campaign", "desc": "FIN8 is an advanced persistent threat (APT) group known for financially motivated cyberattacks, primarily targeting the retail, hospitality, and financial sectors. Active since at least 2016, FIN8 employs sophisticated techniques such as spear-phishing, living-off-the-land tactics, and customized malware to infiltrate networks and steal payment card data. The group is known for using malware families like BADHATCH and Sardonic, constantly evolving their toolsets to avoid detection. FIN8 typically focuses on point-of-sale (POS) systems and has demonstrated a high level of operational security and adaptability in its campaigns.", "emergency": false, "metadata_version": 1, "is_apt": false, "product": "base", "sequence_type": "sequences", "stop_on_error": true, "uuid": "e278cf7a-2c25-44e4-ab64-4ad1042f45a2", "vid": "", "ver": "", "remendation": 2, "protected_endpoint_rollback": "no", "tags": ["ATT&CK:T1018", "ATT&CK:T1047", "ATT&CK:T1482", "ATT&CK:T1055.002", "ATT&CK:Discovery", "ATT&CK:T1059", "ATT&CK:T1055.001", "ATT&CK:Persistence", "ATT&CK:T1027", "ATT&CK:Defense Evasion", "ATT&CK:Execution", "ATT&CK:T1546.003", "ATT&CK:Privilege Escalation", "ATT&CK:T1134", "ATT&CK:T1027.013"]}, {"profiles": [{"name": null, "steps": [{"desc": "In this action, an attacker can get peripheral devices' list with Registry Query", "name": "主机命令行 - Gather Peripheral Devices List using Registry Query", "sim_actions": ["d819acd7-9797-4aef-aefd-747742df9012"], "order": 0}, {"desc": "In this action, an attacker is trying to collect installed browser information including version and binary path via subkeys under the \"HKLM\\SOFTWARE\\Clients\\StartMenuInternet\" registry key.", "name": "主机命令行 - Enumerate Installed Browsers via Registry Keys", "sim_actions": ["8d57772f-e509-4ae2-a97e-2c5f7f06181f"], "order": 1}, {"desc": "In this action, an attacker can get Plug and Play (PnP) devices' list with Win32_PNPEntity Method", "name": "主机命令行 - Gather Plug and Play (PnP) Devices List using PowerShell Win32_PNPEntity", "sim_actions": ["53da965e-f1bb-41f6-9923-5e3da2cd9c8b"], "order": 2}, {"desc": "In this action, powershell.exe tool gets installed software information.", "name": "主机命令行 - List Install Software Information", "sim_actions": ["5141379c-87f2-4549-b239-cfc1eb72bfcb"], "order": 3}, {"desc": "In this action, an attacker utilizes several common Windows command-line tools to enumerate resources.", "name": "主机命令行 - Gather System Information Thread using Command-line Tools", "sim_actions": ["7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e"], "order": 4}, {"desc": "\"time /t\" command displays the current system time and type.", "name": "主机命令行 - Display the current system time information using \"time /t\" command", "sim_actions": ["d75636f9-73d2-4da7-b2db-0f5ddfb55097"], "order": 5}, {"desc": "In this action, a batch script removes Hive ransomware and itself.", "name": "主机命令行 - Execute Self-Deleting Hive Batch Script", "sim_actions": ["9d06aa12-a412-4f24-b8df-24ad22ee5dee"], "order": 6}, {"desc": "In this action, an attacker is trying to stress your anti-malware system. It performs a bunch of common malware tricks including Virtual Machine, Emulation, Debuggers, and Sandbox detection with the goal of seeing if you stay under the radar.", "name": "主机命令行 - Discover Debugger and Sandbox Indicators ", "sim_actions": ["c62d4352-352c-46a1-92be-f4a22e54ae1e"], "order": 7}, {"desc": "In this action, an attacker is trying to query the registry keys for Vmware, QEMU and SMBIOSBIOSVERSION.", "name": "主机命令行 - Query Virtual Machine Information", "sim_actions": ["cf5167a5-fb79-4125-b3da-a9a40c4f638e"], "order": 8}, {"desc": "In this action, an attacker is trying to delete all system event logs by PowerShell.", "name": "主机命令行 - Delete All System Event Logs via PowerShell", "sim_actions": ["0f539245-f3e1-4b2d-b4cd-1e9145996d73"], "order": 9}, {"desc": "In this action, the attacker is trying to create a new scheduled task via Powershell cmdlets.", "name": "主机命令行 - Create a New Scheduled Task via Powershell Cmdlets", "sim_actions": ["34c2767f-28aa-4d9e-adec-55e98fd0025a"], "order": 10}, {"desc": "In this action, an attacker is trying to dump saved browser credentials by using SharpWeb with a Powershell loader script which loads the tool from a URL into the memory.", "name": "主机命令行 - Du<PERSON> Saved Browser Credentials using SharpLoader", "sim_actions": ["465e97d1-5a8b-43d9-a388-89a5d3849947"], "order": 11}, {"desc": "In this action, an attacker can gather credential in the registry(HKLM Hive) using reg query command", "name": "主机命令行 - Gather Credentials in Registry(HKLM Hive) using Reg.exe", "sim_actions": ["1b5b0c6d-322b-4af6-b400-5538082cd81a"], "order": 12}, {"desc": "In this action, an attacker can gather credential in the registry(HKCU Hive) using reg query command", "name": "主机命令行 - Gather Credentials in Registry(HKCU Hive) using Reg.exe", "sim_actions": ["1d41fad3-790e-4167-b9c1-40afaeaccf77"], "order": 13}, {"desc": "In this action, Powershell Compress-Archive command is using to compress all the critical data that collected in recon phases.", "name": "主机命令行 - Compress the Collected Data from the Victim System", "sim_actions": ["d510f5b2-0af2-43f0-96d9-5ddf37035aec"], "order": 14}, {"desc": "In this action, an attacker is trying to dump Security Account Manager (SAM) related registry hives from a shadow copy of the system by using ShadowSteal Tool.", "name": "主机命令行 - Dump SAM Related Registry Hives by using ShadowSteal Tool", "sim_actions": ["2a9e8073-588e-449c-88be-1696df015360"], "order": 15}, {"desc": "In this action, an attacker is trying to download a dummy file under the TMP directory via the cURL executable.", "name": "主机命令行 - Download a Dummy File via cURL", "sim_actions": ["ae0a0e09-0503-47b2-a885-e1afa18f040f"], "order": 16}, {"desc": "In this action, an attacker is trying to download several additional files and saves them to the victim's machine.", "name": "主机命令行 - Download Additional Files from C2s", "sim_actions": ["1cd6e8f5-c44c-4469-ace8-cb7e2dc5689e"], "order": 17}, {"desc": "In this action, an attacker is trying to create an encrypted archive file via Winrar", "name": "主机命令行 - Create an Encrypted Archive File via Winrar", "sim_actions": ["893d17b6-342e-42e6-94fc-4bb43567d618"], "order": 18}]}], "name": "<PERSON><PERSON> Stealer Malware Campaign", "desc": "Taurus Stealer, a.k.a Taurus or Taurus Project, is a C/C++ information‑stealer active since April 2020, most often delivered via malspam attachments or the Fallout Exploit Kit. Rapid development cycles have seen it adopt Predator The Thief style configuration loading and obfuscation techniques, with recent updates strengthening its networking routines to slip past sandbox defenses. Marketed by “Taurus Seller” on Russian‑language underground forums, it quietly harvests browser passwords, cookies, and autofill data, along with lists of installed software and detailed system configurations, before bundling everything into an encrypted payload and exfiltrating it to its operators. Unique geofencing code prevents execution in Commonwealth of Independent States nations, such as Russia, Belarus, and Kazakhstan, underscoring its authors’ intent to steer clear of local law enforcement.", "emergency": false, "metadata_version": 1, "is_apt": false, "product": "base", "sequence_type": "sequences", "stop_on_error": true, "uuid": "506b6f61-d6f0-4438-b223-e459baebc108", "vid": "", "ver": "", "remendation": 2, "protected_endpoint_rollback": "no", "tags": ["ATT&CK:Discovery", "ATT&CK:Collection", "ATT&CK:T1497.001", "ATT&CK:T1070.001", "ATT&CK:T1560.001", "ATT&CK:Persistence", "ATT&CK:T1622", "ATT&CK:T1124", "ATT&CK:T1070.004", "ATT&CK:T1518", "ATT&CK:T1552", "ATT&CK:T1560.002", "ATT&CK:T1217", "ATT&CK:T1555.003", "ATT&CK:T1016", "ATT&CK:Defense Evasion", "ATT&CK:T1053.005", "ATT&CK:Execution", "ATT&CK:Command and Control", "ATT&CK:T1003.002", "ATT&CK:T1105", "ATT&CK:T1059", "ATT&CK:Credential Access", "ATT&CK:T1120"]}]}