{"action": {"name": "(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 5", "desc": "In this action, an attacker is trying together information about the target domain and OS by using Adfind.exe.[bumblebee_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "adfind.exe", "file_owner": "system", "file_transfer_library": 998002333, "randomize_file": null, "file_name": "adfind.exe"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"\"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\adfind.exe\" -f objectcategory=computer -csv name cn OperatingSystem dNSHostName > \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\some.csv\"\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1018"], "mitre_mitigation": []}}, "picus_id": 10502, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Active Directory Enumeration via Adfind Tool", "ruleId": "2371", "description": "Detects the attempt to gather Active Directory Enumeration via Adfind tool. AdFind execution results in a list of all AD computer objects and associated meta-data. Adversaries typically pipe the output of this command to a txt file for exfiltration.", "releaseDate": 1628640000000, "updateDate": 1662940800000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Active Directory Enumeration via Adfind Tool\nstatus: experimental\ndescription: Detects the attempt to gather Active Directory Enumeration via Adfind tool. AdFind execution results in a list of all AD computer objects and associated meta-data. Adversaries typically pipe the output of this command to a txt file for exfiltration.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1087/002/\n   - https://attack.mitre.org/techniques/T1482/\n   - https://attack.mitre.org/techniques/T1069/002/\n   - https://attack.mitre.org/techniques/T1016/\n   - https://attack.mitre.org/techniques/T1018\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/software/S0552/\n   - http://wutils.com/wmi/root/wmi/msacpi_thermalzonetemperature/#properties\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\adfind.exe'\n        ProcessCommandLine|contains:\n          - 'objectcategory'\n          - 'trustdmp'\n          - 'dclist'\n          - 'dcmodes'\n          - 'domainlist'\n          - 'computers_pwdnotreqd'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\n    - Administrative tools/scripts\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.t1018\n    - attack.t1016\n    - attack.t1482\n    - attack.t1069.002\n    - attack.t1087.002\n    - attack.s0552\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities", "Administrative tools/scripts"], "mitre": [{"name": "Remote System Discovery", "mitreId": "T1018", "url": "https://attack.mitre.org/techniques/T1018/", "type": "technique"}, {"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}, {"name": "Domain Trust Discovery", "mitreId": "T1482", "url": "https://attack.mitre.org/techniques/T1482/", "type": "technique"}, {"name": "Domain Groups", "mitreId": "T1069.002", "url": "https://attack.mitre.org/techniques/T1069/002/", "type": "sub_technique"}, {"name": "Domain Account", "mitreId": "T1087.002", "url": "https://attack.mitre.org/techniques/T1087/002/", "type": "sub_technique"}]}]}, "uuid": "77ea3de5-b2b9-465c-994a-7d19c573a682"}