{"action": {"name": "(自定义规则)主机命令行 - Take Screen Capture using a Powershell Script Variant-1 - 2", "desc": "In this action, an attacker can take the desktop screen and save it as a .png file on TMP directory.[apt37_threat_group_campaign_2019]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "T1113.ps1", "file_owner": "system", "file_transfer_library": 998002086, "randomize_file": null, "file_name": "T1113.ps1"}], "steps": [{"step_order": 1, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1113.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1113.ps1'", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c dir \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\screenshot.png\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Collection", "ATT&CK:T1113"], "mitre_mitigation": []}}, "picus_id": 155, "uuid": "2aa93490-e5ed-4d76-83b0-e6b6b200967c", "sigma_device_query": "title: Screen Capture via PowerShell\nstatus: experimental\ndescription: Detects the attempt to take screen shot via PowerShell script. Adversaries aim to gather information over the course of an operation.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1113/\n    - https://attack.mitre.org/tactics/TA0009/\n    - https://learn.microsoft.com/en-us/dotnet/api/system.drawing.graphics.copyfromscreen?view=netframework-4.8\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: 4104\n        Message|contains|all:\n            - 'Drawing.Graphics'\n            - 'CopyFromScreen'\n    condition: selection\nfalsepositives: \n    - Penetration testing\nlevel: high\ntags:\n    - attack.collection\n    - attack.t1113\n    - attack.ta0009"}