{"action": {"name": "(自定义规则)主机命令行 - Enumerate Privilege Escalation Vectors via SharpUp - 6", "desc": "This action allows attackers to find missing configurations to be able to escalate after gaining access by using SharpUp tool.[bumblebee_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "SharpUp.exe", "file_owner": "system", "file_transfer_library": 998002334, "randomize_file": null, "file_name": "SharpUp.exe"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpUp.exe\" audit", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Completed", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1082"], "mitre_mitigation": []}}, "picus_id": 22790, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Cached Group Policy Preferences (GPP) XML Files Enumeration for Unsecured Credentials", "ruleId": "2602", "description": "Detects the attempt to find unsecured credentials in cached Group Policy Preferences (GPP) XML Files. This technique is utilized for credential access.", "releaseDate": 1664409600000, "updateDate": 1664409600000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Cached Group Policy Preferences (GPP) XML Files Enumeration for Unsecured Credentials\nstatus: experimental\ndescription: Detects the attempt to find unsecured credentials in cached Group Policy Preferences (GPP) XML Files. This technique is utilized for credential access.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0006/\n   - https://attack.mitre.org/techniques/T1552/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Removable Storage'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit File System'\n    definition3: 'You should enable System Access Control Lists (SACL) for registry value which you want to monitor or track event changes.'\ndetection:\n    selection:\n        EventID: 4663\n        ObjectName|contains: '\\Microsoft\\Group Policy\\History'\n        Accesses: 'ReadData (or ListDirectory)'\n    selection1:\n        ObjectName|contains:\n            -   Groups.xml\n            -   Services.xml\n            -   Scheduledtasks.xml\n            -   DataSources.xml\n            -   Printers.xml\n            -   Drives.xml\n            -   Registry.xml\n    condition: selection and selection1\nfalsepositives:\n    - Unknown\nlevel: medium\ntags:\n    - attack.credential_access\n    - attack.ta0006\n    - attack.t1552", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Removable Storage", "Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit File System", "You should enable System Access Control Lists (SACL) for registry value which you want to monitor or track event changes."]}, "falsePositives": ["Unknown"], "mitre": [{"name": "Unsecured Credentials", "mitreId": "T1552", "url": "https://attack.mitre.org/techniques/T1552/", "type": "technique"}]}, {"ruleName": "Discover Loaded DLLs via KnownDLLs Registry Key", "ruleId": "2471", "description": "Detects the attempt to discover loaded DLLs from the system folder. Adversaries may use this technique to find possible DLL hijackable processes.", "releaseDate": 1664841600000, "updateDate": 1665532800000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Discover Loaded DLLs via KnownDLLs Registry Key \nstatus: experimental\ndescription: Detects the attempt to discover loaded DLLs from the system folder. Adversaries may use this technique to find possible DLL hijackable processes.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/tactics/TA0007/\n    - https://attack.mitre.org/techniques/T1057/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Registry'\n    definition2: 'You should enable System Access Control Lists (SACL) for file or folder which you want to monitor or track event changes'\ndetection:\n    selection:\n        EventID: 4656\n        ObjectType: Key\n        ObjectName:\n            - '*\\Control\\Session Manager\\KnownDLLs'\n    condition: selection\nfalsepositives:\n    - Unknown\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.ta0007\n    - attack.t1057", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Registry", "You should enable System Access Control Lists (SACL) for file or folder which you want to monitor or track event changes"]}, "falsePositives": ["Unknown"], "mitre": [{"name": "Process Discovery", "mitreId": "T1057", "url": "https://attack.mitre.org/techniques/T1057/", "type": "technique"}]}, {"ruleName": "Group Policy Preferences (GPP) XML Files Enumeration for Unsecured Credentials", "ruleId": "5659", "description": "Detects the attempt to find unsecured credentials in Group Policy Preferences (GPP). This technique is utilized for credential access.", "releaseDate": 1660176000000, "updateDate": 1660176000000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Group Policy Preferences (GPP) XML Files Enumeration for Unsecured Credentials\nstatus: experimental\ndescription: Detects the attempt to find unsecured credentials in Group Policy Preferences (GPP). This technique is utilized for credential access.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0006/\n   - https://attack.mitre.org/techniques/T1552/\nlogsource:\n    product: windows\n    service: security\n    definition: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Detailed File Share'\ndetection:\n    selection:\n        EventID: 5145\n        ShareName|contains: \n            - 'SYSVOL'\n        RelativeTargetName|contains|all:\n            - 'Policies'\n            - '.xml'\n    filter:\n        AccountName|endswith:\n            - '$'\n    condition: selection and not filter\nfalsepositives: \n    - Administrative tools/scripts\nlevel: medium\ntags:\n    - attack.credential_access\n    - attack.ta0006\n    - attack.t1552", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Detailed File Share"]}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "Unsecured Credentials", "mitreId": "T1552", "url": "https://attack.mitre.org/techniques/T1552/", "type": "technique"}]}, {"ruleName": "Suspicious Access to Lsass Memory with Non-System Account", "ruleId": "5310", "description": "Detects the attempt of access to LSASS process via direct system call with the non-system account. Adversaries may use this technique to dump Lsass memory for credential access.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Suspicious Access to Lsass Memory with Non-System Account\nstatus: experimental\ndescription: Detects the attempt of access to LSASS process via direct system call with the non-system account. Adversaries may use this technique to dump Lsass memory for credential access.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/tactics/TA0006/\n    - https://attack.mitre.org/techniques/T1003/001/\n    - https://attack.mitre.org/software/S0002/\n    - https://docs.microsoft.com/en-us/windows/win32/procthread/process-security-and-access-rights\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 10\n        TargetImage|endswith: '\\lsass.exe'\n        GrantedAccess|contains:\n            - '0x1410'\n            - '0x1010'\n    filter:\n        SourceUser|contains:\n            - 'NT AUTHORITY'\n    condition: selection and not filter\nfalsepositives:\n    - Legitimate Windows processes\nlevel: high\ntags:\n    - attack.t1003.001\n    - attack.s0002\n    - attack.ta0006\n    - attack.credential_access", "logSource": {"productName": "windows", "service": "sysmon", "policies": null}, "falsePositives": ["Legitimate Windows processes"], "mitre": [{"name": "LSASS Memory", "mitreId": "T1003.001", "url": "https://attack.mitre.org/techniques/T1003/001/", "type": "sub_technique"}]}]}, "uuid": "83229147-fa0f-4ebb-9fed-3cb0c584a469"}