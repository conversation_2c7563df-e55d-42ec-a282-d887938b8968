{"id": 3631, "display_id": 58503, "name": "FIN7 Threat Group Campaign", "action_count": 9, "objective_count": 6, "release_date": "2025-07-18T03:03:41Z", "update_date": "2025-07-18T03:03:41Z", "is_copyable": true, "is_predefined": true, "is_licensed": true, "severity": "High", "tags": ["APT-C-11", "ATK 32", "Calcium", "Gold Niagara", "ITG14", "Navigator", "TAG-CR1"], "kill_chain_phases": ["Delivery", "Defense Evasion", "Command & Control", "Collection"], "affected_operating_systems": ["Windows"], "applicable_agent_type_ids": [2, 3], "lateral_action_count": 0, "cred_needed_action_count": 0, "cti_map_count": 0, "is_wats_lite": false, "module_ids": [2], "unlicensed_module_ids": null, "categories": [{"id": 2, "name": "<PERSON> <PERSON><PERSON>rio"}], "policies": null, "assessments": null, "is_busy": false, "vulnerability_types": null, "campaign": {"meta_data": {"id": 3631, "display_id": 58503, "name": "FIN7 Threat Group Campaign", "action_count": 9, "description": "FIN7 is a financially-motivated threat group that has primarily targeted the U.S. retail, restaurant, and hospitality sectors since mid-2015. They often use point-of-sale malware. A portion of FIN7 was run out of a front company called Combi Security. Some resources state that, FIN7 is sometimes referred to as Carbanak, Anunak, but others claim that they are two separate groups using the same Carbanak malware and are therefore tracked separately.", "release_date": "2025-07-18T03:03:41Z", "update_date": "2025-07-18T03:03:41Z", "affected_os": [{"is_selected": false, "name": "Windows"}], "affected_platform": [{"is_selected": false, "name": "Windows Server 2022 64-bit"}, {"is_selected": false, "name": "Windows 11 64-bit"}, {"is_selected": false, "name": "Windows Server 2019 64-bit"}, {"is_selected": false, "name": "Windows Server 2016 64-bit"}, {"is_selected": false, "name": "Windows 10 64-bit"}], "threat_actor": {"id": 26, "name": "FIN7", "description": "FIN7 is a financially-motivated threat group that has been active since 2013. FIN7 has primarily targeted the retail, restaurant, hospitality, software, consulting, financial services, medical equipment, cloud services, media, food and beverage, transportation, and utilities industries in the U.S. A portion of FIN7 was run out of a front company called Combi Security and often used point-of-sale malware for targeting efforts. Since 2020, FIN7 shifted operations to a big game hunting (BGH) approach including use of REvil ransomware and their own Ransomware as a Service (RaaS), Darkside. FIN7 may be linked to the Carbanak Group, but there appears to be several groups using Carbanak malware and are therefore tracked separately.", "origin": "Russia", "aka": "", "sectors": ["Military and Defense", "Telecommunication", "Government", "Transportation", "Finance", "Energy", "Healthcare", "Travel, Tourism, and Hospitality", "Technology and Information Services", "Retail and Consumer Goods", "Professional Services and Consulting", "Media, Entertainment, and Arts", "Food and Beverage", "Education and Research", "Construction and Real Estate", "Legal"], "regions": ["Europe", "Middle East"], "associated_groups": ["Gray<PERSON><PERSON><PERSON>", "ITG14", "Gold Niagara", "Calcium", "TAG-CR1", "Navigator", "ATK 32", "APT-C-11", "FIN7", "Gold Niagara", "Calcium", "Navigator", "APT-C-11", "ATK 32", "TAG-CR1", "Gray<PERSON><PERSON><PERSON>", "ITG14", "FIN7", "Gray<PERSON><PERSON><PERSON>", "TAG-CR1", "ITG14", "Calcium", "APT-C-11", "ATK 32", "Gold Niagara", "FIN7", "Navigator", "ITG14", "TAG-CR1", "Gray<PERSON><PERSON><PERSON>", "ATK 32", "Gold Niagara", "APT-C-11", "FIN7", "Calcium", "Navigator", "TAG-CR1", "APT-C-11", "Gray<PERSON><PERSON><PERSON>", "FIN7", "Calcium", "Navigator", "ATK 32", "Gold Niagara", "ITG14", "APT-C-11", "Calcium", "Navigator", "Gray<PERSON><PERSON><PERSON>", "FIN7", "ITG14", "Gold Niagara", "ATK 32", "TAG-CR1", "ITG14", "TAG-CR1", "FIN7", "Calcium", "APT-C-11", "Gold Niagara", "Gray<PERSON><PERSON><PERSON>", "Navigator", "ATK 32"]}, "severity": "High", "modules": ["Endpoint Scenario"], "is_custom": false, "tags": ["Gold Niagara", "ATK 32", "APT-C-11", "Navigator", "Calcium", "TAG-CR1", "ITG14"], "categories": ["<PERSON> <PERSON><PERSON>rio"], "kill_chain_phases": ["Delivery", "Defense Evasion", "Command & Control", "Collection"], "mitre": {"tactic": {"id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": "T1620", "name": "Reflective Code Loading"}, "sub_technique": {"id": "T1056.001", "name": "Keylogging"}}, "cti_actions_count": 0, "vulnerability_types": null}, "result": "", "run_date": "", "flow": [{"detail": {"id": 3783, "type": "Discovery", "result": ""}, "items": [{"detail": {"name": "Execute JScript to Profile Target System", "tag": "execute", "result": "", "tactic": {"id": 8, "mitre_id": "TA0002", "mitre_name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "mitre_name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 93, "mitre_id": "T1059.007", "mitre_name": "JavaScript"}, "phase": {"stage": "Initial Foothold", "phase_id": 3, "phase_name": "Delivery", "phase_description": "Techniques resulting in the transmission of a weaponized object to the targeted environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10460, "node_id": 2, "action": {"id": 10460, "display_id": 12494, "owasp_rank": "", "name": "Execute JScript to Profile Target System", "release_date": "2022-06-18T12:22:03.690972Z", "description": "In this action, an attacker is trying to execute JScript to discover information on the target system.", "mitre": {"tactic": {"id": 8, "mitre_id": "TA0002", "name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 93, "mitre_id": "T1059.007", "name": "JavaScript"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6047, "name": "Process 1", "content": "cscript.exe /e:jscript \"%TMP%\\discovery.js\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9551, "name": "discovery.js", "file_type": ".js", "file_size": 0, "path": "%TMP%\\discovery.js", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "adinformation***"}]}], "rewind_processes": [{"id": 6048, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\discovery.js\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 3, "name": "Delivery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"discovery.js\") OR (\"c2e09e4ee5268f2cb99f8d2877a772f35ba5ac13dd871bd8c38330502b42aade\" OR \"19f0f8ff1a5887126fbfbc4f6515b08ab9f1d1f4\" OR \"ed22ba03d804d29ee0369f131b44fe5e\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "CM-11", "description": "User-installed Software"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-08", "description": "Identification and Authentication (Non-Organizational Users)"}, {"capability_id": "IA-09", "description": "Service Identification and Authentication"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-16", "description": "Memory Protection"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 3783, "node_id": 1}, {"detail": {"id": 3782, "type": "Privilege Escalation", "result": ""}, "items": [{"detail": {"name": "Execute Command by using FacefodUninstaller DLL Search Order Hijacking", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 56, "mitre_id": "T1574", "mitre_name": "Hijack Execution Flow"}, "sub_technique": {"id": 132, "mitre_id": "T1574.001", "mitre_name": "DLL"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10515, "node_id": 4, "action": {"id": 10515, "display_id": 12557, "owasp_rank": "", "name": "Execute Command by using FacefodUninstaller DLL Search Order Hijacking", "release_date": "2022-06-18T12:22:02.872691Z", "description": "In this action, an attacker is trying to run MS Paint by doing  DLL Search Order Hijacking. In order to achieve that attacker places a fake DLL, WinBio.dll, in the same directory as FaceFodUninstaller.exe", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 56, "mitre_id": "T1574", "name": "Hijack Execution Flow"}, "sub_technique": {"id": 132, "mitre_id": "T1574.001", "name": "DLL"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6188, "name": "Process 1", "content": "C:\\Windows\\System32\\WinBioPlugIns\\FaceFodUninstaller.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 17269, "name": "WinBio.dll", "file_type": ".dll", "file_size": 0, "path": "C:\\Windows\\System32\\WinBioPlugIns\\WinBio.dll", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": -2147467261, "result_output": ""}]}, {"id": 12563, "name": "Process 2", "content": "{predefined-process-list} mspaint.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 5240, "name": "Process 1", "content": "{predefined-process-kill} mspaint.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6189, "name": "Process 2", "content": "{predefined-file-delete} C:\\Windows\\System32\\WinBioPlugIns\\WinBio.dll", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"FaceFodUninstaller.exe\") OR ((\"WinBio.dll\") OR (\"356b5680ede5f9c7592f240246bd940023e84a4c2a85b933060864acc980ae89\" OR \"6f6d3050b7b4017a19e267f3303f5ac1c1c3e5e6\" OR \"e712d3bc99e2db24216f4f4b4b9526f6\"))) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 3782, "node_id": 3}, {"detail": {"id": 3781, "type": "Execution", "result": ""}, "items": [{"detail": {"name": "Execute Shellcode by Reading a Registry Key", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "mitre_name": "Process Injection"}, "sub_technique": {"id": 723, "mitre_id": "T1055.003", "mitre_name": "Thread Execution Hijacking"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10518, "node_id": 6, "action": {"id": 10518, "display_id": 12560, "owasp_rank": "", "name": "Execute Shellcode by Reading a Registry Key", "release_date": "2022-06-18T12:22:02.820284Z", "description": "In this action, an attacker is trying to execute shellcode that is stored in a registry key using native API calls.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "name": "Process Injection"}, "sub_technique": {"id": 723, "mitre_id": "T1055.003", "name": "Thread Execution Hijacking"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6202, "name": "Process 1", "content": "powershell.exe -c Unblock-File '%TMP%\\Carbanak.ps1'; & '%TMP%\\Carbanak.ps1'", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9605, "name": "Carbanak.ps1", "file_type": ".ps1", "file_size": 0, "path": "%TMP%\\Carbanak.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6203, "name": "Process 2", "content": "%TMP%\\RegistryKeyReader.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9606, "name": "RegistryKeyReader.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\RegistryKeyReader.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12563, "name": "Process 3", "content": "{predefined-process-list} mspaint.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 5240, "name": "Process 1", "content": "{predefined-process-kill} mspaint.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6204, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\RegistryKeyReader.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6205, "name": "Process 3", "content": "reg.exe delete \"HKLM\\SOFTWARE\\Microsoft\\DRM\" /v \"Carbanak\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6206, "name": "Process 4", "content": "{predefined-file-delete} %TMP%\\Carbanak.ps1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( ((\"Carbanak.ps1\") OR (\"fc22dd17dfa9bfb2acb3d346bce51daf71a650ccb5413a03b16bc86ee7a2054f\" OR \"83d0d84864326a3091fd840044d8ce76d2dab869\" OR \"be2602bbfbbaeab6f2f3fcdc83f399fa\")) OR ((\"RegistryKeyReader\") OR (\"c263af9ff0767a1d27053933bdf3c42fde1cb1c4470c415ac25f2fdac9630960\" OR \"7830c722e27b61361c2023e291220ee547b54b5c\" OR \"19894fd196e65b33abfeea27520ea02b\")) ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Execute PillowMintInjection Tool Variant-2", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "mitre_name": "Process Injection"}, "sub_technique": {"id": 723, "mitre_id": "T1055.003", "mitre_name": "Thread Execution Hijacking"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35639, "node_id": 16, "action": {"id": 35639, "display_id": 5262007, "owasp_rank": "", "name": "Execute PillowMintInjection Tool Variant-2", "release_date": "2025-07-18T03:03:26.805604Z", "description": "In this action, an attacker is trying to execute PillowMintInjection tool to achieve mapping code injection technique used by PillowMint malware attributed to FIN7 Group.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "name": "Process Injection"}, "sub_technique": {"id": 723, "mitre_id": "T1055.003", "name": "Thread Execution Hijacking"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 11380, "name": "Process 1", "content": "powershell.exe -c Start-Process notepad", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 13715, "name": "Process 2", "content": "%TMP%\\PillowMintInjection.exe ", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": [{"id": 18022, "name": "PillowMintInjection.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\PillowMintInjection.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12508, "name": "Process 3", "content": "{predefined-process-list} winver.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 6208, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\PillowMintInjection.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12509, "name": "Process 2", "content": "{predefined-process-kill} winver.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12520, "name": "Process 3", "content": "{predefined-process-kill} notepad.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( ((\"PillowMintInjection\") OR (\"5f1bfb777a1caa0d3858ab10b54d2e0b3ac768b3feb174afb4d873f8a7ce5bb4\" OR \"bb1785ab9659a86b0d38eb1b66200250e1dde3fc\" OR \"b0f5cd76361a797753bc340442cacdef\")) OR (\"powershell.exe\" AND \"notepad\" AND \"Process\" AND \"Start\") ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 3781, "node_id": 5}, {"detail": {"id": 3780, "type": "Command and Control", "result": ""}, "items": [{"detail": {"name": "Execute Tinymet by using Reflective DLL", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 240, "mitre_id": "T1620", "mitre_name": "Reflective Code Loading"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10517, "node_id": 9, "action": {"id": 10517, "display_id": 12559, "owasp_rank": "", "name": "Execute Tinymet by using Reflective DLL", "release_date": "2022-06-18T12:22:02.840818Z", "description": "In this action, the attacker is trying to execute <PERSON><PERSON> with the bind server option on port 59200 by decrypting and injecting a Reflective DLL.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 240, "mitre_id": "T1620", "name": "Reflective Code Loading"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6196, "name": "Process 1", "content": "%TMP%\\rloader.exe \"%TMP%\\tinymet.bin\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9603, "name": "rloader.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\rloader.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 9604, "name": "tinymet.bin", "file_type": ".bin", "file_size": 0, "path": "%TMP%\\tinymet.bin", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6197, "name": "Process 2", "content": "cmd.exe /c timeout 5 && netstat -ona | findstr 59200", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6198, "name": "Process 1", "content": "{predefined-process-kill} rloader.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6199, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\rloader.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6200, "name": "Process 3", "content": "{predefined-file-delete} %TMP%\\tinymet.bin", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "( ((\"rloader.exe\") OR (\"ae4fe85656aab8b3cd78b52d0089078a528b2023692b67caa021c609cbc144d5\" OR \"35aa928d66f231a64a1f079859a438a31165f982\" OR \"87f1b4d1d6966deaa771643e8cc2653c\")) OR ((\"tinymet.bin\") OR (\"c6fda0ebeb613497f2d9fcca7ef94244f0eb1c1e17eb2211809183aeab46b6bf\" OR \"9dc42bea45a3c5adc5a64ba206e1b04eebde5cad\" OR \"8ae771ddefbe15089d1369cb11d3f73b\")) AND NOT (\"taskkill.exe\" OR ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\")))) )", "nist_capabilities": []}}, {"detail": {"name": "Start VNC Server by using Reflective DLL", "tag": "start", "result": "", "tactic": {"id": 15, "mitre_id": "TA0011", "mitre_name": "Command and Control"}, "technique": {"id": 152, "mitre_id": "T1219", "mitre_name": "Remote Access Tools"}, "sub_technique": {"id": 874, "mitre_id": "T1219.002", "mitre_name": "Remote Desktop Software"}, "phase": {"stage": "Initial Foothold", "phase_id": 8, "phase_name": "Command & Control", "phase_description": "Techniques that allow attackers to communicate with controlled systems within a target network."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10640, "node_id": 15, "action": {"id": 10640, "display_id": 5226787, "owasp_rank": "", "name": "Start VNC Server by using Reflective DLL", "release_date": "2022-06-18T12:22:01.456712Z", "description": "In this action, the attacker is trying to start VNC server on the target system by decrypting and injecting a Reflective DLL.", "mitre": {"tactic": {"id": 15, "mitre_id": "TA0011", "name": "Command and Control"}, "technique": {"id": 152, "mitre_id": "T1219", "name": "Remote Access Tools"}, "sub_technique": {"id": 874, "mitre_id": "T1219.002", "name": "Remote Desktop Software"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6397, "name": "Process 1", "content": "%TMP%\\winvnc.exe \"%TMP%\\winvnc.bin\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": [{"id": 9718, "name": "winvnc.exe", "file_type": "exe", "file_size": 0, "path": "%TMP%\\winvnc.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 9719, "name": "winvnc.bin", "file_type": ".bin", "file_size": 0, "path": "%TMP%\\winvnc.bin", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6398, "name": "Process 2", "content": "cmd.exe /c timeout 5 && tasklist /svc | findstr /i winvnc", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6399, "name": "Process 1", "content": "taskkill.exe /f /im winvnc.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6400, "name": "Process 2", "content": "cmd.exe /c del /s /q \"%TMP%\\winvnc.*\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 8, "name": "Command & Control"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((((\"winvnc.exe\") OR (\"ae4fe85656aab8b3cd78b52d0089078a528b2023692b67caa021c609cbc144d5\" OR \"35aa928d66f231a64a1f079859a438a31165f982\" OR \"87f1b4d1d6966deaa771643e8cc2653c\")) OR ((\"winvnc.bin\") OR (\"09a7efc2fd34480c458b8c949e43599c1138c417eee31d5e11a772405327aacc\" OR \"07a0f2143a38e8208cd3a34c0205f6de15191ce6\" OR \"b0bd25f2a7314bb9ec2790e7582f3d63\"))) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\")) OR \"taskkill\"))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-15", "description": "Information Output Filtering"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 3780, "node_id": 8}, {"detail": {"id": 3779, "type": "Collection", "result": ""}, "items": [{"detail": {"name": "Capture ScreenShot via Reflective DLL Injection", "tag": "capture", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 127, "mitre_id": "T1113", "mitre_name": "Screen Capture"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 16, "phase_name": "Collection", "phase_description": "Techniques used to identify and gather data from a target network prior to exfiltration."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10516, "node_id": 11, "action": {"id": 10516, "display_id": 12558, "owasp_rank": "", "name": "Capture ScreenShot via Reflective DLL Injection", "release_date": "2022-06-18T12:22:02.858786Z", "description": "In this action, an attacker is trying to reflectively load custom DLL in order to capture screenshot of victim's machine.", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 127, "mitre_id": "T1113", "name": "Screen Capture"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6190, "name": "Process 1", "content": "mspaint.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6191, "name": "Process 2", "content": "powershell.exe -c \"%TMP%\\ReflectiveInjector.exe $(ps mspaint).id %TMP%\\ScreenShotCapturer.dll\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9601, "name": "ReflectiveInjector.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\ReflectiveInjector.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 9602, "name": "ScreenShotCapturer.dll", "file_type": ".dll", "file_size": 0, "path": "%TMP%\\ScreenShotCapturer.dll", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "[+] Injected the"}]}, {"id": 6192, "name": "Process 3", "content": "cmd.exe /c dir \"%TMP%\\ScreenShot.jpeg\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6193, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\ScreenShot.jpeg\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6194, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\ReflectiveInjector.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 5824, "name": "Process 3", "content": "taskkill.exe /f /im mspaint.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6195, "name": "Process 4", "content": "cmd.exe /c del \"%TMP%\\ScreenShotCapturer.dll\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 16, "name": "Collection"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( ((\"ReflectiveInjector\") OR (\"03b171222c51f6cffd790cf91ec018522eea2a4ce3c0bb8f56322ac5bdcb354d\" OR \"31f1a92c87e77b696c9bdbacb609453ae3a866b0\" OR \"6b2497ad7bab82667bbb78d376982881\")) OR ((\"ScreenShotCapturer\") OR (\"3659bee361cc29e98b8cd86dfe6949b4278b78740a5904bf83dd0f997c3f22a2\" OR \"b0ffda197e6a84ea153c3ed81ac23bdfef4cfa13\" OR \"672952e63afe47b0e9160ea697976d60\")) ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": []}}, {"detail": {"name": "Execute Keylogger by using Reflective DLL", "tag": "execute", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 83, "mitre_id": "T1056", "mitre_name": "Input Capture"}, "sub_technique": {"id": 219, "mitre_id": "T1056.001", "mitre_name": "Keylogging"}, "phase": {"stage": "Initial Foothold", "phase_id": 3, "phase_name": "Delivery", "phase_description": "Techniques resulting in the transmission of a weaponized object to the targeted environment."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10525, "node_id": 12, "action": {"id": 10525, "display_id": 12568, "owasp_rank": "", "name": "Execute Keylogger by using Reflective DLL", "release_date": "2022-06-18T12:22:02.739155Z", "description": "In this action, the attacker is trying to execute a keylogger, which uses RegisterRawInputDevices API call, by injecting a shellcode with Reflective loader stub.", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 83, "mitre_id": "T1056", "name": "Input Capture"}, "sub_technique": {"id": 219, "mitre_id": "T1056.001", "name": "Keylogging"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6229, "name": "Process 1", "content": "%TMP%\\srdinject.exe \"%TMP%\\srdikeylog.bin\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9608, "name": "srdinject.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\srdinject.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 9619, "name": "srdikeylog.bin", "file_type": ".bin", "file_size": 0, "path": "%TMP%\\srdikeylog.bin", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6230, "name": "Process 2", "content": "{predefined-process-list} srdinject.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6210, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\srdi*", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6231, "name": "Process 2", "content": "{predefined-process-kill} srdinject.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6232, "name": "Process 3", "content": "{predefined-file-delete} C:\\Windows\\Temp\\keylog.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 3, "name": "Delivery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( ((\"srdinject.exe\") OR (\"109041cf47217a5920ea45283e58b2e46525caa5e9042ecbc770301c2819646d\" OR \"283ca2af618ee5cb3bbf43698d650cff07014384\" OR \"95aedef07fe5651bf572aefbb80b77bf\")) OR ((\"srdikeylog.bin\") OR (\"0f3630d8372b42c836414ae815e44436a3e545ce12a856910743d4f214dcea84\" OR \"0b598f17cfda582d93c7dc548e9e20f7559c3c29\" OR \"38270c9a8b1e46b86ce2f88b22942149\")) ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\")) OR \"taskkill\" OR \"0xffffffff\"))", "nist_capabilities": []}}], "true_items": [], "false_items": [], "type": "objective", "id": 3779, "node_id": 10}, {"detail": {"id": 3778, "type": "Credential Access", "result": ""}, "items": [{"detail": {"name": "Execute Mimikatz by using Reflective DLL", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 240, "mitre_id": "T1620", "mitre_name": "Reflective Code Loading"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10520, "node_id": 14, "action": {"id": 10520, "display_id": 12562, "owasp_rank": "", "name": "Execute Mimikatz by using Reflective DLL", "release_date": "2022-06-18T12:22:02.790886Z", "description": "In this action, the attacker is trying to execute mimikatz by injecting a shellcode with Reflective loader stub.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 240, "mitre_id": "T1620", "name": "Reflective Code Loading"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6209, "name": "Process 1", "content": "%TMP%\\srdinject.exe \"%TMP%\\srdimimi.bin\" coffee exit", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9608, "name": "srdinject.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\srdinject.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 9609, "name": "srdimimi.bin", "file_type": ".bin", "file_size": 0, "path": "%TMP%\\srdimimi.bin", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "notmimi 2.2.0 (x64)"}]}], "rewind_processes": [{"id": 6210, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\srdi*", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "( ((\"srdinject.exe\") OR (\"109041cf47217a5920ea45283e58b2e46525caa5e9042ecbc770301c2819646d\" OR \"283ca2af618ee5cb3bbf43698d650cff07014384\" OR \"95aedef07fe5651bf572aefbb80b77bf\")) OR ((\"srdimimi.bin\") OR (\"5ee45134d8891ce0f6c9c23977950b5bf83235dbf23c8eef98fb4b4a0fdc8c5c\" OR \"1c99f86f014f0a65e8e416328454cf66f24265d1\" OR \"0e3da640a431d89b51c6c766e0973b9b\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))) )", "nist_capabilities": []}}], "true_items": [], "false_items": [], "type": "objective", "id": 3778, "node_id": 13}], "campaign_condition": "OBJ1 and OBJ2 and OBJ3 and OBJ4 and OBJ5 and OBJ6", "objective_conditions": [{"id": 3778, "condition": "A1"}, {"id": 3779, "condition": "A1 and A2"}, {"id": 3780, "condition": "A1 and A2"}, {"id": 3781, "condition": "A1 and A2"}, {"id": 3782, "condition": "A1"}, {"id": 3783, "condition": "A1"}], "assessment_run_status": "", "assessment_da_run_status": "", "obfuscation_methods": null, "detection_licence_modules": null}}