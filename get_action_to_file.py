import json
import requests
import io
import urllib3
import re
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def get_rules(token, host):
    headers = {'Authorization': token, "Content-Type": "application/json;charset=UTF-8"}
    url = host + '/action/list'
    data = {"page_index":1,"page_number":5000,"tags":[{"action_type":{"data":["host_cli"],"logic":"="},"is_self":{"data":["false"],"logic":"="}}]}

    try:
        response = requests.post(url, json=data, headers=headers, verify=False)
        datas = json.load(io.StringIO(response.text))

        for data in datas["datas"]:
            if 'windows' in data["os"]:
                try:
                    url = host + '/action/info'
                    json_data = {"id":data["uuid"]}
                    response = requests.post(url, json=json_data, headers=headers, verify=False)
                    info = json.load(io.StringIO(response.text))
                    save_json_to_file(info)

                except requests.exceptions.RequestException as e:
                    print('请求异常:', str(e))
                    exit(0)

    except requests.exceptions.RequestException as e:
        print('请求异常:', str(e))
        exit(0)


def save_json_to_file(info):
    action = {"actions": {}}
    uuid = info["data"]["uuid"]
    os = info["data"]["os"]
    must_sandbox = info["data"]["must_sandbox"]

    # print_str = "{} {} {}".format(info["data"]["vid"], uuid, info["data"]["name"])
    # print(print_str)

    if os == "windows":
        if must_sandbox == True:
            file_name_path = "./hostcli_json_file/Windows/sandbox/{}.json".format(uuid)
        else:
            file_name_path = "./hostcli_json_file/Windows/no_sandbox/{}.json".format(uuid)

    # elif os == "linux":
    #     if must_sandbox == True:
    #         file_name_path = "./hostcli_json_file/Linux/sandbox/{}.json".format(uuid)
    #     else:
    #         file_name_path = "./hostcli_json_file/Linux/no_sandbox/{}.json".format(uuid)

    # elif os == "unknow":
    #     file_name_path = "./hostcli_json_file/macOS/{}.json".format(uuid)

    # else:
    #     file_name_path = "./hostcli_json_file/Other/{}.json".format(uuid)

    data = {uuid: {}}

    payload = info["payload"]
    info["data"]["host_cli_action"] = payload
    data[uuid] = info["data"]
    action["actions"].update(data)

    with open(file_name_path, 'w') as file:
        json.dump(action, file, ensure_ascii=False, indent=4)


if __name__ == '__main__':
    host  = "https://100055.secvision.cloud"
    token = "7959fef6ffad317170872d97c4f6bf86f794fb44b3bdc66cb305acc47f61a0b0"
    get_rules(token,host)
