{"actions": {"3e4997c9-82df-401c-bb04-a3869a15c58a": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "ver": 16, "vid": "370354", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 DLL 侧加载技术 (LicensingUI.exe) 加载 DLL 文件", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试通过 Microsoft 的 LicensingUI 应用程序使用 DLL 侧加载来加载 DLL 文件。", "created_at": "2025-07-21 21:41:02", "updated_at": "2025-07-21 21:41:02", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "mitre_techniques", "tag": "T1574.001 - DLL", "tag_en": "T1574.001 - DLL", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "3e4997c9-82df-401c-bb04-a3869a15c58a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_tactics TA0005 - 防御绕过", "control AV", "run_as SYSTEM", "os Windows:Server 2019", "os Windows:Server 2016", "mitre_techniques T1574.002 - DLL侧加载", "os Windows:11", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "run_as User", "control HIDS", "os Windows:Server 2022", "os Windows:10", "control EDR/XDR"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "UI.exe", "file_name": "LicensingUI.exe", "file_owner": "system", "file_transfer_library": 999057730, "file_type": "exe", "filesize": 146816, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "4d691fd9626928959bd89d64b6814b1d", "sha1sum": "0c9666e1937aa22d948f149700ec791a6c428956", "sha256sum": "05ce2a7d84107eaa48d2a184caa1da5c91758c148459f2d108134a92697a00e7"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "dui70.dll", "file_name": "DUI70.dll", "file_owner": "system", "file_transfer_library": 999057731, "file_type": "dll", "filesize": 107008, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "832240b3c207367925524c707e569e02", "sha1sum": "c3a1435ed405296dd9932a1063d34720d6506e1f", "sha256sum": "a5d428263f5d096819c6d31ec0bdb339f08020efd3ad9ba267c279d56593ec67"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\UI.exe\r\ntasklist /svc | findstr /i winver.exe\r\ntaskkill.exe /f /im winver.exe & taskkill.exe /f /im UI.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\UI.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i winver.exe", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im winver.exe & taskkill.exe /f /im UI.exe", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}