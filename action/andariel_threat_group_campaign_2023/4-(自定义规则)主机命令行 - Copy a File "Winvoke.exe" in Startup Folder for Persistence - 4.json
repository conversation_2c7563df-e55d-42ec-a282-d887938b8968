{"action": {"name": "(自定义规则)主机命令行 - Copy a File \"Winvoke.exe\" in Startup Folder for Persistence - 4", "desc": "In this action, an attacker tries to copy a file from the Public Downloads directory to the current user's Startup folder.[andariel_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 2, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Users", "file_owner": "system", "file_transfer_library": 998001950, "randomize_file": null, "file_name": "dummy.exe"}], "steps": [{"step_order": 1, "command": "cmd.exe /c copy \"C:\\Users\\<USER>\\Downloads\\Winvoke.exe\" \"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\StartUp\\\"\n", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c timeout 5 && dir \"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\StartUp\\Winvoke.exe\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1547"], "mitre_mitigation": []}}, "picus_id": 24178, "uuid": "7ec2af60-1819-42f2-bc26-3b1d4b333599", "sigma_device_query": "title: Persistence via File Transport to Startup Folder\nstatus: experimental\ndescription: Detects the attempt to copy or move a file into startup folder. This technique is commonly utilized for persistence.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1547/001/\n   - https://attack.mitre.org/tactics/TA0003/\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/copy\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/move\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        ProcessCommandLine: \n            - '*copy* *Microsoft\\Windows\\Start Menu\\Programs\\StartUp*'\n            - '*move* *Microsoft\\Windows\\Start Menu\\Programs\\StartUp*'\n    condition: selection\nfalsepositives:\n    - Usual user activity\nlevel: low\ntags:\n    - attack.persistence\n    - attack.t1547.001\n    - attack.ta0003"}