{"actions": {"59beaf65-41e4-4885-bd78-9cdc51da0ad6": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "ver": 16, "vid": "374686", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 将用户按键收集到文件中", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试使用 PsTools 将击键捕获到先前创建的日志文件中。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:06", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "malware_os", "tag": "Windows", "tag_en": "Windows", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "malware_type", "tag": "黑客工具", "tag_en": "Hacking Tool", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "mitre_techniques", "tag": "T1056.001 - 键盘记录", "tag_en": "T1056.001 - Keylogging", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "59beaf65-41e4-4885-bd78-9cdc51da0ad6", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["run_as Admin", "run_as SYSTEM", "os Windows:10", "mitre_tactics TA0009 - 信息收集", "control AV", "control EDR/XDR", "control HIDS", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "os Windows:Server 2022", "os Windows:11", "os Windows:Server 2019", "os Windows:Server 2016", "mitre_techniques T1056.001 - 键盘记录"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "psversion.ps1", "file_name": "psversion.ps1", "file_owner": "system", "file_transfer_library": 999055402, "file_type": "ps1", "filesize": 15764, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "57910215db5f6f23457d689b6a815234", "sha1sum": "39a28f19ca243218d81a430180220e00ddd26253", "sha256sum": "8792d6dfcea59dd5d1be8f71d738526762a27cd4fed69ebb57d6f2260d9e6320"}], "monitor_connections": "off", "raw_text": "powershell.exe -c \"New-Item -ItemType Directory -Force -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs'; New-Item -ItemType File -Force -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs' -Name ((Get-Date).ToString('dd-MM-yyyy') + '_sectest.txt')\"\r\npowershell.exe -c \"$fileName=((Get-Date).tostring('dd-MM-yyyy') + '_sectest.txt'); Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Keystroke-Check; Get-Keystrokes; View-Job -JobName 'Keystrokes' | Out-File -Append 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs\\$fileName'\"\r\npowershell.exe -c \"Remove-Item -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs' -Recurse -Force\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"New-Item -ItemType Directory -Force -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs'; New-Item -ItemType File -Force -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs' -Name ((Get-Date).ToString('dd-MM-yyyy') + '_sectest.txt')\"", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"$fileName=((Get-Date).tostring('dd-MM-yyyy') + '_sectest.txt'); Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Keystroke-Check; Get-Keystrokes; View-Job -JobName 'Keystrokes' | Out-File -Append 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs\\$fileName'\"", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "powershell.exe -c \"Remove-Item -Path 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Logs' -Recurse -Force\"", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}