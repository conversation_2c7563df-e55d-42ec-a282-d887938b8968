{"actions": {"4c65a797-370d-4c89-82e6-a4e81f219c8c": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "ver": 16, "vid": "381066", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过 SharpZeroLogon 工具检查 Windows Netlogon 漏洞", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略；加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制；提升漏洞扫描频率。中长期建议：增加特权帐号管理产品以加强对身份信息系统的监测与保护；做好应用程序隔离；加强互联网访问的管控与下载文件检测，并加强内网网络流量的威胁文件传输检测；在内部各个安全区域之间部署NIPS加强检测与防御；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警；加强企业员工安全意识培训。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图利用 Windows Netlogon (CVE-2020-1472) 漏洞，以便使用 SharpZeroLogon 工具获取域管理员权限。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:25", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [{"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "source": "奇安信天擎", "message": "QDE.V2.3.36SQ9EIO8Y.VX", "source_identifier": "", "cn_show": true, "product_version": "", "signature_version": ""}], "tags": [{"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "ics_mitre_tactics", "tag": "TA0109 - 横向移动", "tag_en": "TA0109 - Lateral Movement", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "ics_mitre_techniques", "tag": "T0866 - 远程服务利用", "tag_en": "T0866 - Exploitation of Remote Services", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1016", "tag_en": "M1016", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1019", "tag_en": "M1019", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1030", "tag_en": "M1030", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1042", "tag_en": "M1042", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1048", "tag_en": "M1048", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1050", "tag_en": "M1050", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_mitigation", "tag": "M1051", "tag_en": "M1051", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_tactics", "tag": "TA0008 - 横向移动", "tag_en": "TA0008 - Lateral Movement", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "mitre_techniques", "tag": "T1210 - 远程服务溢出", "tag_en": "T1210 - Exploitation of Remote Services", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "4c65a797-370d-4c89-82e6-a4e81f219c8c", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1050", "ATT&CK:M1019", "ATT&CK:M1016", "ATT&CK:M1026", "ATT&CK:M1030", "ATT&CK:M1042", "ATT&CK:M1048", "ATT&CK:M1051"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1210", "ATT&CK:Lateral Movement"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "SharpZeroLogon.exe", "file_name": "SharpZeroLogon.exe", "file_owner": "system", "file_transfer_library": 999051507, "file_type": "exe", "filesize": 8704, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "7e5adaf5775396c7853df0d2629c4879", "sha1sum": "640bae2c8faac539813556e14aaca62ead9cf4df", "sha256sum": "92cdfa1ed7c76982a4d5c84d4e8fd14af76b678297897a70d689a6e819feb8f7"}], "monitor_connections": "off", "raw_text": "powershell.exe -c \"$dcname=[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().PdcRoleOwner.Name; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpZeroLogon.exe' $dcname\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"$dcname=[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().PdcRoleOwner.Name; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpZeroLogon.exe' $dcname\"", "incompatible_check": "match", "incompatible_match": "ActiveDirectoryOperationException", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "Success!\\ DC\\ can\\ be\\ fully\\ compromised\\ by\\ a\\ Zerologon\\ attack\\.", "success_order": 3, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}