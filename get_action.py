import json
import requests
import io
import urllib3
import re
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

actions = {"actions": {}}

sequences = {"sequences": []}


vids = [
    "8001476",
    "8001475",
    "8001474",
    "8001473",
    "8001472",
    "8001471",
    "8001470",
    "8001469",
    "8001468",
    "8001467",
    "8001466",
]

sequences_vid = [
    "8001484",
    "8001483",
    "8001482",
    "8001481",
    "8001480",
    "8001479",
    "8001478",
    "8001477",
    "8001476",
    "8001452",
    "8001451",
]


def get_rules(token, host):
    headers = {'Authorization': token, "Content-Type": "application/json;charset=UTF-8"}
    url = host + '/action/list'
    data = {"page_number":57,"page_index":1,"tags":[{"is_self":{"data":["true"],"logic":"="},"search":{"data":["(自定义规则)命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTPS"],"logic":"="}}]}
    
    try:
        response = requests.post(url, json=data, headers=headers, verify=False)
        datas = json.load(io.StringIO(response.text))

        for data in datas["datas"]:
            try:
                url = host + '/action/info'
                json_data = {"id":data["uuid"]}
                response = requests.post(url, json=json_data, headers=headers, verify=False)
                info = json.load(io.StringIO(response.text))
                save_json_to_file(info)

            except requests.exceptions.RequestException as e:
                print('请求异常:', str(e))
                exit(0)

    except requests.exceptions.RequestException as e:
        print('请求异常:', str(e))
        exit(0)


def get_rules_vids(token, host):
    for vid in vids:
        try:
            headers = {'Authorization': token, "Content-Type": "application/json;charset=UTF-8"}
            url = host + '/action/list'
            # data = {"page_number":1,"page_index":1,"tags":[{"search":{"data":[vid],"logic":"="}}]}
            data = {"page_number":1,"page_index":1,"tags":[{"search":{"data":[vid],"logic":"="}}]}

            response = requests.post(url, json=data, headers=headers, verify=False)
            datas = json.load(io.StringIO(response.text))

            for data in datas["datas"]:
                try:
                    url = host + '/action/info'
                    json_data = {"id":data["uuid"]}
                    response = requests.post(url, json=json_data, headers=headers, verify=False)
                    info = json.load(io.StringIO(response.text))
                    save_json_to_file(info)

                except requests.exceptions.RequestException as e:
                    print('请求异常:', str(e))
                    exit(0)

        except requests.exceptions.RequestException as e:
            print('请求异常:', str(e))
            exit(0)


def get_sequences_vids(token, host):
    for vid in sequences_vid:
        try:
            headers = {'Authorization': token, "Content-Type": "application/json;charset=UTF-8"}
            url = host + '/sequence/list'
            data = {"page_number":1,"page_index":1,"tags":[{"search":{"data":[vid],"logic":"="}}]}

            response = requests.post(url, json=data, headers=headers, verify=False)
            datas = json.load(io.StringIO(response.text))

            for data in datas["datas"]:
                try:
                    url = host + '/sequence/info'
                    json_data = {"id":data["id"]}
                    response = requests.post(url, json=json_data, headers=headers, verify=False)
                    info = json.load(io.StringIO(response.text))
                    save_sequences_to_file(info)

                except requests.exceptions.RequestException as e:
                    print('请求异常:', str(e))
                    exit(0)

        except requests.exceptions.RequestException as e:
            print('请求异常:', str(e))
            exit(0)


def get_rules_vids_print(token, host):
    for vid in vids:
        try:
            headers = {'Authorization': token, "Content-Type": "application/json;charset=UTF-8"}
            url = host + '/action/list'
            # data = {"page_number":1,"page_index":1,"tags":[{"search":{"data":[vid],"logic":"="}}]}
            data = {"page_number":1,"page_index":1,"tags":[{"search":{"data":[vid],"logic":"="}}]}

            response = requests.post(url, json=data, headers=headers, verify=False)
            datas = json.load(io.StringIO(response.text))

            for data in datas["datas"]:
                try:
                    url = host + '/action/info'
                    json_data = {"id":data["uuid"]}
                    response = requests.post(url, json=json_data, headers=headers, verify=False)
                    info = json.load(io.StringIO(response.text))
                    print_action_info(info)

                except requests.exceptions.RequestException as e:
                    print('请求异常:', str(e))
                    exit(0)

        except requests.exceptions.RequestException as e:
            print('请求异常:', str(e))
            exit(0)


def print_action_info(info):
    uuid = info["data"]["uuid"]
    print_str = "{} {} {}".format(info["data"]["vid"], uuid, info["data"]["name"])
    print(print_str)


def save_json_to_file(info):
    uuid = info["data"]["uuid"]

    print_str = "{} {} {}".format(info["data"]["vid"], uuid, info["data"]["name"])
    print(print_str)

    # pcap_action = info["data"]["pcap_action"]["pcap_library"]["file_location"]
    # print_str = "{} {}".format(pcap_action, info["data"]["name"])
    # print(print_str)


    data = {uuid: {}}

    if 'host_cli' in info["data"]["action_type"]:
        payload = info["payload"]
        info["data"]["host_cli_action"] = payload
        data[uuid] = info["data"]
        actions["actions"].update(data)

    elif "pcap" in info["data"]["action_type"]:
        payload = info["payload"]
        info["data"]["pcap_action"] = payload
        data[uuid] = info["data"]
        actions["actions"].update(data)

    elif "file_transfer" in info["data"]["action_type"]:
        payload = info["payload"]
        info["data"]["file_transfer_action"] = payload
        data[uuid] = info["data"]
        actions["actions"].update(data)

    elif "dns" in info["data"]["action_type"]:
        payload = info["payload"]
        info["data"]["dns_action"] = payload
        data[uuid] = info["data"]
        actions["actions"].update(data)

    elif "email" in info["data"]["action_type"]:
        payload = info["payload"]
        info["data"]["email_action"] = payload
        data[uuid] = info["data"]
        actions["actions"].update(data)

    elif "website" in info["data"]["action_type"]:
        payload = info["payload"]
        info["data"]["web_action"] = payload
        data[uuid] = info["data"]
        actions["actions"].update(data)


def save_sequences_to_file(info):
    print_str = "{} {} {}".format(info["vid"], info["id"], info["name"])
    print(print_str)

    data = {}

    data["profiles"] = info["sequence_steps"]
    data["profiles"] = info["sequence_steps"]


    sequences["sequences"].append(info)


def check_windows_host_cli_runas(info):
    command = info["data"]["host_cli_action"]["steps"][0]["command"]
    if "checkDebugprivilege.ex" in command:
        print(command)
        runas_tags = info["data"]["tags"]
        for runas_tag in runas_tags:
            if runas_tag["tag_type"] == "run_as":
                if runas_tag["tag"] == "SYSTEM":
                    print_str = "{} {} {}".format(info["data"]["vid"], info["data"]["uuid"], info["data"]["name"])
                    with open("./rules.txt", "a", encoding="utf-8") as f:
                        f.write(print_str + "\n")


if __name__ == '__main__':
    host  = "https://100000.secvision.cloud"
    token = "939d7a1ac4516a9f47815d7a7cb8188badada33f29001501f33d22adde209554"
    
    flag = 3
    if flag == 0:
        get_rules(token,host)
        with open('./export/actions.json', 'w') as file:
            json.dump(actions, file, ensure_ascii=False, indent=4)
    elif flag == 1:
        get_rules_vids(token, host)
        with open('./export/actions.json', 'w') as file:
            json.dump(actions, file, ensure_ascii=False, indent=4)
    elif flag == 2:
        get_rules_vids_print(token, host)
    elif flag == 3:
        get_sequences_vids(token, host)
        with open('./export/sequences.json', 'w') as file:
            json.dump(sequences, file, ensure_ascii=False, indent=4)



    # host  = "https://100055.secvision.cloud"
    # token = "7959fef6ffad317170872d97c4f6bf86f794fb44b3bdc66cb305acc47f61a0b0"
    # get_rules(token,host)
    # with open('./export/actions.json', 'w') as file:
    #     json.dump(actions, file, ensure_ascii=False, indent=4)

