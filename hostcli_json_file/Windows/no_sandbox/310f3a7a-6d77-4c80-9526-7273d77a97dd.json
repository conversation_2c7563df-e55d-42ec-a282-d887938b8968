{"actions": {"310f3a7a-6d77-4c80-9526-7273d77a97dd": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "ver": 16, "vid": "370373", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 修改当前用户配置文件的注册表", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者通过 cmd 控制台使用 reg.exe 修改当前登录用户的注册表。", "created_at": "2025-07-21 21:40:51", "updated_at": "2025-07-21 21:40:51", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "mitre_mitigation", "tag": "M1024", "tag_en": "M1024", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "mitre_techniques", "tag": "T1112 - 修改注册表", "tag_en": "T1112 - Modify Registry", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "310f3a7a-6d77-4c80-9526-7273d77a97dd", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2019", "os Windows:Server 2016", "mitre_tactics TA0005 - 防御绕过", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "os Windows:Server 2022", "os Windows:10", "mitre_techniques T1112 - 修改注册表", "mitre_mitigation ATT&CK:M1024", "os Windows:11", "control EDR/XDR", "control HIDS", "run_as SYSTEM", "run_as User"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "reg.exe add HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced /t REG_DWORD /v HideFileExt /d 1 /f\r\nreg.exe add HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced /t REG_DWORD /v HideFileExt /d 0 /f\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "reg.exe add HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced /t REG_DWORD /v HideFileExt /d 1 /f", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "reg.exe add HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced /t REG_DWORD /v HideFileExt /d 0 /f", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}