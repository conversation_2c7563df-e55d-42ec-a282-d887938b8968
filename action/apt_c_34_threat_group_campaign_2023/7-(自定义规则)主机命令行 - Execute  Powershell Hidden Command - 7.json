{"action": {"name": "(自定义规则)主机命令行 - Execute  Powershell Hidden Command - 7", "desc": "In this action, an attacker is trying to used the \"-W Hidden\" to conceal PowerShell windows by setting the WindowStyle parameter to hidden[apt_c_34_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -W Hidden -Command \"write-host test\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1564"], "mitre_mitigation": []}}, "picus_id": 2533, "uuid": "e78548c0-dd0c-4eb3-9e80-5a327082102f", "sigma_device_query": "title: Command Execution without Displaying Window via PowerShell\nstatus: stable\ndescription: Detects the attempt to execute PowerShell command without displaying a window. Adversaries may use hidden windows to conceal malicious activity from the plain sight of users.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1564/003/\n   - https://attack.mitre.org/tactics/TA0005/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\powershell.exe'\n        ProcessCommandLine|contains: 'hidden'\n    parameter:\n        ProcessCommandLine|contains:\n            - '-win'\n            - '-w'\n            - '-window'\n            - '-windowstyle'\n    condition: selection and parameter\nfalsepositives:\n    - Legitimate administrative activities\n    - Administrative tools/scripts\nlevel: medium\ntags:\n    - attack.defense_evasion\n    - attack.ta0005\n    - attack.t1564.003"}