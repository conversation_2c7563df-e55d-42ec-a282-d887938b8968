{"action": {"name": "(自定义规则)主机命令行 - List Currently Connected Network Shares - 12", "desc": "In this action, the net command is using to list currently connected network shares on the victim system.[apt41_threat_group_campaign_2019]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c net.exe use >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\1.txt\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1135"], "mitre_mitigation": ["ATT&CK:M1028"]}}, "picus_id": 10719, "uuid": "8202140f-61a9-4355-b46c-896314ba7291", "sigma_device_query": "title: Network Connections Discovery via Net Tool\nstatus: experimental\ndescription: Detects the attempt to obtain list of network connections via \"net use\" command of net tool. This technique is commonly utilized for system network connections discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1049/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://ss64.com/nt/net-use.html\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: \n            - '*\\net.exe'\n            - '*\\net1.exe'\n        ProcessCommandLine: \n            - '* use'\n            - '* use *'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1049\n    - attack.ta0007"}