{"action": {"name": "(自定义规则)主机命令行 - Execute a DLL via Rundll32 over LNK File - 3", "desc": "In this action, an attacker is trying to execute a dll via Rundll32.exe over a malicious shorcut (.LNK) file.[bumblebee_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "notepadSpawn.lnk", "file_owner": "system", "file_transfer_library": 998002330, "randomize_file": null, "file_name": "notepadSpawn.lnk"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "notepadSpawn.dll", "file_owner": "system", "file_transfer_library": 998002331, "randomize_file": null, "file_name": "notepadSpawn.dll"}], "steps": [{"step_order": 1, "command": "cmd.exe /c cd \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\" & .\\notepadSpawn.lnk", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c timeout 5 && tasklist /svc | findstr /i notepad", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "otepad\\.exe", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Initial Access", "ATT&CK:T1566"], "mitre_mitigation": ["ATT&CK:M1054", "ATT&CK:M1049", "ATT&CK:M1031", "ATT&CK:M1021", "ATT&CK:M1017"]}}, "picus_id": 21178, "uuid": "cfd30948-f144-4490-aaf1-5acbaad2177d", "sigma": {"name": "Sigma", "rules": [{"ruleName": "Suspicious Shortcut File Execution", "ruleId": "3838", "description": "Detects the execution of shortcut (.lnk) file which spawns commandline, dropper or C2 connection tools. This technique is commonly utilized by adversaries to stay frosty on systems and to bypass defensive mechanisms such as blocked portable executable file sharing via e-mail.", "releaseDate": 1589932800000, "updateDate": 1663027200000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Suspicious Shortcut File Execution\nstatus: stable\ndescription: Detects the execution of shortcut (.lnk) file which spawns commandline, dropper or C2 connection tools. This technique is commonly utilized by adversaries to stay frosty on systems and to bypass defensive mechanisms such as blocked portable executable file sharing via e-mail. \nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1547/009/\n   - https://attack.mitre.org/tactics/TA0003/\n   - https://attack.mitre.org/groups/G0016/\n   - https://attack.mitre.org/tactics/TA0004/\n   - https://www.fireeye.com/blog/threat-research/2018/11/not-so-cozy-an-uncomfortable-examination-of-a-suspected-apt29-phishing-campaign.html\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 1\n        ParentCommandLine: '*.lnk*'\n        Image: \n          - '*\\cmd.exe'\n          - '*\\powershell.exe'\n          - '*\\mshta.exe'\n          - '*\\bitsadmin.exe'\n          - '*\\rundll32.exe'\n          - '*\\regsvr32.exe'\n    condition: selection\nfalsepositives:\n    - Legitimate Windows processes\nlevel: high\ntags:\n    - attack.persistence\n    - attack.privilege_escalation\n    - attack.t1547.009\n    - attack.ta0003\n    - attack.ta0004\n    - attack.g0016", "logSource": {"productName": "windows", "service": "sysmon", "policies": null}, "falsePositives": ["Legitimate Windows processes"], "mitre": [{"name": "Shortcut Modification", "mitreId": "T1547.009", "url": "https://attack.mitre.org/techniques/T1547/009/", "type": "sub_technique"}]}]}}