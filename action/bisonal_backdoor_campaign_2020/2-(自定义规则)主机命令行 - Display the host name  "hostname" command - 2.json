{"action": {"name": "(自定义规则)主机命令行 - Display the host name  \"hostname\" command - 2", "desc": "\"hostname\" displays the host name portion of the full computer name of the computer. (before exfiltration to a C2)[bisonal_backdoor_campaign_2020]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 2, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c hostname >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1082"], "mitre_mitigation": []}}, "picus_id": 10736, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Computer Name Discovery", "ruleId": "5783", "description": "Detects to gather host name of the computer via \"hostname\" command. This technique is commonly utilized for discovery.", "releaseDate": 1573430400000, "updateDate": 1619481600000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Computer Name Discovery\nstatus: experimental\ndescription: Detects to gather host name of the computer via \"hostname\" command. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1082/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/hostname\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\hostname.exe'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1082\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}]}]}, "uuid": "dbef0366-11cd-4e51-bf38-d859a342e4d1", "sigma_device_query": "title: Computer Name Discovery\nstatus: experimental\ndescription: Detects to gather host name of the computer via \"hostname\" command. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1082/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/hostname\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\hostname.exe'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1082\n    - attack.ta0007"}