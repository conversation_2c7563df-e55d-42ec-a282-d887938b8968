{"action": {"name": "(自定义规则)主机命令行 - Perform Port Scanning by using a Powershell Script - 14", "desc": "In this action,  an attacker uses a PowerShell script to scan internal network over Port 80,81.[apt41_threat_group_campaign_2019]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "T1046.ps1", "file_owner": "system", "file_transfer_library": 998002172, "randomize_file": null, "file_name": "T1046.ps1"}], "steps": [{"step_order": 1, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1046.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1046.ps1'", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1046"], "mitre_mitigation": ["ATT&CK:M1030", "ATT&CK:M1031", "ATT&CK:M1042"]}}, "picus_id": 10725, "uuid": "c79e28d5-bdba-4654-a363-79157bc15869", "sigma_device_query": "title: Port Scanning via PowerShell\nstatus: stable\ndescription: Detects the attempt to scan ports for given range via native PowerShell functions. This technique is commonly utilized for discovering network services.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1046/\n    - https://attack.mitre.org/tactics/TA0007/\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: 4104\n        Message|contains|all:\n            - 'Net.Sockets.TcpClient'\n            - '.Connected'\n    keywords:\n        Message|contains:\n            - '.Connect('\n            - '.BeginConnect('\n            - '.ConnectAsync('\n    condition: selection and keywords\nfalsepositives: \n    - Administrative tools/scripts\nlevel: high\ntags:\n    - attack.discovery\n    - attack.t1046\n    - attack.ta0007"}