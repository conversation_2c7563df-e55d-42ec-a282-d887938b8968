{"actions": {"188e097a-62ed-4663-9bd1-65f4229f48fe": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 1, "uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "ver": 16, "vid": "377246", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 创建目录作为Workspace", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强用户在系统内安装、卸载、执行软件等权限控制；加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制以及执行PowerShell等权限控制；提高终端/主机安全产品的签名库的更新频率；针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略，监控可疑命令执行。中长期建议：加强应用程序的代码签名检测；加强互联网访问的管控与下载文件检测，并加强内网网络流量的威胁文件传输检测；部署特权帐号管理产品以加强对身份信息系统的监测与保护；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图在APPDATA本地下创建一个名为z_％USERNAME％的目录。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:55", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1021", "tag_en": "M1021", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1038", "tag_en": "M1038", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1040", "tag_en": "M1040", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1042", "tag_en": "M1042", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1045", "tag_en": "M1045", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_mitigation", "tag": "M1049", "tag_en": "M1049", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_tactics", "tag": "TA0002 - 攻击执行", "tag_en": "TA0002 - Execution", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "mitre_techniques", "tag": "T1059 - 命令和脚本解释器", "tag_en": "T1059 - Command and Scripting Interpreter", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "188e097a-62ed-4663-9bd1-65f4229f48fe", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2022", "os Windows:11", "os Windows:Server 2019", "os Windows:Server 2016", "os Windows:10", "mitre_techniques T1059 - 命令和脚本解释器", "control EDR/XDR", "control HIDS", "mitre_mitigation M1021", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "mitre_mitigation M1026", "mitre_mitigation M1045", "mitre_mitigation M1038", "mitre_tactics TA0002 - 攻击执行", "control AV", "mitre_mitigation M1042", "mitre_mitigation M1040", "mitre_mitigation M1049", "run_as SYSTEM"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "mkdir \"%APPDATA%\\..\\Local\\z_%USERNAME%\"\r\nrd /s /q \"%APPDATA%\\..\\Local\\z_%USERNAME%\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "mkdir \"%APPDATA%\\..\\Local\\z_%USERNAME%\"", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q \"%APPDATA%\\..\\Local\\z_%USERNAME%\"", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}