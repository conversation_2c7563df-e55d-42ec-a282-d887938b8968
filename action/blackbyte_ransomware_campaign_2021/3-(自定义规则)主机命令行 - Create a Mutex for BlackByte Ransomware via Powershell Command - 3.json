{"action": {"name": "(自定义规则)主机命令行 - Create a Mutex for BlackByte Ransomware via Powershell Command - 3", "desc": "In this action, malware creates a mutex named {1f07524d-fb13-4d5e-8e5c-c3373860df25}.[blackbyte_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -c \"$mtx = New-Object System.Threading.Mutex($false, '{1f07524d-fb13-4d5e-8e5c-c3373860df25} '); $mtx.Dispose();\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1543"], "mitre_mitigation": ["ATT&CK:M1018", "ATT&CK:M1033", "ATT&CK:M1040", "ATT&CK:M1047", "ATT&CK:M1022", "ATT&CK:M1045", "ATT&CK:M1028"]}}, "picus_id": 21284, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Mutex Creation with PowerShell", "ruleId": "5129", "description": "Detects the attempt of creation of mutex via PowerShell. Malwares use this method to prevent execution of malware repeatedly.", "releaseDate": 1595289600000, "updateDate": 1623715200000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Mutex Creation with PowerShell\nstatus: stable\ndescription: Detects the attempt of creation of mutex via PowerShell. Malwares use this method to prevent execution of malware repeatedly.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1543/\n    - https://attack.mitre.org/tactics/TA0003/\n    - https://attack.mitre.org/tactics/TA0004/\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: 4104\n        Message|contains|all:\n            - 'New-Object'\n            - 'Threading.Mutex'\n    condition: selection\nfalsepositives: \n    - Unknown\nlevel: high\ntags:\n    - attack.persistence\n    - attack.privilege_escalation\n    - attack.t1543\n    - attack.ta0003\n    - attack.ta0004", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Unknown"], "mitre": [{"name": "Create or Modify System Process", "mitreId": "T1543", "url": "https://attack.mitre.org/techniques/T1543/", "type": "technique"}]}]}, "uuid": "0129649d-a655-4125-ac07-a9e41047c9d3", "sigma_device_query": ""}