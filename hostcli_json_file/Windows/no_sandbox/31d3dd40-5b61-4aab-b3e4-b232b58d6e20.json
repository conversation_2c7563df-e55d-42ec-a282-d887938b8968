{"actions": {"31d3dd40-5b61-4aab-b3e4-b232b58d6e20": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "ver": 16, "vid": "370374", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过 Wevtutil 删除 Windows Powershell 事件日志", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图通过 wevtutil 删除 Windows Powershell 系统事件日志。", "created_at": "2025-07-21 21:41:31", "updated_at": "2025-07-21 21:41:31", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "mitre_mitigation", "tag": "M1022", "tag_en": "M1022", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "mitre_mitigation", "tag": "M1029", "tag_en": "M1029", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "mitre_mitigation", "tag": "M1041", "tag_en": "M1041", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "mitre_techniques", "tag": "T1070.001 - 清除Windows事件日志", "tag_en": "T1070.001 - Clear Windows Event Logs", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "31d3dd40-5b61-4aab-b3e4-b232b58d6e20", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_mitigation ATT&CK:M1022", "mitre_tactics TA0005 - 防御绕过", "control HIDS", "mitre_techniques T1070.001 - 清除Windows事件日志", "run_as User", "os Windows:Server 2022", "os Windows:11", "mitre_mitigation ATT&CK:M1029", "run_as SYSTEM", "os Windows:Server 2019", "os Windows:10", "mitre_mitigation ATT&CK:M1041", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "os Windows:Server 2016", "control EDR/XDR"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "wevtutil.exe cl \"windows powershell\"\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "wevtutil.exe cl \"windows powershell\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}