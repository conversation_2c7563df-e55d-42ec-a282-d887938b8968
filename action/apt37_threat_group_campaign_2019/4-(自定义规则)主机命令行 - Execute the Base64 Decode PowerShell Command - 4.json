{"action": {"name": "(自定义规则)主机命令行 - Execute the Base64 Decode PowerShell Command - 4", "desc": "In this action, PowerShell command Base64 decodes a string.[apt37_threat_group_campaign_2019]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -c \"[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String(\\\"U2VjcmV0TWVzc2FnZQ==\\\"))\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Execution", "ATT&CK:T1059"], "mitre_mitigation": ["ATT&CK:M1049", "ATT&CK:M1021", "ATT&CK:M1038", "ATT&CK:M1040", "ATT&CK:M1042", "ATT&CK:M1045", "ATT&CK:M1026"]}}, "picus_id": 14, "uuid": "d1c4eb2b-9fae-42d8-91ff-68ef3616a977", "sigma_device_query": "title:  Usage of PowerShell Base64String Methods\nstatus: stable\ndescription: Detects the attempt to encode or decode data with ToBase64String or FromBase64String methods that PowerShell utilizes. Adversaries use this method to evade defensive mechanism and to make difficult to analyze data.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1027/\n    - https://attack.mitre.org/tactics/TA0005/\n    - https://cyberfibers.com/2017/07/base64-with-powershell/\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: \n            - 4103\n            - 4104\n        Message|contains:\n            - 'ToBase64String'\n            - 'FromBase64String'\n    condition: selection\nfalsepositives:\n    - Penetration testing\n    - Administrative tools/scripts\nlevel: medium\ntags:\n    - attack.defense_evasion\n    - attack.t1027\n    - attack.ta0005"}