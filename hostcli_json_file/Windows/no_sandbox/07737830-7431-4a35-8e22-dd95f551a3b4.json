{"actions": {"07737830-7431-4a35-8e22-dd95f551a3b4": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 2, "uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "ver": 16, "vid": "382958", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 Net Utility 列出域管理员", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略；监控可疑命令执行。中长期建议：增加AD安全产品、特权帐号管理产品以加强对身份信息系统的监测与保护；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试使用 net 实用程序列出目标环境中的域管理员。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:42:07", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "mitre_techniques", "tag": "T1069 - 权限组发现", "tag_en": "T1069 - Permission Groups Discovery", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "07737830-7431-4a35-8e22-dd95f551a3b4", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": [], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1069"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "net.exe group \"Domain admins\" /DOMAIN\r\ndir\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "net.exe group \"Domain admins\" /DOMAIN", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "The\\ command\\ completed\\ successfully", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}