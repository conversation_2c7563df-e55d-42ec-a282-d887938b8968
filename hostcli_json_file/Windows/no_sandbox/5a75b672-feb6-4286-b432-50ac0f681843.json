{"actions": {"5a75b672-feb6-4286-b432-50ac0f681843": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "ver": 16, "vid": "382758", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 PsTools键盘记录，变种 #1", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：监测可疑命令执行；加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制以及执行PowerShell等权限控制；针对使用到的文件在终端/主机安全产品中基于文件Hash添加本地黑名单策略。中长期建议：如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试使用 PsTools 键盘记录", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:27", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [{"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "source": "Huorong Security", "message": "Trojan/PS.PowerSploit.a", "source_identifier": "", "cn_show": true, "product_version": "", "signature_version": ""}], "tags": [{"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "ics_mitre_tactics", "tag": "TA0111 - 权限提升", "tag_en": "TA0111 - Privilege Escalation", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "ics_mitre_techniques", "tag": "T0874 - 挂钩", "tag_en": "T0874 - Hook<PERSON>", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "5a75b672-feb6-4286-b432-50ac0f681843", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": [], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:Collection", "ATT&CK:T1056"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "psversion.ps1", "file_name": "psversion.ps1", "file_owner": "system", "file_transfer_library": 999050882, "file_type": "ps1", "filesize": 15764, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "57910215db5f6f23457d689b6a815234", "sha1sum": "39a28f19ca243218d81a430180220e00ddd26253", "sha256sum": "8792d6dfcea59dd5d1be8f71d738526762a27cd4fed69ebb57d6f2260d9e6320"}], "monitor_connections": "off", "raw_text": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Keystroke-Check; Get-Keystrokes; Start-Sleep -Seconds 15; View-Job -JobName 'Keystrokes'\r\ndir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Keystroke-Check; Get-Keystrokes; Start-Sleep -Seconds 15; View-Job -JobName 'Keystrokes'", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}