{"action": {"name": "(自定义规则)主机命令行 - Get System Default CodePage Identifier - 3", "desc": "In this action, an attacker is trying to get system default CodePage identifier[bisonal_backdoor_campaign_2020]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -c \"Get-WinSystemLocale | Select-Object Name, DisplayName, @{ n='OEMCP'; e={ $_.TextInfo.OemCodePage } }, @{ n='ACP';   e={ $_.TextInfo.AnsiCodePage } }\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Name", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1082"], "mitre_mitigation": []}}, "picus_id": 10862, "sigma": {"name": "Sigma", "rules": [{"ruleName": "System Locale Settings Discovery via PowerShell Cmdlet", "ruleId": "3138", "description": "Detects the attempt to gather information about code pages which the computer uses by default via Get-WinSystemLocale cmdlet of PowerShell. This technique is commonly utilized for discovery.", "releaseDate": 1598918400000, "updateDate": 1663027200000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: System Locale Settings Discovery via PowerShell Cmdlet\nstatus: stable\ndescription: Detects the attempt to gather information about code pages which the computer uses by default via Get-WinSystemLocale cmdlet of PowerShell. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1082/\n    - https://attack.mitre.org/tactics/TA0007/\n    - https://docs.microsoft.com/en-us/powershell/module/international/get-winsystemlocale?view=windowsserver2022-ps&viewFallbackFrom=win10-ps\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\ndetection:\n    selection:\n        EventID: 4103\n        Message|contains:\n            - 'Get-WinSystemLocale'\n    condition: selection\nfalsepositives: \n    - Administrative tools/scripts\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1082\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging"]}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}]}]}, "uuid": "03801cec-03a9-43d3-934a-7ddb2e652666", "sigma_device_query": ""}