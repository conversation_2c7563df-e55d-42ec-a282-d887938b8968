{"actions": {"d819a675-4edb-4644-9b37-955bcfd079b3": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "ver": 16, "vid": "374665", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 创建持久化注册表", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制。中长期建议：如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；加强对终端的日志审计在SIEM系统中对此攻击时的行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图在 HKCU\\Software\\Microsoft hive 中创建名为 Netwire 的注册表。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:42:00", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "mitre_mitigation", "tag": "M1024", "tag_en": "M1024", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "mitre_techniques", "tag": "T1112 - 修改注册表", "tag_en": "T1112 - Modify Registry", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "d819a675-4edb-4644-9b37-955bcfd079b3", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:11", "os Windows:10", "control HIDS", "os Windows:Server 2019", "mitre_tactics TA0005 - 防御绕过", "control EDR/XDR", "mitre_mitigation ATT&CK:M1024", "run_as SYSTEM", "os Windows:Server 2016", "control AV", "run_as Admin", "os Windows:Server 2022", "mitre_techniques T1112 - 修改注册表", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "reg.exe add \"HKCU\\Software\\Microsoft\\Netwire\" /F\r\nreg.exe delete \"HKCU\\Software\\Microsoft\\Netwire\" /F\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "reg.exe add \"HKCU\\Software\\Microsoft\\Netwire\" /F", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "The\\ operation\\ completed\\ successfully\\.", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "reg.exe delete \"HKCU\\Software\\Microsoft\\Netwire\" /F", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}