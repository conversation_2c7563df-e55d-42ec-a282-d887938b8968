{"action": {"name": "(自定义规则)主机命令行 - Increase the Number of Outstanding Requests Allowed via Registry - 8", "desc": "In this action, an attacker is trying to increase the number of outstanding requests allowed (for example, SMB requests when distributing ransomware via its PsExec methodology).[blackcat_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c reg.exe add hkey_local_machine\\system\\currentcontrolset\\services\\lanmanserver\\parameters /v maxmpxct /d 65535 /t reg_dword /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1112"], "mitre_mitigation": ["ATT&CK:M1024"]}}, "picus_id": 22537, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Maximum Concurrect Request Config via MaxMpxCt Value", "ruleId": "3994", "description": "Detects to set maximum MaxMpxCt value of LanmanServer Parameters in registry. This technique is commonly utilized for Defense Evasion.", "releaseDate": 1677456000000, "updateDate": 1677456000000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Maximum Concurrect Request Config via MaxMpxCt Value\nstatus: stable\ndescription: Detects to set maximum MaxMpxCt value of LanmanServer Parameters in registry. This technique is commonly utilized for Defense Evasion.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0005/\n   - https://attack.mitre.org/techniques/T1112/\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 13 \n        TargetObject: '*\\currentcontrolset\\services\\lanmanserver\\parameters\\maxmpxct'\n        EventType: 'SetValue'\n        Details: 'DWORD (0x0000ffff)'\n    condition: selection\nfalsepositives:\n    - Unknown\nlevel: high\ntags:\n    - attack.defense_evasion\n    - attack.ta0005\n    - attack.t1112", "logSource": {"productName": "windows", "service": "sysmon", "policies": null}, "falsePositives": ["Unknown"], "mitre": [{"name": "Modify Registry", "mitreId": "T1112", "url": "https://attack.mitre.org/techniques/T1112/", "type": "technique"}]}]}, "sigma_device_query": ""}