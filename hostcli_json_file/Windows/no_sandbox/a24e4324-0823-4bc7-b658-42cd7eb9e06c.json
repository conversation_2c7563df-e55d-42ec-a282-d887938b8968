{"actions": {"a24e4324-0823-4bc7-b658-42cd7eb9e06c": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "ver": 16, "vid": "382318", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 Excel 宏执行命令", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制；提高终端/主机安全产品的签名库的更新频率；针对使用到的恶意文件在终端安全产品中基于文件Hash添加本地黑名单策略。中长期建议：加强互联网访问的管控与下载文件检测，并加强内网网络流量的威胁文件传输检测；在内部各个安全区域之间部署NIPS加强检测与防御；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；加强对终端的日志审计在SIEM系统中对此攻击时的行为进行监测与告警；加强员工安全意识培训。", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试使用 Excel 宏工作簿在目标系统上执行命令。该宏通过 regsvr32 执行 dll。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:47", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [{"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "source": "Huorong Security", "message": "Trojan/W64.Injector.d", "source_identifier": "", "cn_show": true, "product_version": "", "signature_version": ""}], "tags": [{"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "ics_mitre_tactics", "tag": "TA0108 - 初始访问", "tag_en": "TA0108 - Initial Access", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "ics_mitre_techniques", "tag": "T0865 - 鱼叉式钓鱼附件", "tag_en": "T0865 - <PERSON><PERSON><PERSON><PERSON> Attachment", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_mitigation", "tag": "M1017", "tag_en": "M1017", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_mitigation", "tag": "M1021", "tag_en": "M1021", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_mitigation", "tag": "M1049", "tag_en": "M1049", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_mitigation", "tag": "M1054", "tag_en": "M1054", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_tactics", "tag": "TA0001 - 初始访问", "tag_en": "TA0001 - Initial Access", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "mitre_techniques", "tag": "T1566.001 - 网络钓鱼附件", "tag_en": "T1566.001 - <PERSON><PERSON><PERSON><PERSON> Attachment", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "a24e4324-0823-4bc7-b658-42cd7eb9e06c", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1031", "ATT&CK:M1021", "ATT&CK:M1049", "ATT&CK:M1054", "ATT&CK:M1017"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1566.001", "ATT&CK:Initial Access"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "microsoft.security", "file_name": "microsoft.security", "file_owner": "system", "file_transfer_library": 999051091, "file_type": "bin", "filesize": 5120, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "99531f2eb4668e200774a64bb9f0067b", "sha1sum": "fc260fee3ff459823b9f3461cdeab65d5693302b", "sha256sum": "107d128ea663a1855b235df986293e035c8846f4f338bc4a532c3a19a4edf436"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "sodinokibi.xlsm", "file_name": "sodinokibi.xlsm", "file_owner": "system", "file_transfer_library": 999051092, "file_type": "bin", "filesize": 12659, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "de24b59357e5cc050fb45804f3d67942", "sha1sum": "3eb5b5a6c6cb8dc330234d79ed50e0d2e924758b", "sha256sum": "a82fcca1cfbf0834e0b6ded7a2844aff10d0bdd7a0d966362bf4a06d5c52d882"}], "monitor_connections": "off", "raw_text": "cmd.exe /c dir \"%APPDATA%\\Microsoft\\Templates\\\" || mkdir \"%APPDATA%\\Microsoft\\Templates\\\" & copy \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\microsoft.security\" \"%APPDATA%\\Microsoft\\Templates\" & copy \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\sodinokibi.xlsm\" \"%APPDATA%\\Microsoft\\Templates\"\r\ncmd.exe /c \"%APPDATA%\\Microsoft\\Templates\\sodinokibi.xlsm\"\r\ntasklist /svc | findstr /i calc\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}} & del \"%APPDATA%\\Microsoft\\Templates\\sodinokibi.xlsm\" & del \"%APPDATA%\\Microsoft\\Templates\\microsoft.security\"\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"((Test-Path 'HKLM:\\SOFTWARE\\Microsoft\\Office\\Word\\Addins') -or (Test-Path 'HKCU:\\SOFTWARE\\Microsoft\\Office\\Word\\Addins'))\"", "incompatible_check": "match", "incompatible_match": "False", "incompatible_order": 1, "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "match", "success_match": "True", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "dir \"%APPDATA%\\Microsoft\\Templates\\\" || mkdir \"%APPDATA%\\Microsoft\\Templates\"", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "copy /y \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\microsoft.security\" \"%TMP%\"", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "copy /y \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\sodinokibi.xlsm\" \"%APPDATA%\\Microsoft\\Templates\"", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "start excel.exe \"%APPDATA%\\Microsoft\\Templates\\sodinokibi.xlsm\"", "prompt": "auto", "sleep": 20, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i calc", "prompt": "auto", "sleep": 0, "step_order": 7, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im calculator.exe || taskkill.exe /f /im win32calc.exe || taskkill.exe /f /im calc.exe || taskkill.exe /f /im calculatorapp.exe", "prompt": "auto", "sleep": 0, "step_order": 8, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im excel.exe", "prompt": "auto", "sleep": 0, "step_order": 9, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%APPDATA%\\Microsoft\\Templates\\sodinokibi.xlsm\"", "prompt": "auto", "sleep": 0, "step_order": 10, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%TMP%\\microsoft.security\"", "prompt": "auto", "sleep": 0, "step_order": 11, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rmdir /s /q \"%TMP%\\VBE\" & del \"%TMP%\\*OProcSessId.dat\"", "prompt": "auto", "sleep": 0, "step_order": 12, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 13, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}