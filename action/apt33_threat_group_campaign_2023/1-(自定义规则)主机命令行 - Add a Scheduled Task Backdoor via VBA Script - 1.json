{"action": {"name": "(自定义规则)主机命令行 - Add a Scheduled Task Backdoor via VBA Script - 1", "desc": "In this action, an attacker leaves the PowerShell backdoor through scheduled tasks on the system with a word file containing a macro.[apt33_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "temp_script.ps1", "file_owner": "system", "file_transfer_library": 998002044, "randomize_file": null, "file_name": "temp_script.ps1"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Apply_Form.docm", "file_owner": "system", "file_transfer_library": 998002045, "randomize_file": null, "file_name": "Apply_Form.docm"}], "steps": [{"step_order": 1, "command": "cmd.exe /c dir \"%APPDATA%\\Microsoft\\Templates\\\" || mkdir \"%APPDATA%\\Microsoft\\Templates\\\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c \"%APPDATA%\\Microsoft\\Templates\\Apply_Form.docm\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "cmd.exe /c timeout 10 && tasklist /svc | findstr /i notepad.exe", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "otepad\\.exe", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Execution", "ATT&CK:T1059"], "mitre_mitigation": ["ATT&CK:M1042", "ATT&CK:M1040", "ATT&CK:M1026", "ATT&CK:M1038", "ATT&CK:M1021", "ATT&CK:M1049", "ATT&CK:M1045"]}}, "picus_id": 23220, "uuid": "31e28f00-322b-4c6e-a22d-5186a04ffb1d", "sigma_device_query": "title: Suspicious Visual Basic Libraries Loaded via Microsoft Office Application\nstatus: experimental\ndescription: Detects DLL's loaded via Microsoft Office Application Containing VBA Macros. Adversaries may use VB payloads to execute malicious commands.\nauthor: Picus Security\nreferences: \n    -   https://attack.mitre.org/tactics/TA0002/\n    -   https://attack.mitre.org/techniques/T1059/005/\n    -   https://github.com/Inf0secRabbit/BadAssMacros\nlogsource:\n     product: windows\n     service: sysmon\ndetection:\n    selection:\n        EventID: 7\n        Image|endswith:\n            - 'winword.exe'\n            - 'powerpnt.exe'\n            - 'excel.exe'\n        ImageLoaded|contains:\n            - 'vbe*.dll'\n    condition: selection\nfalsepositives:\n    - Usual user activity\nlevel: medium\ntags:\n    - attack.ta0002\n    - attack.execution\n    - attack.t1059.005"}