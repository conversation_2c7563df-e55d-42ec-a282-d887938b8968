{"id": 5255, "display_id": 99657, "name": "Boot or Logon Autostart Execution Micro Emulation Plan - 1", "action_count": 25, "objective_count": 25, "release_date": "2025-06-27T03:03:29Z", "update_date": "2025-06-27T03:03:30Z", "is_copyable": true, "is_predefined": true, "is_licensed": true, "severity": "High", "tags": ["MicroEmulationPlan", "Red Report 2024"], "kill_chain_phases": ["Persistence", "Impact"], "affected_operating_systems": ["Windows"], "applicable_agent_type_ids": [2, 3], "lateral_action_count": 0, "cred_needed_action_count": 0, "cti_map_count": 0, "is_wats_lite": false, "module_ids": [2], "unlicensed_module_ids": null, "categories": [{"id": 2, "name": "<PERSON> <PERSON><PERSON>rio"}], "policies": null, "assessments": null, "is_busy": false, "vulnerability_types": null, "campaign": {"meta_data": {"id": 5255, "display_id": 99657, "name": "Boot or Logon Autostart Execution Micro Emulation Plan - 1", "action_count": 25, "description": "This threat involves a micro emulation plan for Mitre Technique T1547 \"Boot or Logon Autostart Execution\". In this technique, adversaries aim to achieve persistence and execution at system startup or user logon by leveraging mechanisms that automatically execute specific code or scripts. Boot or Logon Autostart Execution consists of techniques that manipulate the ways in which systems initiate processes during bootup or user login to run adversary-controlled code automatically. These techniques are critical for adversaries to maintain a foothold within a network because they ensure the malicious code executes regularly without further intervention. An adversary might configure a malicious script to run at system startup by adding it to the Windows Registry's Run keys or scheduling a task. Similarly, placing scripts or executables in specific directories that are automatically executed upon logon can achieve persistent access. The emulation of this technique helps in comprehensively understanding its mechanisms, identifying its indicators, and developing effective countermeasures to prevent unauthorized persistence and automatic execution attempts on systems. ", "release_date": "2025-06-27T03:03:29Z", "update_date": "2025-06-27T03:03:30Z", "affected_os": [{"is_selected": false, "name": "Windows"}], "affected_platform": [{"is_selected": false, "name": "Windows Server 2022 64-bit"}, {"is_selected": false, "name": "Windows 11 64-bit"}, {"is_selected": false, "name": "Windows Server 2019 64-bit"}, {"is_selected": false, "name": "Windows Server 2016 64-bit"}, {"is_selected": false, "name": "Windows 10 64-bit"}], "threat_actor": {"id": 0, "name": "", "description": "", "origin": "", "aka": "", "sectors": null, "regions": null}, "severity": "High", "modules": ["Endpoint Scenario"], "is_custom": false, "tags": ["MicroEmulationPlan", "Red Report 2024"], "categories": ["<PERSON> <PERSON><PERSON>rio"], "kill_chain_phases": ["Persistence", "Impact"], "mitre": {"tactic": {"id": "TA0003", "name": "Persistence"}, "technique": {"id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": "T1491.001", "name": "Internal Defacement"}}, "cti_actions_count": 0, "vulnerability_types": null}, "result": "", "run_date": "", "flow": [{"detail": {"id": 31654, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a Shortcut named TA866.lnk to Startup Folder using Powershell Script", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 151, "mitre_id": "T1547.009", "mitre_name": "Shortcut Modification"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 30255, "node_id": 2, "action": {"id": 30255, "display_id": 13223, "owasp_rank": "", "name": "Create a Shortcut named TA866.lnk to Startup Folder using Powershell Script", "release_date": "2024-02-08T08:03:53.058285Z", "description": "In this action, an attacker is creating a shortcut named TA866.lnk in the logged-on user’s Startup folder.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 151, "mitre_id": "T1547.009", "name": "Shortcut Modification"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 11263, "name": "Process 1", "content": "powershell.exe New-Item -ItemType SymbolicLink -Path '%USERPROFILE%\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup' -Name \"TA866.lnk\" -Value \"%TMP%\\TA866.js\";", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 15130, "name": "TA866.js", "file_type": ".js", "file_size": 0, "path": "%TMP%\\TA866.js", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 11264, "name": "Process 1", "content": "{predefined-file-delete} %USERPROFILE%\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\TA866.lnk", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12902, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\TA866.js", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"TA866.js\") OR (\"13ee0bf920e3425f60efd5964f0070b7391370ceade9a33c5daf8c9c80a01ea9\" OR \"a5500856dfccc261386c0eabb1d68dc9715f6050\" OR \"6e441a242cc93b751deb8ae1850e9525\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31654, "node_id": 1}, {"detail": {"id": 31653, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Copy a File \"Winvoke.exe\" in Startup Folder for Persistence", "tag": "copy", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 24178, "node_id": 4, "action": {"id": 24178, "display_id": 13121, "owasp_rank": "", "name": "Copy a File \"Winvoke.exe\" in Startup Folder for Persistence", "release_date": "2023-03-22T20:20:41.684793Z", "description": "In this action, an attacker tries to copy a file from the Public Downloads directory to the current user's Startup folder.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 8547, "name": "Process 1", "content": "cmd.exe /c copy \"C:\\Users\\<USER>\\Downloads\\Winvoke.exe\" \"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\StartUp\\\"\n", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11983, "name": "dummy.exe", "file_type": ".exe", "file_size": 0, "path": "C:\\Users\\<USER>\\Downloads\\Winvoke.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 8548, "name": "Process 2", "content": "{predefined-file-search} \"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\StartUp\\Winvoke.exe\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 8549, "name": "Process 1", "content": "{predefined-file-delete} \"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\StartUp\\Winvoke.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 8550, "name": "Process 2", "content": "{predefined-file-delete} \"C:\\Users\\<USER>\\Downloads\\Winvoke.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"Winvoke.exe\") OR (\"f1f55e91f0d005cd43b75229dfff68aa88cbc0efbb570ee1737bfccbf5afad1d\" OR \"b0b48c8f63a95725b1569a8488632f8703a8eb68\" OR \"cb07359946d58ff9c1469865b1624bf6\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "Microsoft Sentinel", "Palo Alto Cortex XDR", "Microsoft Defender", "SentinelOne", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "File Creation in Startup Folder", "ruleId": "2119", "description": "Detects the attempt to create file in startup folder. This technique is utilized for persistence.", "releaseDate": 1659484800000, "updateDate": 1663113600000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='11' and (\"File Path\" ilike '%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\%'))", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["Software installations/updates"], "mitre": [{"name": "Boot or Logon Autostart Execution", "mitreId": "T1547", "url": "https://attack.mitre.org/techniques/T1547/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Persistence via File Transport to Startup Folder", "ruleId": "4429", "description": "Detects the attempt to copy or move a file into startup folder. This technique is commonly utilized for persistence.", "releaseDate": 1553904000000, "updateDate": 1663027200000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (\"Process CommandLine\" ilike '%copy% %Microsoft\\Windows\\Start Menu\\Programs\\StartUp%' or \"Process CommandLine\" ilike '%move% %Microsoft\\Windows\\Start Menu\\Programs\\StartUp%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Usual user activity"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31653, "node_id": 3}, {"detail": {"id": 31652, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a New Persistence Registry Key for FileSyncShell64.dll in HKCU Hive", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 23109, "node_id": 6, "action": {"id": 23109, "display_id": 13050, "owasp_rank": "", "name": "Create a New Persistence Registry Key for FileSyncShell64.dll in HKCU Hive", "release_date": "2022-10-03T10:45:14.174046Z", "description": "In this action, an attacker is trying to create a new registry key \"{01575CFE-9A55-4003-A5E1-F38D1EBDCBE1}\" in HKCU to persist on the target system.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7928, "name": "Process 1", "content": "reg.exe add \"HKCU\\SOFTWARE\\Classes\\CLSID\\{01575CFE-9A55-4003-A5E1-F38D1EBDCBE1}\\InprocServer32\" /f", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}, {"id": 7929, "name": "Process 2", "content": "reg.exe add HKCU\\SOFTWARE\\Classes\\CLSID\\{01575CFE-9A55-4003-A5E1-F38D1EBDCBE1}\\InprocServer32 /v \"\" /t REG_SZ /d \"%TMP%\\Microsoft\\EdgeFss\\FileSyncShell64.dll\" /f", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}, {"id": 7930, "name": "Process 3", "content": "reg.exe add HKCU\\SOFTWARE\\Classes\\CLSID\\{01575CFE-9A55-4003-A5E1-F38D1EBDCBE1}\\InprocServer32 /v ThreadingModel /t REG_SZ /d \"Apartment\" /f", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 7931, "name": "Process 1", "content": "reg.exe delete HKCU\\SOFTWARE\\Classes\\CLSID\\{01575CFE-9A55-4003-A5E1-F38D1EBDCBE1} /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"InprocServer32\" AND (\"FileSyncShell64.dll\" OR \"Apartment\"))"}, "detection": {"availableSources": ["Microfocus ArcSight ESM", "Microsoft Sentinel", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "IBM QRadar", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "Microsoft Defender", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Suspicious InprocServer32 Registry Key Modification", "ruleId": "8811", "description": "Detects the suspicious InprocServer32 registry key modification. The InprocServer32 key is typically used to specify a DLL that is loaded when a corresponding COM object is instantiated. Adversaries can abuse this key to hijack the search order by creating a fake COM object with an equivalent key structure in HKCU, pointing to a user-controlled DLL. This technique is utilized for persistence.", "releaseDate": 1681344000000, "updateDate": 1681344000000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and (EventID='13' and Image ilike '%reg.exe' and (\"Target Object\" ilike 'HKU%\\CLSID\\%\\InprocServer32\\(Default)') and \"Registry Value Data\" ilike '%.dll') and not ((\"Registry Value Data\" ilike '%\\Windows\\System32\\%' or \"Registry Value Data\" ilike '%\\Windows\\SysWOW64\\%')))", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": []}, "falsePositives": ["Unknown"], "mitre": [{"name": "Component Object Model Hijacking", "mitreId": "T1546.015", "url": "https://attack.mitre.org/techniques/T1546/015/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31652, "node_id": 5}, {"detail": {"id": 31651, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a New Persistence Registry Key \"Recycle Bin\" in HKLM hive by using RecycleBinPersistence Tool", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 22828, "node_id": 8, "action": {"id": 22828, "display_id": 13020, "owasp_rank": "", "name": "Create a New Persistence Registry Key \"Recycle Bin\" in HKLM hive by using RecycleBinPersistence Tool", "release_date": "2022-08-22T07:49:29.720863Z", "description": "In this action, an attacker is trying to create a new registry key \"Recycle Bin\" to persist on the target system.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7819, "name": "Process 1", "content": "%TMP%\\RecycleBinPersistence.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11283, "name": "RecycleBinPersistence.exe", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\RecycleBinPersistence.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7820, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\RecycleBinPersistence.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7821, "name": "Process 2", "content": "reg.exe DELETE  \"HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\CLSID\\{645FF040-5081-101B-9F08-00AA002F954E}\\shell\\open\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"RecycleBinPersistence\") OR (\"2be42d2bfeefac8abfafdd50d998d2e2b4a66423bdc6ea4079ce0c6e7b49c371\" OR \"15612ebfc77ccfaff0cbde1003c99eb19d78a0a6\" OR \"679cf5393cd7ccafb2f7a4889d1e5215\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["<PERSON><PERSON><PERSON><PERSON>", "Crowdstrike", "Microsoft Sentinel", "Microsoft Defender", "SentinelOne", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "IBM QRadar", "Sigma"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via \"Recycle Bin\" Registry Key", "ruleId": "7157", "description": "Detects the modification of \"Recycle Bin\" registry key. Adversaries may modificate \"Shell\" subkey in regard to CLSID associated with Recycle Bin. This technique is utilized for persistence.", "releaseDate": 1667433600000, "updateDate": 1667433600000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='13' and EventType='SetValue' and \"Target Object\" ilike 'HKCR\\CLSID\\{645FF040-5081-101B-9F08-00AA002F954E}\\shell\\open\\command\\%')", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["Unknown"], "mitre": [{"name": "Boot or Logon Autostart Execution", "mitreId": "T1547", "url": "https://attack.mitre.org/techniques/T1547/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31651, "node_id": 7}, {"detail": {"id": 31650, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a New Persistence Registry Key \"EverNoteTrayUService\" in HKCU hive", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 22624, "node_id": 10, "action": {"id": 22624, "display_id": 13006, "owasp_rank": "", "name": "Create a New Persistence Registry Key \"EverNoteTrayUService\" in HKCU hive", "release_date": "2022-07-19T15:57:19.910767Z", "description": "In this action, an attacker is trying create a new registry key \"EverNoteTrayUService\" to persist on the target system.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7773, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"EverNoteTrayUService\" /t REG_SZ /d \"%USERPROFILE%\\AppData\\Roaming\\EverNoteService\\RemovableDisc\" /f", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}, {"id": 7774, "name": "Process 2", "content": "reg.exe query \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /s /f \"EverNoteTrayUService\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7775, "name": "Process 1", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"EverNoteTrayUService\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"reg\" AND \"add\" AND \"EverNoteTrayUService\") OR (\"reg\" AND \"query\" AND \"EverNoteTrayUService\"))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31650, "node_id": 9}, {"detail": {"id": 31649, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Copy an Executable to Startup Folder for Persistence via Grpconv.exe", "tag": "copy", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 22425, "node_id": 12, "action": {"id": 22425, "display_id": 12989, "owasp_rank": "", "name": "Copy an Executable to Startup Folder for Persistence via Grpconv.exe", "release_date": "2022-06-18T12:19:12.469876Z", "description": "In this action, an attacker tries to copy an executable to the startup folder for the persistence via Grpconv.exe binary.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7722, "name": "Process 1", "content": "grpconv.exe -o", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11086, "name": "grpconvPersistence.txt", "file_type": ".txt", "file_size": 0, "path": "%USERPROFILE%\\setup.ini", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 1, "result_output": ""}]}, {"id": 7723, "name": "Process 2", "content": "{predefined-file-search} \"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\GrpconvCalc.lnk\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7724, "name": "Process 1", "content": "{predefined-file-delete} \"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\GrpconvCalc.lnk\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7725, "name": "Process 2", "content": "{predefined-file-delete} %USERPROFILE%\\setup.ini", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"GrpconvCalc.lnk\") OR (\"grpconv -o\")) OR (\"setup.ini\" OR \"a3b66b814d891c71b230af0ba807e877fc6abb1e0b660116a29e86ecf607b725\" OR \"16af1ca005742aa9501f7507fb74eb1d1944d03f\" OR \"45cec29356b652c044fc1d3b67834496\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel", "Microsoft Defender", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "Crowdstrike", "SentinelOne", "Sigma"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "File Creation in Startup Folder", "ruleId": "2119", "description": "Detects the attempt to create file in startup folder. This technique is utilized for persistence.", "releaseDate": 1659484800000, "updateDate": 1663113600000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='11' and (\"File Path\" ilike '%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\%'))", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["Software installations/updates"], "mitre": [{"name": "Boot or Logon Autostart Execution", "mitreId": "T1547", "url": "https://attack.mitre.org/techniques/T1547/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31649, "node_id": 11}, {"detail": {"id": 31648, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a Registry Run Key via VBA Macro", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 13812, "node_id": 14, "action": {"id": 13812, "display_id": 12834, "owasp_rank": "", "name": "Create a Registry Run Key via VBA Macro", "release_date": "2022-06-18T12:21:23.344914Z", "description": "In this action, an attacker is trying create a registry key (Run) via a VBA Macro.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5305, "name": "Process 1", "content": "cmd.exe /c dir \"%APPDATA%\\Microsoft\\Templates\\\" || mkdir \"%APPDATA%\\Microsoft\\Templates\\\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 7270, "name": "Process 2", "content": "cmd.exe /c \"%APPDATA%\\Microsoft\\Templates\\VBARegRunWMI.docm\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 10263, "name": "VBARegRunWMI.docm", "file_type": ".docm", "file_size": 0, "path": "%APPDATA%\\Microsoft\\Templates\\VBARegRunWMI.docm", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 7271, "name": "Process 3", "content": "cmd.exe /c timeout 5 && reg query \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /s /f \"PersistenceFromVBA\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7273, "name": "Process 1", "content": "cmd.exe /c reg delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"PersistenceFromVBA\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7274, "name": "Process 2", "content": "{predefined-file-delete} %APPDATA%\\Microsoft\\Templates\\VBARegRunWMI.docm", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 7994, "name": "Process 3", "content": "{predefined-process-kill} winword.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 10476, "name": "Process 4", "content": "{predefined-directory-delete} %TMP%\\VBE", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 10477, "name": "Process 5", "content": "{predefined-file-delete} %TMP%\\*OProcSessId.dat", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"WINWORD.EXE\" AND (\"VBE7.DLL\" OR \"wmiutils.dll\")) OR (\"reg\" AND \"query\" AND \"PersistenceFromVBA\") OR (\"VBARegRunWMI\") OR (\"bc5ab6d0dd47b6cbdc5ca7fd10c74ae68c1d4ea62b8a0b5f116450840491d57d\" OR \"08b9de01ff26c22956b5add4014a97cb8d937934\" OR \"633d282005583fc32e8d1fe1ad12e8ec\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Palo Alto Cortex XDR", "IBM QRadar", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Crowdstrike", "Microsoft Sentinel", "Microsoft Defender", "VMware Carbon Black EDR", "Sigma", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Suspicious Windows Management Instrumentation DLL Load via Microsoft Office Application", "ruleId": "5202", "description": "Detects DLL's loaded via Microsoft Office application containing macros that execute WMI commands. Adversaries may use WMI to perform many tactic functions, such as gathering information for Discovery.", "releaseDate": 1629936000000, "updateDate": 1662940800000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='7' and (Image ilike '%\\winword.exe' or Image ilike '%\\powerpnt.exe' or Image ilike '%\\excel.exe') and (LoadedImage ilike '%\\wmiutils.dll' or LoadedImage ilike '%\\wbemcomn.dll' or LoadedImage ilike '%\\wbemprox.dll' or LoadedImage ilike '%\\wbemdisp.dll' or LoadedImage ilike '%\\wbemsvc.dll'))", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["None"], "mitre": [{"name": "Windows Management Instrumentation", "mitreId": "T1047", "url": "https://attack.mitre.org/techniques/T1047/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}, {"ruleName": "Suspicious Visual Basic Libraries Loaded via Microsoft Office Application", "ruleId": "8297", "description": "Detects DLL's loaded via Microsoft Office Application Containing VBA Macros. Adversaries may use VB payloads to execute malicious commands.", "releaseDate": 1629676800000, "updateDate": 1663718400000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='7' and (Image ilike '%winword.exe' or Image ilike '%powerpnt.exe' or Image ilike '%excel.exe') and (LoadedImage ilike '%vbe%.dll%'))", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["Usual user activity"], "mitre": [{"name": "Visual Basic", "mitreId": "T1059.005", "url": "https://attack.mitre.org/techniques/T1059/005/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31648, "node_id": 13}, {"detail": {"id": 31647, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a new Registry Key for Autorun of dummy.exe Variant-4", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10853, "node_id": 16, "action": {"id": 10853, "display_id": 12303, "owasp_rank": "", "name": "Create a new Registry Key for Autorun of dummy.exe Variant-4", "release_date": "2022-06-18T12:21:58.844288Z", "description": "In this action, an attacker is trying to add the dummy.exe file to autorun hive as a key for persistence.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6858, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"M00nd3v\" /d \"%TMP%\\dummy.exe\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 5, "name": "dummy.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\dummy.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6268, "name": "Process 1", "content": "{predefined-file-delete} C:\\Users\\<USER>\\dummy.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6859, "name": "Process 2", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\"  /v \"M00nd3v\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"add\" AND \"M00nd3v\")"}, "detection": {"availableSources": ["<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "Microsoft Defender", "SentinelOne", "Palo Alto Cortex XDR", "Microfocus ArcSight ESM", "IBM QRadar", "Microsoft Sentinel", "VMware Carbon Black EDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "M00nd3v Autorun Key Value Creation via DeathStalker Threat Group", "ruleId": "4933", "description": "Detects the modification of Run registry value by DeathStalker Threat Group. This technique is commonly utilized for persistence.", "releaseDate": 1601856000000, "updateDate": 1647216000000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='13' and \"Target Object\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Run%\\M00nd3v\\%' and EventType='SetValue')", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["None"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31647, "node_id": 15}, {"detail": {"id": 31646, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a New Registry Key in HKCU Hive Variant-4", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10825, "node_id": 18, "action": {"id": 10825, "display_id": 12205, "owasp_rank": "", "name": "Create a New Registry Key in HKCU Hive Variant-4", "release_date": "2022-06-18T12:21:59.107032Z", "description": "In this action, an attacker is trying to create a new key \"CurlInit\" under the \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\\" hive.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6810, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"CurlInit\" /d \"C:\\windows\\System32\\calc.exe\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6811, "name": "Process 1", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\"  /v \"CurlInit\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"Run\" AND \"CurlInit\" AND (\"add\" OR \"SetValue\"))"}, "detection": {"availableSources": ["Microsoft Sentinel", "Microsoft Defender", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "IBM QRadar", "Crowdstrike", "SentinelOne", "Palo Alto Cortex XDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via Windows Registry Run Keys", "ruleId": "2606", "description": "Detects the addition of a visual basic script or an executable file to the Windows Registry Run Key. Adversaries may achieve persistence by adding a program to a Registry run key. Adding an entry to the \"run keys\" in the Registry will cause the program referenced to be executed when a user logs in.", "releaseDate": 1590537600000, "updateDate": 1638921600000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\reg.exe' and (\"Process CommandLine\" ilike '%add%') and (\"Process CommandLine\" ilike '%.vbs%' or \"Process CommandLine\" ilike '%.exe%') and (\"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Run%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnceEx%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServices%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Unknown"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31646, "node_id": 17}, {"detail": {"id": 31645, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write a File that Contains Ransom Note for Netwalker Ransomware and Open It", "tag": "write", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "mitre_name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10819, "node_id": 20, "action": {"id": 10819, "display_id": 12194, "owasp_rank": "", "name": "Write a File that Contains Ransom Note for Netwalker Ransomware and Open It", "release_date": "2022-06-18T12:21:59.197907Z", "description": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6790, "name": "Process 1", "content": "cmd.exe /c echo \"Netwalker Malware is there\" > \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6791, "name": "Process 2", "content": "cmd.exe /c echo \"Your files are encrypted.\" >> \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6792, "name": "Process 3", "content": "cmd.exe /c notepad.exe \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6083, "name": "Process 1", "content": "cmd.exe /c taskkill /f /im \"notepad.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6793, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\Netwalker_Ransomware_619815.txt\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"<PERSON>walker_Ransomware\" AND \"notepad.exe\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31645, "node_id": 19}, {"detail": {"id": 31644, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create Registry Key Value Named \"Dummy\" For Persistence", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10793, "node_id": 22, "action": {"id": 10793, "display_id": 12123, "owasp_rank": "", "name": "Create Registry Key Value Named \"Dummy\" For Persistence", "release_date": "2022-06-18T12:21:59.592821Z", "description": "In this action, an attacker is -trying to create an key under HKCU/Software/Microsoft/Windows/CurrentVersion/RunOnce/ for persistence", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6738, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce\" /v \"Dummy\" /t REG_DWORD /d 1", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6739, "name": "Process 1", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce\"  /v \"Dummy\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"CurrentVersion\\\\RunOnce\" AND \"Dummy\" AND (NOT \"delete\"))"}, "detection": {"availableSources": ["Microsoft Defender", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "Sigma", "Crowdstrike", "Microsoft Sentinel", "SentinelOne", "IBM QRadar", "<PERSON><PERSON><PERSON><PERSON>"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via Windows Registry Run Keys", "ruleId": "2606", "description": "Detects the addition of a visual basic script or an executable file to the Windows Registry Run Key. Adversaries may achieve persistence by adding a program to a Registry run key. Adding an entry to the \"run keys\" in the Registry will cause the program referenced to be executed when a user logs in.", "releaseDate": 1590537600000, "updateDate": 1638921600000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\reg.exe' and (\"Process CommandLine\" ilike '%add%') and (\"Process CommandLine\" ilike '%.vbs%' or \"Process CommandLine\" ilike '%.exe%') and (\"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Run%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnceEx%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServices%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Unknown"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31644, "node_id": 21}, {"detail": {"id": 31643, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create New Registry Key for Persistence", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10788, "node_id": 24, "action": {"id": 10788, "display_id": 12118, "owasp_rank": "", "name": "Create New Registry Key for Persistence", "release_date": "2022-06-18T12:21:59.654757Z", "description": "In this action, The Attacker is trying to secure persistence by creating a new registry key for Autorun.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6725, "name": "Process 1", "content": "reg.exe add \"HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\" /V \"loki\" /t REG_SZ /F /D \"wscript.exe %TMP%\\Lokibot.vbs\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9788, "name": "Lokibot.vbs", "file_type": ".vbs", "file_size": 0, "path": "%TMP%\\Lokibot.vbs", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6723, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\Lokibot*", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6726, "name": "Process 2", "content": "reg.exe delete \"HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\" /V \"loki\" /F", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"Lokibot.vbs\") OR (\"b9f0bd513a161c21dbdcfeeccf9631d745f7443442cf1e73f9a80a979b5e5b5d\" OR \"fbfca373cc69aea3dacfb1398160ebc69e50caa0\" OR \"ac0090e7ed8c235623630e0a338bc566\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Sigma", "Crowdstrike", "Microsoft Defender", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel", "SentinelOne", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "IBM QRadar"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via Windows Registry Run Keys", "ruleId": "2606", "description": "Detects the addition of a visual basic script or an executable file to the Windows Registry Run Key. Adversaries may achieve persistence by adding a program to a Registry run key. Adding an entry to the \"run keys\" in the Registry will cause the program referenced to be executed when a user logs in.", "releaseDate": 1590537600000, "updateDate": 1638921600000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\reg.exe' and (\"Process CommandLine\" ilike '%add%') and (\"Process CommandLine\" ilike '%.vbs%' or \"Process CommandLine\" ilike '%.exe%') and (\"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Run%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnceEx%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServices%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Unknown"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31643, "node_id": 23}, {"detail": {"id": 31642, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write .Vbs File to Startup Folder", "tag": "write", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10781, "node_id": 26, "action": {"id": 10781, "display_id": 12098, "owasp_rank": "", "name": "Write .Vbs File to Startup Folder", "release_date": "2022-06-18T12:21:59.762236Z", "description": "In this action, a malware is trying to write a .vbs file to Startup folder.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6707, "name": "Process 1", "content": "cscript.exe \"%TMP%\\Zoom.vbs\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9784, "name": "Zoom.vbs", "file_type": ".vbs", "file_size": 0, "path": "%TMP%\\Zoom.vbs", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12530, "name": "Process 2", "content": "{predefined-process-list} calc.exe || win32calc.exe || calculator.exe || calculatorapp.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 4784, "name": "Process 1", "content": "{predefined-process-kill} calc.exe || win32calc.exe || calculator.exe || calculatorapp.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6708, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\Zoom.vbs", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"Zoom.vbs\") OR (\"e6caf89b3f7c5f7104c36db4a308920f5e4c6d35b30b81a169c58658a186714c\" OR \"67d011ca869f02fe7325c5aed7224ff2454654a8\" OR \"6dc17f927b9973e17926e2a3cbcb21e3\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["Sigma", "Microsoft Sentinel", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "<PERSON><PERSON><PERSON><PERSON>", "SentinelOne", "IBM QRadar", "Crowdstrike", "Microsoft Defender"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Script File Execution via WScript or CScript Tool", "ruleId": "3124", "description": "Detects the attempt to execute scripts such as VBScript, JavaScript files via WScript or CScript tool. This method is used for bypassing process monitoring mechanisms.", "releaseDate": 1561766400000, "updateDate": 1595808000000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and (Image ilike '%\\cscript.exe' or Image ilike '%\\wscript.exe') and (\"Process CommandLine\" ilike '%.vbs%' or \"Process CommandLine\" ilike '%.js%' or \"Process CommandLine\" ilike '%//E:jscript%' or \"Process CommandLine\" ilike '%//E:vbscript%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "Visual Basic", "mitreId": "T1059.005", "url": "https://attack.mitre.org/techniques/T1059/005/", "type": "sub_technique"}, {"name": "JavaScript", "mitreId": "T1059.007", "url": "https://attack.mitre.org/techniques/T1059/007/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31642, "node_id": 25}, {"detail": {"id": 31641, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write a File that Contains Ransom Note and Add to Startup", "tag": "write", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10775, "node_id": 28, "action": {"id": 10775, "display_id": 12080, "owasp_rank": "", "name": "Write a File that Contains Ransom Note and Add to Startup", "release_date": "2022-06-18T12:21:59.860647Z", "description": "In this action, the malware is trying to write a file in TMP to inform the victim, and add to startup from registry.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6694, "name": "Process 1", "content": "cmd.exe /c echo \"CORONOVIRUS is there\" > \"%TMP%\\CoronaVirus.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6695, "name": "Process 2", "content": "reg.exe add \"HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\" /V \"shfc\" /t REG_SZ /F /D \"%TMP%\\CoronaVirus.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "success"}]}], "rewind_processes": [{"id": 6696, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\CoronaVirus.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6697, "name": "Process 2", "content": "reg.exe delete \"HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\" /V \"shfc\" /F", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"CurrentVersion\\\\Run\" AND \"CoronaVirus.txt\" AND (NOT \"delete\"))"}, "detection": {"availableSources": ["<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Crowdstrike", "Microsoft Sentinel", "Microsoft Defender", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "Microfocus ArcSight ESM", "IBM QRadar", "SentinelOne"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "CoronaVirus Ransomware Ransom Note Demonstration Setting", "ruleId": "3678", "description": "Detects the modification of Run registry value by CoronaVirus ransomware which demonstrates its ransom note when user logs on.", "releaseDate": 1589241600000, "updateDate": 1647216000000, "author": "Picus Security", "severity": "critical", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='13' and \"Target Object\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Run%' and EventType='SetValue' and \"Registry Value Data\" ilike '%CoronaVirus.txt%')", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["None"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31641, "node_id": 27}, {"detail": {"id": 31640, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create GroupPolicy Registry Keys for Persistence", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "mitre_name": "Registry Run Keys / Startup Folder"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10762, "node_id": 30, "action": {"id": 10762, "display_id": 11927, "owasp_rank": "", "name": "Create GroupPolicy Registry Keys for Persistence", "release_date": "2022-06-18T12:22:00.015563Z", "description": "In this action, an attacker is trying to create registry keys \"Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs\" for persistence in the target environment. An INF file, referenced in the registry, can be used to execute custom commands after the system reboor.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 143, "mitre_id": "T1547.001", "name": "Registry Run Keys / Startup Folder"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6667, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs\" /v \"Path1\" /t REG_SZ /d \"%APPDATA%\\mounper.inf\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9783, "name": "Gootkit.inf", "file_type": ".inf", "file_size": 0, "path": "%APPDATA%\\mounper.inf", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}, {"id": 6668, "name": "Process 2", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs\" /v \"Count\" /t REG_DWORD /d 4", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}, {"id": 6669, "name": "Process 3", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs\" /v \"Section1\" /t REG_SZ /d \"DefaultInstall\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6670, "name": "Process 1", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6671, "name": "Process 2", "content": "{predefined-file-delete} %APPDATA%\\mounper.inf", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"\\\\GroupPolicy\\\\PendingGPOs\" AND (NOT \"delete\"))"}, "detection": {"availableSources": ["Microfocus ArcSight ESM", "Crowdstrike", "SentinelOne", "Palo Alto Cortex XDR", "VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Microsoft Sentinel", "Microsoft Defender", "IBM QRadar"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via IEAK Registry Key Modification", "ruleId": "6478", "description": "Detects the modification on Internet Explorer Administrative Kit registry key. This value is responsible for hosting settings in IEAK which initialized every user logon process.", "releaseDate": 1590019200000, "updateDate": 1623715200000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='13' and \"Target Object\" ilike '%\\Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs%' and EventType='SetValue')", "logSource": {"productName": ["windows"], "service": "sysmon", "policies": null}, "falsePositives": ["Software installations/updates"], "mitre": [{"name": "Boot or Logon Autostart Execution", "mitreId": "T1547", "url": "https://attack.mitre.org/techniques/T1547/", "type": "technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31640, "node_id": 29}, {"detail": {"id": 31638, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write a File that Contains Ransom Note and Open It Variant-1", "tag": "write", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "mitre_name": "Defacement"}, "sub_technique": {"id": 12, "mitre_id": "T1491.001", "mitre_name": "Internal Defacement"}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10686, "node_id": 34, "action": {"id": 10686, "display_id": 12681, "owasp_rank": "", "name": "Write a File that Contains Ransom Note and Open It Variant-1", "release_date": "2022-06-18T12:22:00.924572Z", "description": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "name": "Defacement"}, "sub_technique": {"id": 12, "mitre_id": "T1491.001", "name": "Internal Defacement"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6519, "name": "Process 1", "content": "cmd.exe /c echo \"Conti Ransomware is there\" > \"%TMP%\\readme.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12201, "name": "Process 2", "content": "notepad.exe \"%TMP%\\readme.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16546, "name": "DragonForce.txt", "file_type": ".txt", "file_size": 0, "path": "%TMP%\\readme.txt", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 7204, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\readme.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12520, "name": "Process 2", "content": "{predefined-process-kill} notepad.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"Conti Ransomware\" AND \"readme.txt\")"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31638, "node_id": 33}, {"detail": {"id": 31637, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write a File that Contains Ransom Note and Open It Variant-2", "tag": "write", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "mitre_name": "Defacement"}, "sub_technique": {"id": 12, "mitre_id": "T1491.001", "mitre_name": "Internal Defacement"}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10584, "node_id": 36, "action": {"id": 10584, "display_id": 12628, "owasp_rank": "", "name": "Write a File that Contains Ransom Note and Open It Variant-2", "release_date": "2022-06-18T12:22:01.985838Z", "description": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "name": "Defacement"}, "sub_technique": {"id": 12, "mitre_id": "T1491.001", "name": "Internal Defacement"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6366, "name": "Process 1", "content": "cmd.exe /c echo \"MountLocker Ransomware is there\" > \"%TMP%\\MountLocker.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6367, "name": "Process 2", "content": "cmd.exe /c echo \"Your files are encrypted.\" >> \"%TMP%\\MountLocker.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6368, "name": "Process 3", "content": "notepad.exe \"%TMP%\\MountLocker.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6369, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\MountLocker.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"MountLocker.txt\" AND NOT ((\"PICUS_REWIND\") OR (\"Scenarios\" OR \"Simulation\")))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31637, "node_id": 35}, {"detail": {"id": 31636, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write a File that Contains Ransom Note for Darkside Ransomware and Open It", "tag": "write", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "mitre_name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10546, "node_id": 38, "action": {"id": 10546, "display_id": 12590, "owasp_rank": "", "name": "Write a File that Contains Ransom Note for Darkside Ransomware and Open It", "release_date": "2022-06-18T12:22:02.455997Z", "description": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6275, "name": "Process 1", "content": "cmd.exe /c echo \"Darkside Ransomware is there\" > \"%TMP%\\Darkside.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6276, "name": "Process 2", "content": "cmd.exe /c echo \"Your files are encrypted.\" >> \"%TMP%\\Darkside.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6277, "name": "Process 3", "content": "cmd.exe /c notepad.exe \"%TMP%\\Darkside.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6083, "name": "Process 1", "content": "cmd.exe /c taskkill /f /im \"notepad.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6278, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\Darkside.txt\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"Darkside\") AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31636, "node_id": 37}, {"detail": {"id": 31635, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a New Registry Key in HKCU Hive Variant-3", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10507, "node_id": 40, "action": {"id": 10507, "display_id": 12548, "owasp_rank": "", "name": "Create a New Registry Key in HKCU Hive Variant-3", "release_date": "2022-06-18T12:22:02.973016Z", "description": "In this action, an attacker is trying to create a new key \"Saintbot\" under the \"\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" hive.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6171, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"SaintBot\" /t REG_SZ /d \"%TMP%\\dummy.exe\" /f", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 5, "name": "dummy.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\dummy.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6172, "name": "Process 1", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"SaintBot\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6268, "name": "Process 2", "content": "{predefined-file-delete} C:\\Users\\<USER>\\dummy.txt", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"SaintBot\" AND \"add\")"}, "detection": {"availableSources": ["VMware Carbon Black EDR", "<PERSON><PERSON><PERSON><PERSON>", "Microsoft Sentinel", "Microsoft Defender", "SentinelOne", "Palo Alto Cortex XDR", "IBM QRadar", "Microfocus ArcSight ESM", "Sigma", "Crowdstrike"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via Windows Registry Run Keys", "ruleId": "2606", "description": "Detects the addition of a visual basic script or an executable file to the Windows Registry Run Key. Adversaries may achieve persistence by adding a program to a Registry run key. Adding an entry to the \"run keys\" in the Registry will cause the program referenced to be executed when a user logs in.", "releaseDate": 1590537600000, "updateDate": 1638921600000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\reg.exe' and (\"Process CommandLine\" ilike '%add%') and (\"Process CommandLine\" ilike '%.vbs%' or \"Process CommandLine\" ilike '%.exe%') and (\"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Run%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnceEx%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServices%' or \"Process CommandLine\" ilike '%\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run%'))", "logSource": {"productName": ["windows"], "service": "security", "policies": []}, "falsePositives": ["Unknown"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31635, "node_id": 39}, {"detail": {"id": 31634, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Write a File that Contains Ransom Note for Sodinokibi Ransomware and Open It", "tag": "write", "result": "", "tactic": {"id": 12, "mitre_id": "TA0040", "mitre_name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "mitre_name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Action on Objectives", "phase_id": 18, "phase_name": "Impact", "phase_description": "Techniques aimed at manipulating, interrupting or destroying the target system or data."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10503, "node_id": 42, "action": {"id": 10503, "display_id": 12542, "owasp_rank": "", "name": "Write a File that Contains Ransom Note for Sodinokibi Ransomware and Open It", "release_date": "2022-06-18T12:22:03.026829Z", "description": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "mitre": {"tactic": {"id": 12, "mitre_id": "TA0040", "name": "Impact"}, "technique": {"id": 15, "mitre_id": "T1491", "name": "Defacement"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6161, "name": "Process 1", "content": "cmd.exe /c \"echo \"<PERSON><PERSON><PERSON><PERSON>(Revil) Ransomware is there\" > \"%TMP%\\Sodinokibi.txt\"\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6162, "name": "Process 2", "content": "cmd.exe /c \"echo \"Your files are encrypted.\" >> \"%TMP%\\Sodinokibi.txt\"\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 6163, "name": "Process 3", "content": "cmd.exe /c notepad.exe \"%TMP%\\Sodinokibi.txt\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 6083, "name": "Process 1", "content": "cmd.exe /c taskkill /f /im \"notepad.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 6164, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\Sodinokibi.txt\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 18, "name": "Impact"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"Sodinokibi.txt\" AND NOT ((\"PICUS_REWIND\") OR (\"Scenarios\" OR \"Simulation\")))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31634, "node_id": 41}, {"detail": {"id": 31633, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a new Registry Key for Autorun of cmd.exe", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10491, "node_id": 44, "action": {"id": 10491, "display_id": 12529, "owasp_rank": "", "name": "Create a new Registry Key for Autorun of cmd.exe", "release_date": "2022-06-18T12:22:03.213697Z", "description": "In this action, an attacker is trying to achieve persistence by adding new autorun hive as a key (New Value #1) for persistence.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6128, "name": "Process 1", "content": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"New Value #1\" /t REG_SZ /d \"cmd.exe\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The operation completed successfully."}]}], "rewind_processes": [{"id": 6129, "name": "Process 1", "content": "reg.exe delete \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" /v \"New Value #1\" /f", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": "", "cwe": "", "malware_family": "", "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"reg\" AND \"New Value\" AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}, "detection": {"availableSources": ["<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Microsoft Sentinel", "Palo Alto Cortex XDR", "IBM QRadar", "Microfocus ArcSight ESM", "Crowdstrike", "Microsoft Defender", "SentinelOne", "VMware Carbon Black EDR"], "contentSources": [{"name": "IBM QRadar", "rules": [{"ruleName": "Persistence via Octopus Trojan with Autorun Key Value Creation", "ruleId": "3240", "description": "Detects the attempt to create autorun key value \"New Value #1\" via reg command. This technique is commonly utilized for persistence as LazyScripter Apt Group's usage in its campaigns.", "releaseDate": 1620777600000, "updateDate": 1662940800000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "(LOGSOURCETYPENAME(devicetype)='Microsoft Windows Security Event Log' and EventID='4688' and Image ilike '%\\reg.exe' and \"Process CommandLine\" ilike '%add%' and \"Process CommandLine\" ilike '%\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run%' and \"Process CommandLine\" ilike '%New Value #1%')", "logSource": {"productName": ["windows"], "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Penetration testing"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}], "crowdStrikeLogScaleQuery": "", "detectionDeployInfo": {"isDeployable": false, "detectionLicense": false, "canDeploy": false, "peerId": 0, "integrationVersion": 0, "integrationName": ""}, "deploymentInformation": null}]}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 31633, "node_id": 43}, {"detail": {"id": 31632, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Execute SharpHide Tool", "tag": "execute", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2487, "node_id": 46, "action": {"id": 2487, "display_id": 11794, "owasp_rank": "", "name": "Execute SharpHide Tool", "release_date": "2022-06-18T12:24:07.341477Z", "description": "In this action, an attacker is trying to create a hidden registry key by using SharpHide Tool", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5207, "name": "Process 1", "content": "%TMP%\\SharpHide.exe action=create keyvalue=\"C:\\Windows\\Temp\\Bla.exe\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 1577, "name": "SharpHide.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\SharpHide.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 5208, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\SharpHide.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"SharpHide.exe\") OR (\"0986251f701a76f49ff9bf2efa3d6008c910572d9441f0a01ca17814c290fdea\" OR \"e25896cd448dd05d7091eba6f5a75df952760027\" OR \"91fc44b5f3aae412bc8791f5e270ab31\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31632, "node_id": 45}, {"detail": {"id": 31631, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a Lnk File in Startup Folder by using SharPersist Tool", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2480, "node_id": 48, "action": {"id": 2480, "display_id": 11766, "owasp_rank": "", "name": "Create a Lnk File in Startup Folder by using SharPersist Tool", "release_date": "2022-06-18T12:24:07.408162Z", "description": "In this action, an attacker is trying to create a Lnk File in Startup Folder.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5192, "name": "Process 1", "content": "%TMP%\\sharpersist.exe -t startupfolder -c \"C:\\Windows\\System32\\cmd.exe\" -a \"/c calc.exe\" -f \"Some File\" -m add", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 1570, "name": "sharpersist.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\sharpersist.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 5191, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\sharpersist.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 5193, "name": "Process 2", "content": "%TMP%\\sharpersist.exe -t startupfolder -f \"Some File\" -m remove", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"sharpersist\") OR (\"7806b81514ecc44219a6f6193b15b23aea0a947f3c91b339332bea1445745596\" OR \"daedb9d53501dcb655044ce4cbb5d39a645070b4\" OR \"ad9b68820de30d71cc2ec17235bb4ad1\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31631, "node_id": 47}, {"detail": {"id": 31630, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Create a Registry Key by using SharPersist Tool", "tag": "create", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2479, "node_id": 50, "action": {"id": 2479, "display_id": 11765, "owasp_rank": "", "name": "Create a Registry Key by using SharPersist Tool", "release_date": "2022-06-18T12:24:07.421903Z", "description": "In this action, an attacker is trying to create a registry key in Hkcurun Hive.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 5189, "name": "Process 1", "content": "%TMP%\\sharpersist.exe -t reg -c \"C:\\Windows\\System32\\cmd.exe\" -a \"/c calc.exe\" -k \"hkcurun\" -v \"Test Stuff\" -m add", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 1570, "name": "sharpersist.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\sharpersist.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "[+]"}]}], "rewind_processes": [{"id": 5190, "name": "Process 1", "content": "%TMP%\\sharpersist.exe -t reg -k \"hkcurun\" -v \"Test Stuff\" -m remove", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 5191, "name": "Process 2", "content": "cmd.exe /c del \"%TMP%\\sharpersist.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"sharpersist\") OR (\"7806b81514ecc44219a6f6193b15b23aea0a947f3c91b339332bea1445745596\" OR \"daedb9d53501dcb655044ce4cbb5d39a645070b4\" OR \"ad9b68820de30d71cc2ec17235bb4ad1\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 31630, "node_id": 49}, {"detail": {"id": 37169, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Register New Time Provider via Rundll32.exe", "tag": "register", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "mitre_name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2349, "node_id": 52, "action": {"id": 2349, "display_id": 11385, "owasp_rank": "", "name": "Register New Time Provider via Rundll32.exe", "release_date": "2022-06-18T12:24:09.178955Z", "description": "In this action, an attacker is trying to register the malicious gametime.dll as a new Time Provider via rundll32.exe.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 59, "mitre_id": "T1547", "name": "Boot or Logon Autostart Execution"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 13514, "name": "Process 1", "content": "rundll32.exe C:\\Windows\\System32\\gametime.dll, Register", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": [{"id": 17875, "name": "gametime.dll", "file_type": "undefined", "file_size": 0, "path": "C:\\Windows\\System32\\gametime.dll", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 13516, "name": "Process 2", "content": "sc.exe stop w32time", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 13517, "name": "Process 3", "content": "sc.exe start w32time", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12508, "name": "Process 4", "content": "{predefined-process-list} winver.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 12509, "name": "Process 1", "content": "{predefined-process-kill} winver.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13515, "name": "Process 2", "content": "rundll32.exe C:\\Windows\\System32\\gametime.dll, <PERSON><PERSON><PERSON>", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13519, "name": "Process 3", "content": "sc.exe stop w32time", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13518, "name": "Process 4", "content": "{predefined-file-delete} C:\\Windows\\System32\\gametime.dll", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 13520, "name": "Process 5", "content": "sc.exe start w32time", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"gametime.dll\") OR (\"236a85fe22a29d4d9a5d7d44d43370938fd6d3ad6143d47b17347b652e11fa8c\" OR \"8fc626b0f975ffa01dce4e0d8d17c4fd4b3e8256\" OR \"1d28f94867b883bec630bdda1d2b7e58\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))"}}], "true_items": [], "false_items": [], "type": "objective", "id": 37169, "node_id": 51}], "campaign_condition": "OBJ1 or OBJ2 or OBJ3 or OBJ4 or OBJ5 or OBJ6 or OBJ7 or OBJ8 or OBJ9 or OBJ10 or OBJ11 or OBJ12 or OBJ13 or OBJ14 or OBJ15 or OBJ16 or OBJ17 or OBJ18 or OBJ19 or OBJ20 or OBJ21 or OBJ22 or OBJ23 or OBJ24 or OBJ25", "objective_conditions": [{"id": 31630, "condition": "A1"}, {"id": 31631, "condition": "A1"}, {"id": 31632, "condition": "A1"}, {"id": 31633, "condition": "A1"}, {"id": 31634, "condition": "A1"}, {"id": 31635, "condition": "A1"}, {"id": 31636, "condition": "A1"}, {"id": 31637, "condition": "A1"}, {"id": 31638, "condition": "A1"}, {"id": 31640, "condition": "A1"}, {"id": 31641, "condition": "A1"}, {"id": 31642, "condition": "A1"}, {"id": 31643, "condition": "A1"}, {"id": 31644, "condition": "A1"}, {"id": 31645, "condition": "A1"}, {"id": 31646, "condition": "A1"}, {"id": 31647, "condition": "A1"}, {"id": 31648, "condition": "A1"}, {"id": 31649, "condition": "A1"}, {"id": 31650, "condition": "A1"}, {"id": 31651, "condition": "A1"}, {"id": 31652, "condition": "A1"}, {"id": 31653, "condition": "A1"}, {"id": 31654, "condition": "A1"}, {"id": 37169, "condition": "A1"}], "assessment_run_status": "", "assessment_da_run_status": "", "obfuscation_methods": null, "detection_licence_modules": null}}