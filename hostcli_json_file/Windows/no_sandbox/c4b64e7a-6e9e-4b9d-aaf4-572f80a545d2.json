{"actions": {"c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "ver": 16, "vid": "370363", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 Invisi-shell 绕过所有 Powershell 安全功能", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试使用 CorProfiler 选项执行 Powershell。", "created_at": "2025-07-21 21:42:19", "updated_at": "2025-07-21 21:42:19", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "c4b64e7a-6e9e-4b9d-aaf4-572f80a545d2", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["control EDR/XDR", "control HIDS", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "run_as SYSTEM", "os Windows:10", "os Windows:Server 2016", "mitre_techniques T1056 - 输入捕获", "control AV", "os Windows:Server 2019", "os Windows:Server 2022", "mitre_tactics TA0009 - 信息收集", "os Windows:11", "run_as User"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "RunWithPathAsAdmin.bat", "file_name": "RunWithPathAsAdmin.bat", "file_owner": "system", "file_transfer_library": 999057719, "file_type": "batch", "filesize": 223, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "2f903876772d69bb184bfe771354a766", "sha1sum": "d6bb822839b04c284d87da27ef89bf6433043be3", "sha256sum": "835747f27a37aa3fab9a116d7480701b813c16eba6b903eb82b96fa230aa992e"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "InvisiShellProfiler.dll", "file_name": "InvisiShellProfiler.dll", "file_owner": "system", "file_transfer_library": 999057720, "file_type": "dll", "filesize": 119808, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "347324eff50d650d345074b51fb7775c", "sha1sum": "f2e5a95fb27e878d89bb1525d77b6b7b7fa53df9", "sha256sum": "833d68452ea956b5d23bcb243cd327bd05dfd79fb5a4a34064783749eafa1ddf"}], "monitor_connections": "off", "raw_text": "cd C:\\Users\\<USER>\\Documents\\{{v_action_id}} && echo exit | RunWithPathAsAdmin.bat\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "cd C:\\Users\\<USER>\\Documents\\{{v_action_id}} && echo exit | RunWithPathAsAdmin.bat", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "Microsoft Corporation", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}