{"action": {"name": "(自定义规则)主机命令行 - Capture Video by using Screenshooter - 8", "desc": "In this action, an attacker is trying to record the screen of the target for 15 seconds by using Screenshooter tool.[apt36_threat_group_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Screenshooter.exe", "file_owner": "system", "file_transfer_library": 998002077, "randomize_file": null, "file_name": "Screenshooter.exe"}], "steps": [{"step_order": 1, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Screenshooter.exe record \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\" 15", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "\\[\\+\\]\\ Recording\\ completed", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c dir \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\*.mp4\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Collection", "ATT&CK:T1125"], "mitre_mitigation": []}}, "picus_id": 2763, "uuid": "de2e5fa6-0096-47a3-99d0-c1872f99b310", "sigma_device_query": "title: Possible Screenshot and Video Capture by Loading ScreenRecorderLib\nstatus: experimental\ndescription: Detects the screen and video capture attempt by loading ScreenRecorderLib.dll. This library can be used by screen recording and screenshot tools like Screenshooter. Adversaries may use this technique in order to gather information about victim system.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1125/\n    - https://attack.mitre.org/techniques/T1113/\n    - https://attack.mitre.org/tactics/TA0009/\n    - https://github.com/RedSiege/Screenshooter\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 7\n        OriginalFileName: 'ScreenRecorderLib.dll'\n    condition: selection\nfalsepositives:\n    - Usual user activity\nlevel: medium\ntags:\n    - attack.collection\n    - attack.t1113\n    - attack.ta0009\n    - attack.t1125"}