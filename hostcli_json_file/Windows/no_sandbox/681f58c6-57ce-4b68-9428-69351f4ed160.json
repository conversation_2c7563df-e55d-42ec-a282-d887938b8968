{"actions": {"681f58c6-57ce-4b68-9428-69351f4ed160": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "ver": 16, "vid": "371499", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - Windows，通过注册表在本地计算机上启用受限管理模式", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试通过注册表项为 RDP 设置启用受限管理模式。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:42:05", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1028", "tag_en": "M1028", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1030", "tag_en": "M1030", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1032", "tag_en": "M1032", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1035", "tag_en": "M1035", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1042", "tag_en": "M1042", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_tactics", "tag": "TA0008 - 横向移动", "tag_en": "TA0008 - Lateral Movement", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "mitre_techniques", "tag": "T1021.001 - 远程桌面协议", "tag_en": "T1021.001 - Remote Desktop Protocol", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "681f58c6-57ce-4b68-9428-69351f4ed160", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_mitigation ATT&CK:M1026", "mitre_mitigation ATT&CK:M1035", "mitre_mitigation ATT&CK:M1018", "mitre_tactics TA0008 - 横向移动", "mitre_mitigation ATT&CK:M1032", "mitre_mitigation ATT&CK:M1030", "control HIDS", "os Windows:Server 2019", "os Windows:Server 2022", "mitre_mitigation ATT&CK:M1042", "mitre_mitigation ATT&CK:M1028", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "os Windows:11", "mitre_mitigation ATT&CK:M1047", "run_as SYSTEM", "os Windows:Server 2016", "os Windows:10", "mitre_techniques T1021.001 - 远程桌面协议", "control EDR/XDR"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "reg.exe add \"hklm\\system\\currentcontrolset\\control\\lsa\" /v \"disablerestrictedadmin\" /t reg_dword /d 00000000 /f\r\nreg.exe query \"hklm\\system\\currentcontrolset\\control\\lsa\"\r\nreg.exe delete \"HKLM\\System\\CurrentControlSet\\control\\lsa\" /v \"disablerestrictedadmin\" /f\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "reg.exe add \"hklm\\system\\currentcontrolset\\control\\lsa\" /v \"disablerestrictedadmin\" /t reg_dword /d 00000000 /f", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "reg.exe query \"hklm\\system\\currentcontrolset\\control\\lsa\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "match", "success_match": "disable<PERSON><PERSON><PERSON><PERSON>", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "reg.exe delete \"HKLM\\System\\CurrentControlSet\\control\\lsa\" /v \"disablerestrictedadmin\" /f", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}