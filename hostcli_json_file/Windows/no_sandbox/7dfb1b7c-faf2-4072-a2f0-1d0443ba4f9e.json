{"actions": {"7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "ver": 16, "vid": "376757", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用命令行工具收集系统信息线程", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者利用几个常见的 Windows 命令行工具来枚举资源。（在泄露数据到 C&C 服务器之前）", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:59", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "mitre_tactics", "tag": "TA0007 - 信息发现", "tag_en": "TA0007 - Discovery", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "mitre_techniques", "tag": "T1016 - 系统网络配置发现", "tag_en": "T1016 - System Network Configuration Discovery", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "7dfb1b7c-faf2-4072-a2f0-1d0443ba4f9e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_techniques T1016 - 系统网络配置发现", "control AV", "control HIDS", "os Windows:11", "os Windows:Server 2016", "os Windows:10", "mitre_tactics TA0007 - 信息发现", "control EDR/XDR", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as SYSTEM", "run_as Admin", "os Windows:Server 2022", "os Windows:Server 2019"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "mkdir C:\\Users\\<USER>\\Documents\\{{v_action_id}} & ipconfig /all >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"\r\narp -a >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"\r\nroute PRINT >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"\r\nsysteminfo >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"\r\ntasklist /v /fo \"TABLE\" >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "mkdir C:\\Users\\<USER>\\Documents\\{{v_action_id}} & ipconfig /all >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "arp -a >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "route PRINT >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "systeminfo >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /v /fo \"TABLE\" >> \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\exfil.txt\"", "prompt": "auto", "sleep": 4, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}