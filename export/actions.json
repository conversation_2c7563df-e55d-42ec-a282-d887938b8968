{"actions": {"dd48d34c-fc13-4a17-a92b-9e6419373e74": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "ver": 15, "vid": "8001476", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - HTTP隧道，XLSX 文件，上传，变种 #10", "action_mitigation": "", "metadata_version": 27, "desc": "此验证动作还原了通过HTTP隧道上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 20:15:05", "updated_at": "2025-08-10 20:15:05", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "mitre_techniques", "tag": "T1560 - 归档收集数据", "tag_en": "T1560 - Archive Collected Data", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "dd48d34c-fc13-4a17-a92b-9e6419373e74", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>ICMP", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "http", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000865, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 80, "transport_layer": "tcp", "tunnel_cidr": "*************", "tunnel_protocol": "http", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "80", "transfer": {"method": "post", "protocol": "http"}, "tunnel": {"protocol": "http"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "1934c1f5-db00-46d6-9025-ad694a40336e": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "ver": 15, "vid": "8001475", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - SSH隧道，XLSX 文件，上传，变种 #9", "action_mitigation": "", "metadata_version": 27, "desc": "此验证动作还原了通过SSH隧道上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-10 20:14:05", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "mitre_techniques", "tag": "T1560 - 归档收集数据", "tag_en": "T1560 - Archive Collected Data", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "1934c1f5-db00-46d6-9025-ad694a40336e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>ICMP", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "http", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000865, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 22, "transport_layer": "tcp", "tunnel_cidr": "*************", "tunnel_protocol": "ssh", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "22", "transfer": {"method": "post", "protocol": "http"}, "tunnel": {"protocol": "ssh"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "3ad4e538-d6f2-42e3-8c43-438363976eda": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "ver": 15, "vid": "8001474", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - DNS，XLSX 文件，上传，变种 #8", "action_mitigation": "", "metadata_version": 27, "desc": "此验证动作还原了通过DNS上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 20:12:22", "updated_at": "2025-08-10 20:12:22", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "Cisco Firepower", "message": "PROTOCOL-ICMP Unusual PING detected", "source_identifier": "29456", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "<PERSON><PERSON>", "message": "GPL ICMP_INFO Echo Reply", "source_identifier": "2100408", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "Palo Alto Networks", "message": "ICMP Tunnel for Hans IP over ICMP(18692)", "source_identifier": "18692", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "<PERSON><PERSON>", "message": "GPL ICMP Large ICMP Packet", "source_identifier": "2100499", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "<PERSON><PERSON>", "message": "GPL ICMP_INFO PING", "source_identifier": "2100384", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "Cisco Firepower", "message": "INDICATOR-COMPROMISE Win.Trojan.AnchorBotDNS variant outbound ICMP connection", "source_identifier": "56565", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "Cisco Firepower", "message": "SERVER-<PERSON><PERSON>ER MRLG fastping echo reply memory corruption attempt", "source_identifier": "31767", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "source": "Cisco Firepower", "message": "INDICATOR-OBFUSCATION ICMP HTTP tunneling attempt", "source_identifier": "47401", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "mitre_techniques", "tag": "T1560 - 归档收集数据", "tag_en": "T1560 - Archive Collected Data", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "3ad4e538-d6f2-42e3-8c43-438363976eda", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>ICMP", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "http", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "dns_domain": "test.example.com", "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000865, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 53, "transport_layer": "tcp", "tunnel_cidr": "*************", "tunnel_protocol": "dns", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "test.example.com", "orig_file_name": "informations.xlsx", "path": "", "target_port": "53", "transfer": {"method": "post", "protocol": "http"}, "tunnel": {"protocol": "dns"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "a733d403-4f95-4009-bc99-77e02a40ad0c": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "ver": 15, "vid": "8001473", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - ICMP，XLSX 文件，上传，变种 #7", "action_mitigation": "", "metadata_version": 27, "desc": "此验证动作还原了通过 ICMP上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-10 20:11:20", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "Cisco Firepower", "message": "PROTOCOL-ICMP Unusual PING detected", "source_identifier": "29456", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "<PERSON><PERSON>", "message": "GPL ICMP_INFO Echo Reply", "source_identifier": "2100408", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "Palo Alto Networks", "message": "ICMP Tunnel for Hans IP over ICMP(18692)", "source_identifier": "18692", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "<PERSON><PERSON>", "message": "GPL ICMP Large ICMP Packet", "source_identifier": "2100499", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "<PERSON><PERSON>", "message": "GPL ICMP_INFO PING", "source_identifier": "2100384", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "Cisco Firepower", "message": "INDICATOR-COMPROMISE Win.Trojan.AnchorBotDNS variant outbound ICMP connection", "source_identifier": "56565", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "Cisco Firepower", "message": "SERVER-<PERSON><PERSON>ER MRLG fastping echo reply memory corruption attempt", "source_identifier": "31767", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "source": "Cisco Firepower", "message": "INDICATOR-OBFUSCATION ICMP HTTP tunneling attempt", "source_identifier": "47401", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "mitre_techniques", "tag": "T1560 - 归档收集数据", "tag_en": "T1560 - Archive Collected Data", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "a733d403-4f95-4009-bc99-77e02a40ad0c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>ICMP", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "http", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "dns_domain": "test.example.com", "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000865, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 80, "transport_layer": "tcp", "tunnel_cidr": "*************", "tunnel_protocol": "icmp", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "test.example.com", "orig_file_name": "informations.xlsx", "path": "", "target_port": "80", "transfer": {"method": "post", "protocol": "http"}, "tunnel": {"protocol": "icmp"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "53d071c8-fce1-4c88-b861-dcfd3c2e6536": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "ver": 14, "vid": "8001472", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - SCPV1，XLSX 文件，上传，变种 #6", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 22 上通过 SCPV1上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-10 20:10:22", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "53d071c8-fce1-4c88-b861-dcfd3c2e6536", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "scpv1", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 22, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "22", "transfer": {"method": "post", "protocol": "scpv1"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "a1e9abd0-5080-4f0e-bf50-a2efde14132a": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "ver": 14, "vid": "8001471", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - SCP，XLSX 文件，上传，变种 #6", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 22 上通过 SCP上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 20:04:34", "updated_at": "2025-08-10 20:04:34", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "a1e9abd0-5080-4f0e-bf50-a2efde14132a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "scp", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 22, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "22", "transfer": {"method": "post", "protocol": "scp"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "03de0dd6-e276-4dba-a5fb-6354d38390dd": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "ver": 14, "vid": "8001470", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - SFTP，XLSX 文件，上传，变种 #5", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 22 上通过 SFTP上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 20:02:49", "updated_at": "2025-08-10 20:02:49", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "03de0dd6-e276-4dba-a5fb-6354d38390dd", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "sftp", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 22, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "22", "transfer": {"method": "post", "protocol": "sftp"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "ccff6164-eb7f-4567-a081-2033ad9caf8d": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "ver": 14, "vid": "8001469", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - TFTP，XLSX 文件，上传，变种 #4", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 69 上通过 TFTP上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 20:00:57", "updated_at": "2025-08-10 20:00:57", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "source": "ET OPEN", "message": "ET INFO FTP STOR to External Network", "source_identifier": "2015016", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "source": "<PERSON><PERSON>", "message": "GPL FTP FTP anonymous login attempt", "source_identifier": "2100553", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "source": "ET OPEN", "message": "ET POLICY FTP Login Successful", "source_identifier": "2003410", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "ccff6164-eb7f-4567-a081-2033ad9caf8d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "tftp", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 69, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "69", "transfer": {"method": "post", "protocol": "tftp"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "3f397967-92e7-4e36-8367-5e16e69b0d7e": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "ver": 14, "vid": "8001468", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - https，XLSX 文件，上传，变种 #3", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 443 上通过 https上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 19:59:14", "updated_at": "2025-08-10 19:59:14", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "source": "ET OPEN", "message": "ET INFO FTP STOR to External Network", "source_identifier": "2015016", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "source": "<PERSON><PERSON>", "message": "GPL FTP FTP anonymous login attempt", "source_identifier": "2100553", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "source": "ET OPEN", "message": "ET POLICY FTP Login Successful", "source_identifier": "2003410", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "3f397967-92e7-4e36-8367-5e16e69b0d7e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "https", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 443, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "443", "transfer": {"method": "post", "protocol": "https"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "46a2326e-0432-4073-aa36-38deb97c9de5": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "ver": 14, "vid": "8001467", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - FTP，XLSX 文件，上传，变种 #2", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 21 上通过 ftp上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 19:57:36", "updated_at": "2025-08-10 19:57:36", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "source": "ET OPEN", "message": "ET INFO FTP STOR to External Network", "source_identifier": "2015016", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "source": "<PERSON><PERSON>", "message": "GPL FTP FTP anonymous login attempt", "source_identifier": "2100553", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "source": "ET OPEN", "message": "ET POLICY FTP Login Successful", "source_identifier": "2003410", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "46a2326e-0432-4073-aa36-38deb97c9de5", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "ftp", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 21, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "21", "transfer": {"method": "post", "protocol": "ftp"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655": {"newStyle": false, "enable": true, "action_type": "file_transfer", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 3, "uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "ver": 14, "vid": "8001466", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "数据泄露 - http，XLSX 文件，上传，变种 #1", "action_mitigation": "", "metadata_version": 16, "desc": "此验证动作还原了在端口 80上通过 http 上传 XLSX 文件。该文件包含 100 行随机生成的姓名、身份证、地址、电话号码、银行卡号、邮箱信息。", "created_at": "2025-08-10 19:55:11", "updated_at": "2025-08-10 19:55:11", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "source": "ET OPEN", "message": "ET INFO FTP STOR to External Network", "source_identifier": "2015016", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "source": "<PERSON><PERSON>", "message": "GPL FTP FTP anonymous login attempt", "source_identifier": "2100553", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "source": "ET OPEN", "message": "ET POLICY FTP Login Successful", "source_identifier": "2003410", "cn_show": false, "product_version": "", "signature_version": ""}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "source": "Palo Alto Networks", "message": "Manufacturer default username and/or password found in FTP login(57354)", "source_identifier": "57354", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "mitre_techniques", "tag": "T1071.002 - 文件传输协议", "tag_en": "T1071.002 - File Transfer Protocols", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "control", "tag": "DLP", "tag_en": "DLP", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "ea31b3ca-0804-49a0-a0eb-3f7c2c2e1655", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>Upload-Exfil", "OS/Platform>General-OS/Platform", "Stage of Attack>Action on Target"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": {"app_layer": "http", "check_date": "0001-01-01 00:00:00", "compare_transfer": true, "file_name": "informations.xlsx", "file_size": 17524, "file_transfer_library": 998000864, "file_type": "", "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "malicious": 0, "md5": "495f7c9f465f7d2855a6e1333e0eb9cd", "method": "post", "sha1": "5056ffaf45d7ae510445417d210feb6acd10111d", "sha256": "5605f3709859c8de70ee492be1bfe4441192aaa6888d2f2f6863e0aba977e7ad", "target_port": 80, "transport_layer": "tcp", "tunnel_protocol": "none", "undetected": 0}, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": null, "port_scan_action": null, "required_parms": [], "is_http": false, "args": {"content_encoding": "", "custom_shell_path": "", "dns_domain": "", "orig_file_name": "informations.xlsx", "path": "", "target_port": "80", "transfer": {"method": "post", "protocol": "http"}, "tunnel": {"protocol": "none"}}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}