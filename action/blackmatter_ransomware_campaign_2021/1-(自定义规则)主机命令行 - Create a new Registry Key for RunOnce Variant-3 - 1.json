{"action": {"name": "(自定义规则)主机命令行 - Create a new Registry Key for RunOnce Variant-3 - 1", "desc": "In this action, an attacker is trying to achieve persistence by adding a key (franceisshit) to  runonce hive.[blackmatter_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "dummy.exe", "file_owner": "system", "file_transfer_library": 998002357, "randomize_file": null, "file_name": "dummy.exe"}], "steps": [{"step_order": 1, "command": "reg.exe add \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce\" /v \"*franceisshit\" /d \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\dummy.exe\" /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "The\\ operation\\ completed\\ successfully", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1112"], "mitre_mitigation": ["ATT&CK:M1024"]}}, "picus_id": 10876, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Persistence via Windows Registry Run Keys", "ruleId": "2606", "description": "Detects the addition of a visual basic script or an executable file to the Windows Registry Run Key. Adversaries may achieve persistence by adding a program to a Registry run key. Adding an entry to the \"run keys\" in the Registry will cause the program referenced to be executed when a user logs in.", "releaseDate": 1590537600000, "updateDate": 1638921600000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Persistence via Windows Registry Run Keys\nstatus: experimental\ndescription: Detects the addition of a visual basic script or an executable file to the Windows Registry Run Key. Adversaries may achieve persistence by adding a program to a Registry run key. Adding an entry to the \"run keys\" in the Registry will cause the program referenced to be executed when a user logs in. \nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0003/\n   - https://attack.mitre.org/tactics/TA0004/\n   - https://attack.mitre.org/techniques/T1547/001/\nlogsource:\n    product: windows\n    service: security\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\reg.exe'\n        ProcessCommandLine: \n          - '*add*'\n    selection1:\n        ProcessCommandLine: \n          - '*.vbs*'\n          - '*.exe*'\n    selection2:\n        ProcessCommandLine: \n          - '*\\Software\\Microsoft\\Windows\\CurrentVersion\\Run*'\n          - '*\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnce*'\n          - '*\\Software\\Microsoft\\Windows\\CurrentVersion\\RunOnceEx*'\n          - '*\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServicesOnce*'\n          - '*\\Software\\Microsoft\\Windows\\CurrentVersion\\RunServices*'\n          - '*\\Software\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run*'\n    condition: selection and selection1 and selection2\nfalsepositives:\n    - Unknown\nlevel: high\ntags:\n    - attack.persistence\n    - attack.ta0003\n    - attack.privilege_escalation\n    - attack.ta0004\n    - attack.t1547.001", "logSource": {"productName": "windows", "service": "security", "policies": null}, "falsePositives": ["Unknown"], "mitre": [{"name": "Registry Run Keys / Startup Folder", "mitreId": "T1547.001", "url": "https://attack.mitre.org/techniques/T1547/001/", "type": "sub_technique"}]}]}, "uuid": "75c8a09c-4717-41dc-9d86-cc2f1601e4d4"}