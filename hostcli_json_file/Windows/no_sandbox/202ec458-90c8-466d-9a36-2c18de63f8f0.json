{"actions": {"202ec458-90c8-466d-9a36-2c18de63f8f0": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "ver": 16, "vid": "382253", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 执行 TeamViewer 进行远程连接", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：做好环境中各安全区域的访问控制；加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制以及执行PowerShell等权限控制；针对使用到的工具在终端/主机安全产品中基于文件Hash添加本地黑名单策略，监控可疑命令执行。中长期建议：增加特权帐号管理产品以加强对身份信息系统的监测与保护；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图执行 TeamViewer。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:23", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "ics_mitre_tactics", "tag": "TA0108 - 初始访问", "tag_en": "TA0108 - Initial Access", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "ics_mitre_techniques", "tag": "T0822 - 外部远程服务", "tag_en": "T0822 - External Remote Services", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "mitre_mitigation", "tag": "M1030", "tag_en": "M1030", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "mitre_mitigation", "tag": "M1032", "tag_en": "M1032", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "mitre_mitigation", "tag": "M1035", "tag_en": "M1035", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "mitre_mitigation", "tag": "M1042", "tag_en": "M1042", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "mitre_techniques", "tag": "T1133 - 外部远程服务", "tag_en": "T1133 - External Remote Services", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "202ec458-90c8-466d-9a36-2c18de63f8f0", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1032", "ATT&CK:M1035", "ATT&CK:M1030", "ATT&CK:M1042"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1133"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "TeamViewerPortable.zip", "file_name": "TeamViewerPortable.zip", "file_owner": "system", "file_transfer_library": 999051141, "file_type": "zip", "filesize": 28739596, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "b11e70f8bccdb683e43f27b2089308c7", "sha1sum": "838f0cf23ca2fac96f3b42c70da11960ffd25544", "sha256sum": "6160742c7d30dd98d8935dca6ccf5fa93ed46b4cf8818d7eaa02fe26b5a499b3"}], "monitor_connections": "off", "raw_text": "powershell.exe -c \"Add-Type -AssemblyName System.IO.Compression.FileSystem; \"[System.IO.Compression.ZipFile]::ExtractToDirectory('C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\TeamViewerPortable.zip',\" 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\teamviewer\\.')\"\r\nC:\\Users\\<USER>\\Documents\\{{v_action_id}}\\teamviewer\\TeamViewer.exe \r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"Add-Type -AssemblyName System.IO.Compression.FileSystem; \"[System.IO.Compression.ZipFile]::ExtractToDirectory('C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\TeamViewerPortable.zip',\" 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\teamviewer\\.')\"", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "start \"\" /b C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\teamviewer\\TeamViewer.exe", "prompt": "auto", "sleep": 30, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill /f /im TeamViewer.exe & taskkill /f /im tv_w32.exe & taskkill /f /im tv_x64.exe", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}