{"action": {"name": "(自定义规则)主机命令行 - List All the Running Processes - 6", "desc": "In this action, the tasklist command is using to list currently running processes of the victim system.[bisonal_backdoor_campaign_2020]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 2, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c tasklist.exe /v >> \"C:\\Windows\\debug\\1.txt\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Collection", "ATT&CK:T1005"], "mitre_mitigation": ["ATT&CK:M1057"]}}, "picus_id": 10728, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Process Discovery via Tasklist Tool", "ruleId": "3545", "description": "Detects the attempt to gather information about currently running processes via tasklist tool. Adversaries use this method to shape follow-on behaviors.", "releaseDate": 1576195200000, "updateDate": 1576195200000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Process Discovery via Tasklist Tool\nstatus: stable\ndescription: Detects the attempt to gather information about currently running processes via tasklist tool. Adversaries use this method to shape follow-on behaviors.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1057/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0075/\n   - https://attack.mitre.org/groups/G0010/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/tasklist\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\tasklist.exe'\n    service:\n        ProcessCommandLine: '*svc*'\n    condition: selection and not service\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.ta0007 \n    - attack.t1057\n    - attack.g0075\n    - attack.g0010", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Process Discovery", "mitreId": "T1057", "url": "https://attack.mitre.org/techniques/T1057/", "type": "technique"}]}]}, "uuid": "e532153b-8440-4b45-83da-65b11811e73d", "sigma_device_query": ""}