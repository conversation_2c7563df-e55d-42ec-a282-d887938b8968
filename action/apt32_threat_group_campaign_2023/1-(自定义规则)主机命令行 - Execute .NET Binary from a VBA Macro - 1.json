{"action": {"name": "(自定义规则)主机命令行 - Execute .NET Binary from a VBA Macro - 1", "desc": "In this action, an attacker is trying to execute an arbitrary .NET binary from a VBA macro.[apt32_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vba2clr.docm", "file_owner": "system", "file_transfer_library": 998002053, "randomize_file": null, "file_name": "vba2clr.docm"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "HelloPicus.exe", "file_owner": "system", "file_transfer_library": 998002054, "randomize_file": null, "file_name": "RunNslookupNET46.exe"}], "steps": [{"step_order": 1, "command": "cmd.exe /c dir \"%APPDATA%\\Microsoft\\Templates\\\" || mkdir \"%APPDATA%\\Microsoft\\Templates\\\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "cmd.exe /c \"%APPDATA%\\Microsoft\\Templates\\vba2clr.docm\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "cmd.exe /c timeout 5 && tasklist /svc | findstr /i winver.exe", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "winver", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Execution", "ATT&CK:T1059"], "mitre_mitigation": ["ATT&CK:M1042", "ATT&CK:M1049", "ATT&CK:M1026", "ATT&CK:M1045", "ATT&CK:M1021", "ATT&CK:M1040", "ATT&CK:M1038"]}}, "picus_id": 23178, "uuid": "e5522300-e679-4651-aae5-91c4985f6d3f", "sigma_device_query": ""}