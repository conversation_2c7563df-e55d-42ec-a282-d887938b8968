{"sequences": [{"profiles": [{"name": null, "steps": [{"desc": "In this action, an attacker is creating a shortcut named TA866.lnk in the logged-on user’s Startup folder.", "name": "主机命令行 - Create a Shortcut named TA866.lnk to Startup Folder using Powershell Script", "sim_actions": ["0dd36c10-4b16-495a-80a7-4232695b17e0"], "order": 0}, {"desc": "In this action, an attacker tries to copy a file from the Public Downloads directory to the current user's Startup folder.", "name": "主机命令行 - Copy a File \"Winvoke.exe\" in Startup Folder for Persistence", "sim_actions": ["7ec2af60-1819-42f2-bc26-3b1d4b333599"], "order": 1}, {"desc": "In this action, an attacker is trying to create a new registry key \"{01575CFE-9A55-4003-A5E1-F38D1EBDCBE1}\" in HKCU to persist on the target system.", "name": "主机命令行 - Create a New Persistence Registry Key for FileSyncShell64.dll in HKCU Hive", "sim_actions": ["8fc00407-2ae2-4c32-bace-b7fe83a6eb02"], "order": 2}, {"desc": "In this action, an attacker is trying to create a new registry key \"Recycle Bin\" to persist on the target system.", "name": "主机命令行 - Create a New Persistence Registry Key \"Recycle Bin\" in HKLM hive by using RecycleBinPersistence Tool", "sim_actions": ["86e00dfb-57e5-43c6-9895-43aa94d15e89"], "order": 3}, {"desc": "In this action, an attacker is trying create a new registry key \"EverNoteTrayUService\" to persist on the target system.", "name": "主机命令行 - Create a New Persistence Registry Key \"EverNoteTrayUService\" in HKCU hive", "sim_actions": ["26b578bd-c9b8-4c5e-90a5-7ae9eb104638"], "order": 4}, {"desc": "In this action, an attacker tries to copy an executable to the startup folder for the persistence via Grpconv.exe binary.", "name": "主机命令行 - Copy an Executable to Startup Folder for Persistence via Grpconv.exe", "sim_actions": ["6e7aa54a-b615-4a73-bec3-50c57867b4b2"], "order": 5}, {"desc": "In this action, an attacker is trying create a registry key (Run) via a VBA Macro.", "name": "主机命令行 - Create a Registry Run Key via VBA Macro", "sim_actions": ["77a3a285-4149-416f-bfcf-0541b986bb30"], "order": 6}, {"desc": "In this action, an attacker is trying to add the dummy.exe file to autorun hive as a key for persistence.", "name": "主机命令行 - Create a new Registry Key for Autorun of dummy.exe Variant-4", "sim_actions": ["60f5e056-e7c1-4238-832e-0abf6488d27d"], "order": 7}, {"desc": "In this action, an attacker is trying to create a new key \"CurlInit\" under the \"HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\\" hive.", "name": "主机命令行 - Create a New Registry Key in HKCU Hive Variant-4", "sim_actions": ["645d7a3d-c1dd-427a-b0cc-780e8e19813e"], "order": 8}, {"desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "name": "主机命令行 - Write a File that Contains Ransom Note for Netwalker Ransomware and Open It", "sim_actions": ["0aba08a5-e7b4-40ee-a7c1-d8919a579f6a"], "order": 9}, {"desc": "In this action, an attacker is -trying to create an key under HKCU/Software/Microsoft/Windows/CurrentVersion/RunOnce/ for persistence", "name": "主机命令行 - Create Registry Key Value Named \"Dummy\" For Persistence", "sim_actions": ["a9671cec-fd27-4946-b5cb-ff7acde8c1de"], "order": 10}, {"desc": "In this action, The Attacker is trying to secure persistence by creating a new registry key for Autorun.", "name": "主机命令行 - Create New Registry Key for Persistence", "sim_actions": ["f9b5054d-a0b8-4bca-b345-d2f7cb604796"], "order": 11}, {"desc": "In this action, a malware is trying to write a .vbs file to Startup folder.", "name": "主机命令行 - Write .Vbs File to Startup Folder", "sim_actions": ["7c489490-6df2-4a93-b95a-1c65b24fd7dd"], "order": 12}, {"desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and add to startup from registry.", "name": "主机命令行 - Write a File that Contains Ransom Note and Add to Startup", "sim_actions": ["b7ab8919-1320-4632-85b9-39220ff46c41"], "order": 13}, {"desc": "In this action, an attacker is trying to create registry keys \"Software\\Microsoft\\IEAK\\GroupPolicy\\PendingGPOs\" for persistence in the target environment. An INF file, referenced in the registry, can be used to execute custom commands after the system reboor.", "name": "主机命令行 - Create GroupPolicy Registry Keys for Persistence", "sim_actions": ["dfe1f7c7-03e0-418e-8c7f-4a9c9fe391b9"], "order": 14}, {"desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "name": "主机命令行 - Write a File that Contains Ransom Note and Open It Variant-1", "sim_actions": ["beff92a3-d7cd-4c23-9749-3bc7e3e8b095"], "order": 15}, {"desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "name": "主机命令行 - Write a File that Contains Ransom Note and Open It Variant-2", "sim_actions": ["62217b57-f702-48d3-a758-15d11b8b1453"], "order": 16}, {"desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "name": "主机命令行 - Write a File that Contains Ransom Note for Darkside Ransomware and Open It", "sim_actions": ["e43c4665-e562-4e92-b384-b67a022d1217"], "order": 17}, {"desc": "In this action, an attacker is trying to create a new key \"Saintbot\" under the \"\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\" hive.", "name": "主机命令行 - Create a New Registry Key in HKCU Hive Variant-3", "sim_actions": ["63515f43-e31f-46ab-8383-3e954969af02"], "order": 18}, {"desc": "In this action, the malware is trying to write a file in TMP to inform the victim, and open it using notepad.exe.", "name": "主机命令行 - Write a File that Contains Ransom Note for Sodinokibi Ransomware and Open It", "sim_actions": ["46b572a9-bd46-4a9b-b470-af9829803f0a"], "order": 19}, {"desc": "In this action, an attacker is trying to achieve persistence by adding new autorun hive as a key (New Value #1) for persistence.", "name": "主机命令行 - Create a new Registry Key for Autorun of cmd.exe", "sim_actions": ["1a229a9b-32b5-4478-a448-444251ae09fa"], "order": 20}, {"desc": "In this action, an attacker is trying to create a hidden registry key by using SharpHide Tool", "name": "主机命令行 - Execute SharpHide Tool", "sim_actions": ["8ccf3fcd-0bb5-49d4-b9d4-836f62a59c8e"], "order": 21}, {"desc": "In this action, an attacker is trying to create a Lnk File in Startup Folder.", "name": "主机命令行 - Create a Lnk File in Startup Folder by using SharPersist Tool", "sim_actions": ["82bc3034-199a-4ff1-b04c-8ec945e893a7"], "order": 22}, {"desc": "In this action, an attacker is trying to create a registry key in Hkcurun Hive.", "name": "主机命令行 - Create a Registry Key by using SharPersist Tool", "sim_actions": ["ab899b3d-9da1-430d-b112-4f6d807e811e"], "order": 23}, {"desc": "In this action, an attacker is trying to register the malicious gametime.dll as a new Time Provider via rundll32.exe.", "name": "主机命令行 - Register New Time Provider via Rundll32.exe", "sim_actions": ["4e48f67a-fe7a-41b8-b961-1cd55dc59808"], "order": 24}]}], "name": "Boot or Logon Autostart Execution Micro Emulation Plan - 1", "desc": "This threat involves a micro emulation plan for Mitre Technique T1547 \"Boot or Logon Autostart Execution\". In this technique, adversaries aim to achieve persistence and execution at system startup or user logon by leveraging mechanisms that automatically execute specific code or scripts. Boot or Logon Autostart Execution consists of techniques that manipulate the ways in which systems initiate processes during bootup or user login to run adversary-controlled code automatically. These techniques are critical for adversaries to maintain a foothold within a network because they ensure the malicious code executes regularly without further intervention. An adversary might configure a malicious script to run at system startup by adding it to the Windows Registry's Run keys or scheduling a task. Similarly, placing scripts or executables in specific directories that are automatically executed upon logon can achieve persistent access. The emulation of this technique helps in comprehensively understanding its mechanisms, identifying its indicators, and developing effective countermeasures to prevent unauthorized persistence and automatic execution attempts on systems. ", "emergency": false, "metadata_version": 1, "is_apt": false, "product": "base", "sequence_type": "sequences", "stop_on_error": true, "uuid": "9e74a228-0f0a-41f3-a353-86ec3b83df76", "vid": "", "ver": "", "remendation": 2, "protected_endpoint_rollback": "no", "tags": ["ATT&CK:Impact", "ATT&CK:T1547", "ATT&CK:T1491", "ATT&CK:T1547.001", "ATT&CK:T1547.009", "ATT&CK:Persistence", "ATT&CK:T1491.001"]}, {"profiles": [{"name": null, "steps": [{"desc": "\"net user\" adds or modifies user accounts, or displays user account information.", "name": "主机命令行 - List domain accounts using \"net user /domain\" command", "sim_actions": ["a66373fb-46ab-4256-99dd-60c9a38c324b"], "order": 0}, {"desc": "\"net user administrator\" command displays \"administrator\" user's information.", "name": "主机命令行 - Display Information of \"Administrator\" User via net.exe", "sim_actions": ["5f476cf8-c21b-4e5c-92f4-331977df3ef9"], "order": 1}, {"desc": "In this action, Domain user's info collected and saved a file.", "name": "主机命令行 - Find Domain Users and Save a File", "sim_actions": ["41a5135f-f319-4bcc-b60b-0950f466cb00"], "order": 2}, {"desc": "In this action, an attacker is trying to discover local active ports via Windows Sockets.", "name": "主机命令行 - Scan Local Ports via Windows Sockets\t", "sim_actions": ["253cd215-5431-45ab-9c1f-dc3df7c1e90a"], "order": 3}, {"desc": "In this action,  an attacker execute net account command to gather access detailed information about the password policy.", "name": "主机命令行 - Get Password Policy using Net Account Command", "sim_actions": ["e1ce0f7a-17ff-4db7-9e07-28a4b2921910"], "order": 4}, {"desc": "In this action, the net command is using to list the global groups on the Primary Domain Controller of the current domain.", "name": "主机命令行 - List the Global Groups on the Primary Domain Controller", "sim_actions": ["fae51b7e-c220-4ac8-b2d8-4cd8b248f9bf"], "order": 5}, {"desc": "In this action, an attacker is trying to list all processes that are currently running.", "name": "主机命令行 - List Currently Running Processes", "sim_actions": ["a85e9824-d8aa-4e5d-ac35-************"], "order": 6}, {"desc": "In this action, an attacker utilizes several common Windows command-line tools to enumerate resources.", "name": "主机命令行 - Gather System Information by using Command-line Tools", "sim_actions": ["554755f4-2c9c-4c19-800f-f0b0e41020b4"], "order": 7}, {"desc": "\"whoami\" displays user, group and privileges information for the user who is currently logged on to the local system.", "name": "主机命令行 - Display the current domain and user name using \"whoami\" command", "sim_actions": ["d3512b02-43fc-4e72-9753-9e1713d4155e"], "order": 8}, {"desc": "In this action, the Sc.exe command-line tool queries any Windows Service.", "name": "主机命令行 - Enumerate Services via SC Command", "sim_actions": ["b4d6f8c7-b425-4208-a682-36f4c236e569"], "order": 9}, {"desc": "In this action, the WMI component gets some information such as PointingDevice, BaseBoard, and DiskDrive.", "name": "主机命令行 - Discover System Hardware via WMI", "sim_actions": ["483e952d-4b29-4d41-b735-658e3d263801"], "order": 10}, {"desc": "In this action, The reg command retrieves the value of a registry key HKEY_CURRENT_USER\\Software\\Microsoft\\Terminal Server Client\\Default", "name": "主机命令行 - Query the \"Terminal Server Client\" Registry Key", "sim_actions": ["981e7229-f6a0-4ea3-855b-444d6791fa6e"], "order": 11}, {"desc": "In this action, Certutil is used to download a malicious file.", "name": "主机命令行 - Download a File using Certutil Tool", "sim_actions": ["67cfb443-f136-44f5-8eb6-4c27f4ada31c"], "order": 12}, {"desc": "In this action, PowerShell command Base64 decodes a string.", "name": "主机命令行 - Execute the Base64 Decode PowerShell Command", "sim_actions": ["d1c4eb2b-9fae-42d8-91ff-68ef3616a977"], "order": 13}, {"desc": "In this action, Certutil is used to encode/decode a malicious file.", "name": "主机命令行 - Encode and Decode a Text File using Certutil Tool", "sim_actions": ["b55ed43c-8541-4a23-a43a-c84d02c5a9c3"], "order": 14}, {"desc": "In this action, an attacker lists cached credentials using cmdkey command.", "name": "主机命令行 - List Cached Credentials using Cmdkey Command", "sim_actions": ["434cf3e8-8cf0-4e2e-ad0b-e109b7ce07be"], "order": 15}, {"desc": "Mimikatz is a credential dumper tool capable of obtaining plaintext Windows account logins and passwords, along with many other features that make it useful for testing the security of networks.", "name": "主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) <PERSON>l", "sim_actions": ["59c709cd-92e6-4270-a50b-76d4f8bc358b"], "order": 16}, {"desc": "In this action, an attacker can copies \".docx\" files from one location to another by using PowerShell.", "name": "主机命令行 - Copy \".docx\" Files Using PowerShell", "sim_actions": ["e2d3dbad-d8f9-4089-bb66-224785a926f0"], "order": 17}, {"desc": "In this action, The PowerShell script captures user input using SetWindowsHookExA API.", "name": "主机命令行 - Capture User Input using Powershell Script", "sim_actions": ["9a2af110-0b63-42f0-ab25-bc1910e4785d"], "order": 18}, {"desc": "In this action, an attacker is trying to capture screenshots from the target system by using a .NET binary.", "name": "主机命令行 - Capture Screenshot via .NET Binary", "sim_actions": ["5cc2ffc1-dceb-4541-8a72-796c2ce2129f"], "order": 19}, {"desc": "In this action, an attacker is trying to delete all system event logs by PowerShell.", "name": "主机命令行 - Delete All System Event Logs via PowerShell", "sim_actions": ["0f539245-f3e1-4b2d-b4cd-1e9145996d73"], "order": 20}, {"desc": "In this action, a scheduled task called \"InetlSecurityAssistManager\" is created and the scheduled task contains Wscript commands \"wscript %TMP%\\test.vbs\".", "name": "主机命令行 - Create a scheduled task \"InetlSecurityAssistManager\" using schtasks command", "sim_actions": ["ca4d2457-f8bf-4ff2-a091-90aa32ecb502"], "order": 21}]}], "name": "OilRig Threat Group Campaign", "desc": "OilRig is a threat group with suspected Iranian origins that has targeted Middle Eastern and international victims since at least 2014. The group has targeted a variety of industries, including financial, government, energy, chemical, and telecommunications, and has largely focused its operations within the Middle East. It appears the group carries out supply chain attacks, leveraging the trust relationship between organizations to attack their primary targets.\n", "emergency": false, "metadata_version": 1, "is_apt": false, "product": "base", "sequence_type": "sequences", "stop_on_error": true, "uuid": "c798887b-d62a-47b8-9c17-66569d99f0c7", "vid": "", "ver": "", "remendation": 2, "protected_endpoint_rollback": "no", "tags": ["ATT&CK:Collection", "ATT&CK:T1201", "ATT&CK:Command and Control", "ATT&CK:T1140", "ATT&CK:T1033", "ATT&CK:T1087.002", "ATT&CK:T1016", "ATT&CK:T1056", "ATT&CK:T1113", "ATT&CK:T1087.001", "ATT&CK:T1087", "ATT&CK:T1070.001", "ATT&CK:T1119", "ATT&CK:T1057", "ATT&CK:T1105", "ATT&CK:Credential Access", "ATT&CK:T1059", "ATT&CK:T1053.005", "ATT&CK:T1078.003", "ATT&CK:Discovery", "ATT&CK:T1047", "ATT&CK:T1007", "ATT&CK:T1003.001", "ATT&CK:T1069", "ATT&CK:T1012", "ATT&CK:T1049", "ATT&CK:Persistence", "ATT&CK:Defense Evasion", "ATT&CK:Execution"]}]}