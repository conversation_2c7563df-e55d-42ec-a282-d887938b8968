{"action": {"name": "(自定义规则)主机命令行 - Gather Information from Browsers using SharpChromium - 2", "desc": "In this action, an attacker is trying to gather information like cookies, history and saved Logins from Chromium based browsers by using SharpChromium.[black_basta_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "SharpChromium.exe", "file_owner": "system", "file_transfer_library": 998002285, "randomize_file": null, "file_name": "SharpChromium.exe"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharpChromium.exe\" all", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1217"], "mitre_mitigation": []}}, "picus_id": 10571, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Credential Dumping from Browser Password Stores", "ruleId": "5537", "description": "Detects suspicious processes based on name and location that access the browser credential stores. This technique is commonly utilized for credential access.", "releaseDate": 1630022400000, "updateDate": 1665360000000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Credential Dumping from Browser Password Stores\nstatus: experimental\ndescription: Detects suspicious processes based on name and location that access the browser credential stores. This technique is commonly utilized for credential access.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0006/\n   - https://attack.mitre.org/techniques/T1555/003/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Removable Storage'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit File System'\n    definition3: 'You should enable System Access Control Lists (SACL) for registry value which you want to monitor or track event changes.'\ndetection:\n    selection:\n        EventID: 4656\n        ObjectType: File\n        ObjectName|contains:\n            - '\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Network\\Cookies'\n            - '\\Appdata\\Local\\Chrome\\User Data\\Default\\Login Data'\n            - '\\AppData\\Local\\Google\\Chrome\\User Data\\Local State'\n            - '\\Appdata\\Local\\Microsoft\\Windows\\WebCache\\WebCacheV01.dat'\n            - '\\cookies.sqlite'\n            - 'release\\key3.db'\n            - 'release\\key4.db'\n            - 'release\\logins.json'\n    filter:\n        ProcessName|endswith:\n            - '\\firefox.exe'\n            - '\\chrome.exe'\n            - '\\MsMpEng.exe'\n    condition: selection and not filter\nfalsepositives:\n    - Legitimate Windows processes\nlevel: medium\ntags:\n    - attack.credential_access\n    - attack.ta0006\n    - attack.t1555.003", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Removable Storage", "Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit File System", "You should enable System Access Control Lists (SACL) for registry value which you want to monitor or track event changes."]}, "falsePositives": ["Legitimate Windows processes"], "mitre": [{"name": "Credentials from Web Browsers", "mitreId": "T1555.003", "url": "https://attack.mitre.org/techniques/T1555/003/", "type": "sub_technique"}]}]}, "uuid": "99e0e008-e045-47fa-b80c-6363175e9d4a", "sigma_device_query": "title: Credential Dumping from Browser Password Stores\nstatus: experimental\ndescription: Detects suspicious processes based on name and location that access the browser credential stores. This technique is commonly utilized for credential access.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0006/\n   - https://attack.mitre.org/techniques/T1555/003/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit Removable Storage'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Policies\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Object Access\\Audit File System'\n    definition3: 'You should enable System Access Control Lists (SACL) for registry value which you want to monitor or track event changes.'\ndetection:\n    selection:\n        EventID: 4656\n        ObjectType: File\n        ObjectName|contains:\n            - '\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Network\\Cookies'\n            - '\\Appdata\\Local\\Chrome\\User Data\\Default\\Login Data'\n            - '\\AppData\\Local\\Google\\Chrome\\User Data\\Local State'\n            - '\\Appdata\\Local\\Microsoft\\Windows\\WebCache\\WebCacheV01.dat'\n            - '\\cookies.sqlite'\n            - 'release\\key3.db'\n            - 'release\\key4.db'\n            - 'release\\logins.json'\n    filter:\n        ProcessName|endswith:\n            - '\\firefox.exe'\n            - '\\chrome.exe'\n            - '\\MsMpEng.exe'\n    condition: selection and not filter\nfalsepositives:\n    - Legitimate Windows processes\nlevel: medium\ntags:\n    - attack.credential_access\n    - attack.ta0006\n    - attack.t1555.003"}