{"action": {"name": "(自定义规则)主机命令行 - Clear All Event Logs Variant-1 - 8", "desc": "In this action, an attacker is trying to clear all event logs in the victim system.[apt29_group_campaign_2020]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c FOR %N IN (APPLICATION SECURITY SETUP SYSTEM) DO wevtutil.exe cl %N", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Failed\\ to\\ clear", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1070.001"], "mitre_mitigation": ["ATT&CK:M1022", "ATT&CK:M1041", "ATT&CK:M1029"]}}, "picus_id": 48, "uuid": "a8d23842-336b-46ef-8f82-a1efa5e1a66b", "sigma_device_query": "title: Defense Evasion by Windows Security Event Log Deletion\nstatus: stable\ndescription: Detects deletion of Windows Security Event Logs. This technique is commonly utilized for defense evasion as APT32 threat group's usage in its campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1070/\n   - https://attack.mitre.org/tactics/TA0005/\n   - https://attack.mitre.org/groups/G0050/\nlogsource:\n    product: windows\n    service: security\ndetection:\n    selection:\n        EventID: 1102\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: high\ntags:\n    - attack.defense_evasion\n    - attack.t1070\n    - attack.ta0005\n    - attack.g0050"}