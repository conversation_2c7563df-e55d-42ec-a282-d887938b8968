{"action": {"name": "(自定义规则)主机命令行 - Gather .Pgp Private Key using dir and findstr commands - 9", "desc": "In this action, an attacker can get sensitive data such as private cryptographic keys, certificates, digital signatures using dir and findstr commands. This private key includes \".pgp\" extension.[beagleboyz_threat_group_fastcash_2.0_campaign_2020]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c dir \"c:\\\" /b /s .pgp | findstr /e .pgp", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Credential Access", "ATT&CK:T1552"], "mitre_mitigation": ["ATT&CK:M1037", "ATT&CK:M1026", "ATT&CK:M1051", "ATT&CK:M1041", "ATT&CK:M1015", "ATT&CK:M1022", "ATT&CK:M1035", "ATT&CK:M1028", "ATT&CK:M1047", "ATT&CK:M1017", "ATT&CK:M1027"]}}, "picus_id": 43, "sigma_device_query": "title: Private Keys and Certificate Files Discovery\nstatus: experimental\ndescription: Detects the attempt to search private keys and certificate files via dir and findstr command. This method is used for authenticating to Remote Services like SSH or decrypting other collected files such as email.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1552/004/\n   - https://attack.mitre.org/tactics/TA0006/\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/findstr\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection1:\n        EventID: 4688\n        NewProcessName: '*\\findstr.exe'\n        ProcessCommandLine: \n            - '*.key*'\n            - '*.pgp*'\n            - '*.cer*'\n    selection2:\n        EventID: 4688\n        ProcessCommandLine: \n            - '*Dir *.key*'\n            - '*Dir *.pgp*'\n            - '*Dir *.cer*'\n    condition: 1 of them\nfalsepositives:\n    - Administrative tools/scripts\nlevel: high\ntags:\n    - attack.credential_access\n    - attack.ta0006 \n    - attack.t1552.004", "uuid": "ee4d377e-a000-4c3f-81a6-c1464fd61991"}