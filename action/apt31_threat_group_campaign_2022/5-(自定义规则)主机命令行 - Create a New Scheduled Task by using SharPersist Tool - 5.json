{"action": {"name": "(自定义规则)主机命令行 - Create a New Scheduled Task by using SharPersist Tool - 5", "desc": "In this action, the attacker is trying to create a new scheduled task.[apt31_threat_group_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "SharPersist_v2.exe", "file_owner": "system", "file_transfer_library": 998002035, "randomize_file": null, "file_name": "SharPersist_v2.exe"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Microsoft.Win32.TaskScheduler.dll", "file_owner": "system", "file_transfer_library": 998002036, "randomize_file": null, "file_name": "Microsoft.Win32.TaskScheduler.dll"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Microsoft.Win32.dll", "file_owner": "system", "file_transfer_library": 998002037, "randomize_file": null, "file_name": "Microsoft.Win32.dll"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"\"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\SharPersist_v2.exe\" -t \"schtask\" -c \"C:\\Windows\\System32\\cmd.exe\" -a \"/c echo 123 >> c:\\123.txt\" -n \"Some Task\" -m \"add\" -o \"hourly\"\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "SUCCESS:", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1543"], "mitre_mitigation": ["ATT&CK:M1022", "ATT&CK:M1040", "ATT&CK:M1047", "ATT&CK:M1018", "ATT&CK:M1028", "ATT&CK:M1033", "ATT&CK:M1045"]}}, "picus_id": 22875, "uuid": "4bf56d31-2877-4924-94d6-48041348f520", "sigma_device_query": ""}