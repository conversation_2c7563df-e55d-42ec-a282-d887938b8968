{"action": {"name": "(自定义规则)主机命令行 - Enable WDigest Authentication via Registry - 7", "desc": "In this action, an attacker is trying to enable WDigest Authentication via registry to enforce the storage of credentials in plaintext on future logins.[blackcat_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 5, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "cmd.exe /c reg.exe add HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecurityProviders\\WDigest /v UseLogonCredential /t REG_DWORD /d 1 /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1112"], "mitre_mitigation": ["ATT&CK:M1024"]}}, "picus_id": 22119, "sigma": {"name": "Sigma", "rules": [{"ruleName": "WDigest Registry Key Modification", "ruleId": "3772", "description": "Detects the enablement of WDigest-UseLogonCredential protocol in registry. WDigest procotol is disabled by default but adversaries enable this protocol to gather clear-text credentials from LSASS.", "releaseDate": 1578268800000, "updateDate": 1647216000000, "author": "Picus Security", "severity": "critical", "reportScore": 0, "query": "title: WDigest Registry Key Modification\nstatus: stable\ndescription: Detects the enablement of WDigest-UseLogonCredential protocol in registry. WDigest procotol is disabled by default but adversaries enable this protocol to gather clear-text credentials from LSASS.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1003/\n   - https://attack.mitre.org/tactics/TA0006/\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 13 \n        TargetObject: '*\\Control\\SecurityProviders\\WDigest\\UseLogonCredential'\n        EventType: 'SetValue'\n        Details: 'DWORD (0x00000001)'\n    condition: selection\nfalsepositives:\n    - None\nlevel: critical\ntags:\n    - attack.credential_access\n    - attack.t1003\n    - attack.ta0006", "logSource": {"productName": "windows", "service": "sysmon", "policies": null}, "falsePositives": ["None"], "mitre": [{"name": "OS Credential Dumping", "mitreId": "T1003", "url": "https://attack.mitre.org/techniques/T1003/", "type": "technique"}]}]}, "uuid": "4e7dac75-262b-4b1f-b365-59b096f68541", "sigma_device_query": ""}