{"action": {"name": "(自定义规则)主机命令行 - Attach ISO Disk for Evasion - 1", "desc": "In this action, The Attacker is trying to execute custom EXE by attaching a virtual disk file.[apt29_dropping_brute_ratel_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "VirtualMal.exe", "file_owner": "system", "file_transfer_library": 998002058, "randomize_file": null, "file_name": "VirtualMal.exe"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "VirtualMal.iso", "file_owner": "system", "file_transfer_library": 998002059, "randomize_file": null, "file_name": "VirtualMal.iso"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"\"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\VirtualMal.exe\" && timeout 3 && tasklist /fi \"imagename eq notepad.exe\"\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1202"], "mitre_mitigation": []}}, "picus_id": 2677, "uuid": "3c3429b2-2065-4357-9d9d-002423795f71", "sigma_device_query": "title: Suspicious Process Execution via Mounted Disk\nstatus: experimental\ndescription: Detects the attempt to execute suspicious process with mounted disk. Adversaries use this method to subvert detection controls and bypass security restrictions.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1202/\n   - https://attack.mitre.org/tactics/TA0005/\nlogsource:\n    product: windows\n    service: sysmon\ndetection:\n    selection:\n        EventID: 1\n        ParentImage|contains: 'volume'\n        ParentCommandLine|contains: 'cdrom'\n    condition: selection\nfalsepositives:\n    - Unknown\nlevel: high\ntags:\n    - attack.defense_evasion\n    - attack.t1202\n    - attack.ta0005"}