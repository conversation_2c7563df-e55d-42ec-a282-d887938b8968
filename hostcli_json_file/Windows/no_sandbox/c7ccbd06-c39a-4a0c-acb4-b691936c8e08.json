{"actions": {"c7ccbd06-c39a-4a0c-acb4-b691936c8e08": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "ver": 16, "vid": "370359", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 执行 Invoke-BadPotato 函数", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图执行 Invoke-BadPotato。", "created_at": "2025-07-21 21:40:20", "updated_at": "2025-07-21 21:40:20", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "ics_mitre_tactics", "tag": "TA0111 - 权限提升", "tag_en": "TA0111 - Privilege Escalation", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "ics_mitre_techniques", "tag": "T0890 - 漏洞利用实现权限提升", "tag_en": "T0890 - Exploitation for Privilege Escalation", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_mitigation", "tag": "M1019", "tag_en": "M1019", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_mitigation", "tag": "M1038", "tag_en": "M1038", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_mitigation", "tag": "M1048", "tag_en": "M1048", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_mitigation", "tag": "M1050", "tag_en": "M1050", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_mitigation", "tag": "M1051", "tag_en": "M1051", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_tactics", "tag": "TA0004 - 特权提升", "tag_en": "TA0004 - Privilege Escalation", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "mitre_techniques", "tag": "T1068 - 特权提升利用", "tag_en": "T1068 - Exploitation for Privilege Escalation", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "c7ccbd06-c39a-4a0c-acb4-b691936c8e08", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_tactics TA0004 - 特权提升", "mitre_techniques T1068 - 特权提升利用", "control HIDS", "mitre_mitigation ATT&CK:M1048", "mitre_mitigation ATT&CK:M1019", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "os Windows:Server 2022", "os Windows:11", "os Windows:Server 2019", "os Windows:10", "mitre_mitigation ATT&CK:M1051", "control EDR/XDR", "mitre_mitigation ATT&CK:M1050", "mitre_mitigation ATT&CK:M1038", "run_as Admin", "os Windows:Server 2016", "control AV", "run_as User"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "Invoke-BadPotato.ps1", "file_name": "Invoke-BadPotato.ps1", "file_owner": "system", "file_transfer_library": 999057726, "file_type": "ps1", "filesize": 79014, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "cad3073a272fc3f5692f0b44634e1513", "sha1sum": "d4527e61a2ea10ca1a4882fa8d388538bc559659", "sha256sum": "58e46faa8c606bb0e7068586e198327669740478fdbbb0713443c0b07590973c"}], "monitor_connections": "off", "raw_text": "whoami\r\npowershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"\r\npowershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-BadPotato.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-BadPotato.ps1'; Invoke-BadPotato\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "match", "success_match": "Unrestricted", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-BadPotato.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\Invoke-BadPotato.ps1'; Invoke-BadPotato\"", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "match", "success_match": "nt\\ authority\\\\system", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}