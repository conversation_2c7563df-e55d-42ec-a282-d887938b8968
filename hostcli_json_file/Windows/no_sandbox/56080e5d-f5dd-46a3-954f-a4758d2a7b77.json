{"actions": {"56080e5d-f5dd-46a3-954f-a4758d2a7b77": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "ver": 16, "vid": "370361", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 执行 Tamperetw 工具", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图执行 TamperETW 工具来向进程监控工具隐藏 .NET 程序集。", "created_at": "2025-07-21 21:42:15", "updated_at": "2025-07-21 21:42:15", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "56080e5d-f5dd-46a3-954f-a4758d2a7b77", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2022", "os Windows:Server 2016", "run_as SYSTEM", "run_as User", "os Windows:11", "mitre_techniques T1056 - 输入捕获", "control EDR/XDR", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "mitre_tactics TA0009 - 信息收集", "control AV", "os Windows:Server 2019", "os Windows:10", "control HIDS"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "TamperETW.exe", "file_name": "TamperETW.exe", "file_owner": "system", "file_transfer_library": 999057722, "file_type": "exe", "filesize": 583168, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "e0d599d31ff2d8e8b769e7768cb55b87", "sha1sum": "354942ee4286dd67130ac6494795f894b9f656a7", "sha256sum": "f16ea449052e884580898f8f1342a529851ec6ef5ef6dd87a8cb7c217840fd17"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "ManagedDLL.dll", "file_name": "ManagedDLL.dll", "file_owner": "system", "file_transfer_library": 999057723, "file_type": "dll", "filesize": 4096, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "4b761732f9f252c8eaba9d5a93b7e04d", "sha1sum": "ca085f97407767d709a717f237b94a0566e27b19", "sha256sum": "3455450fb9b5e9fd9d623a9d68a3fdbe44e8a6b21f67df28cb419da46890a9b2"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\TamperETW.exe\r\ntasklist /svc | findstr /i calc\r\ntaskkill.exe /f /im calculator.exe || taskkill.exe /f /im win32calc.exe || taskkill.exe /f /im calc.exe || taskkill.exe /f /im calculatorapp.exe \r\ndel \"%TMP%\\ManagedDLL.dll\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\TamperETW.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "\\[\\+\\]\\ Done", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i calc", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im calculator.exe || taskkill.exe /f /im win32calc.exe || taskkill.exe /f /im calc.exe || taskkill.exe /f /im calculatorapp.exe ", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%TMP%\\ManagedDLL.dll\"", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}