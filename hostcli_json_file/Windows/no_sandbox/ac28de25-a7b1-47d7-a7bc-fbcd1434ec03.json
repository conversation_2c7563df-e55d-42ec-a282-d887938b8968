{"actions": {"ac28de25-a7b1-47d7-a7bc-fbcd1434ec03": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "ver": 16, "vid": "370355", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过 Early Bird APC 队列技术将 Shellcode 注入“runonce.exe”进程", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试通过 Early Bird APC Queue Technique 将 shellcode 注入“runonce.exe”进程来执行该 shellcode。", "created_at": "2025-07-21 21:41:41", "updated_at": "2025-07-21 21:41:41", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "mitre_mitigation", "tag": "M1040", "tag_en": "M1040", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "mitre_techniques", "tag": "T1055.001 - 动态链接库注入", "tag_en": "T1055.001 - Dynamic-link Library Injection", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "ac28de25-a7b1-47d7-a7bc-fbcd1434ec03", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2019", "os Windows:10", "control AV", "control HIDS", "mitre_mitigation ATT&CK:M1040", "run_as Admin", "os Windows:Server 2022", "control EDR/XDR", "os Windows:Server 2016", "mitre_tactics TA0005 - 防御绕过", "mitre_techniques T1055.001 - 动态链接库注入", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as SYSTEM", "run_as User", "os Windows:11"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "runonceInjection.exe", "file_name": "RunonceInjection.exe", "file_owner": "system", "file_transfer_library": 999057729, "file_type": "exe", "filesize": 112640, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "5b4ec8a5d9c3eb186ecec25390a1b9ab", "sha1sum": "cc5cc801a68d268286dfb3e189537988eede8b78", "sha256sum": "70de71ccacd956264137c5d7408ea98ffdceddb24ec6e9ecd0b4a395119452ca"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\runonceInjection.exe\r\ntasklist /svc | findstr /i winver.exe\r\ntaskkill.exe /f /im winver.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\runonceInjection.exe", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i winver.exe", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im winver.exe", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}