{"action": {"name": "(自定义规则)主机命令行 - Capture ScreenShot using PsTools - 13", "desc": "In this action, The attacker is trying to capture screenshot of victim's machine using PsTools[apt29_group_campaign_2020]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "psversion.ps1", "file_owner": "system", "file_transfer_library": 998002028, "randomize_file": null, "file_name": "psversion.ps1"}], "steps": [{"step_order": 1, "command": "powershell.exe -c Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Import-Module 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\psversion.ps1'; Invoke-ScreenCapture; Start-Sleep -Seconds 3; View-Job -JobName 'Screenshot'", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Collection", "ATT&CK:T1113"], "mitre_mitigation": []}}, "picus_id": 56, "uuid": "1a8a5c52-60ce-47d8-b2a8-eb6fc8d6548a", "sigma_device_query": "title: Screen Capture via PowerShell\nstatus: experimental\ndescription: Detects the attempt to take screen shot via PowerShell script. Adversaries aim to gather information over the course of an operation.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1113/\n    - https://attack.mitre.org/tactics/TA0009/\n    - https://learn.microsoft.com/en-us/dotnet/api/system.drawing.graphics.copyfromscreen?view=netframework-4.8\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID: 4104\n        Message|contains|all:\n            - 'Drawing.Graphics'\n            - 'CopyFromScreen'\n    condition: selection\nfalsepositives: \n    - Penetration testing\nlevel: high\ntags:\n    - attack.collection\n    - attack.t1113\n    - attack.ta0009"}