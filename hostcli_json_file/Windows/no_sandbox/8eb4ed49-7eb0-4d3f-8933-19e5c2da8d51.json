{"actions": {"8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "ver": 16, "vid": "370366", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 执行 NetRipper Post Exploitation 工具", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图执行 NetRipper 后利用工具。", "created_at": "2025-07-21 21:42:02", "updated_at": "2025-07-21 21:42:02", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "8eb4ed49-7eb0-4d3f-8933-19e5c2da8d51", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "mitre_techniques T1056 - 输入捕获", "control AV", "os Windows:10", "mitre_tactics TA0009 - 信息收集", "control EDR/XDR", "control HIDS", "os Windows:Server 2022", "os Windows:11", "run_as User", "os Windows:Server 2019", "os Windows:Server 2016", "run_as SYSTEM"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "NetRipper.x64.exe", "file_name": "NetRipper.x64.exe", "file_owner": "system", "file_transfer_library": 999057714, "file_type": "exe", "filesize": 289280, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "0415723ea8cad95197c55b51575e8e2e", "sha1sum": "28c874ad3cccc6461e0684bf54745be1dd12a5bf", "sha256sum": "6eeacb615ef8da0045b50c3e9eaaea84486ad3d63b51682aeaeea1905bc97f2b"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "DLL.x64.dll", "file_name": "DLL.x64.dll", "file_owner": "system", "file_transfer_library": 999057715, "file_type": "dll", "filesize": 354304, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "fbd334fbd825ad3e63e79f86833ceeb1", "sha1sum": "301bcb0624b23e5c5b9e61ac498a58c19833010b", "sha256sum": "adaf49aec86af4d3280790ea58ce3d1fc0f6ea5ace6bc834d1be97a95c4de931"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\NetRipper.x64.exe -w \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\DLL.x64.dll\" -l TEMP -p true -d 4096 -s user,pass\r\ndel \"%TMP%\\ConfiguredDLL.dll\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\NetRipper.x64.exe -w \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\DLL.x64.dll\" -l TEMP -p true -d 4096 -s user,pass", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "succesfully", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del \"%TMP%\\ConfiguredDLL.dll\"", "prompt": "auto", "sleep": 0, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}