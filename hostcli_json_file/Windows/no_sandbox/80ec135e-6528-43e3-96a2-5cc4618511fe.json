{"actions": {"80ec135e-6528-43e3-96a2-5cc4618511fe": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "ver": 16, "vid": "370357", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 创建新服务“windowsinspectionupdate”", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了使用 sc.exe 创建名为“windowsinspectionupdate”的服务", "created_at": "2025-07-21 21:41:45", "updated_at": "2025-07-21 21:41:45", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "mitre_mitigation", "tag": "M1022", "tag_en": "M1022", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "mitre_mitigation", "tag": "M1040", "tag_en": "M1040", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "mitre_techniques", "tag": "T1569.002 - 服务执行", "tag_en": "T1569.002 - Service Execution", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "80ec135e-6528-43e3-96a2-5cc4618511fe", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2022", "os Windows:Server 2019", "control AV", "mitre_mitigation ATT&CK:M1026", "control EDR/XDR", "mitre_mitigation ATT&CK:M1022", "os Windows:Server 2016", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as Admin", "run_as User", "os Windows:11", "os Windows:10", "mitre_techniques T1569.002 - 服务执行", "control HIDS", "mitre_mitigation ATT&CK:M1040", "run_as SYSTEM"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "sc.exe create windowsinspectionupdate binpath= \"cmd /c start c:/windows/temp/tmp/lld.exe c:/windows/temp/tmp/tmp.log\"\r\nsc.exe description windowsinspectionupdate “windows inspection integrity”\r\nsc.exe config windowsinspectionupdate start= auto\r\nsc.exe delete windowsinspectionupdate\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "sc.exe create windowsinspectionupdate binpath= \"cmd /c start c:/windows/temp/tmp/lld.exe c:/windows/temp/tmp/tmp.log\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "sc.exe description windowsinspectionupdate “windows inspection integrity”", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "sc.exe config windowsinspectionupdate start= auto", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "sc.exe delete windowsinspectionupdate", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}