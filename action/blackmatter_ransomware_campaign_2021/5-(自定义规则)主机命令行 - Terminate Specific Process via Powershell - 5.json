{"action": {"name": "(自定义规则)主机命令行 - Terminate Specific Process via Powershell - 5", "desc": "In this action, an attacker is trying to terminate internet explorer process via powershell.[blackmatter_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -c \"$mypid=get-process iexplore |select -expand id;  Stop-Process -Id $mypid -Force\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Impact", "ATT&CK:T1489"], "mitre_mitigation": ["ATT&CK:M1030", "ATT&CK:M1024", "ATT&CK:M1018", "ATT&CK:M1022"]}}, "picus_id": 10582, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Process Termination via PowerShell", "ruleId": "3918", "description": "Detects the attempt to terminate processes via Stop-Process cmdlet of PowerShell. This method is utilized by malwares to stop specific processes of services which are mostly related to protect the system.", "releaseDate": 1598918400000, "updateDate": 1635984000000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Process Termination via PowerShell\nstatus: stable\ndescription: Detects the attempt to terminate processes via Stop-Process cmdlet of PowerShell. This method is utilized by malwares to stop specific processes of services which are mostly related to protect the system.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1489/\n    - https://attack.mitre.org/tactics/TA0040/\n    - https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.management/stop-service?view=powershell-7\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging'\ndetection:\n    selection:\n        EventID:\n            - 4103\n            - 4104\n        Message|contains: 'Stop-Process'\n    condition: selection\nfalsepositives:\n    - Administrative tools/scripts\nlevel: medium\ntags:\n    - attack.impact\n    - attack.t1489\n    - attack.ta0040", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging", "Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On PowerShell Script Block Logging"]}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "Service Stop", "mitreId": "T1489", "url": "https://attack.mitre.org/techniques/T1489/", "type": "technique"}]}]}, "uuid": "d7247dc3-c374-426e-aa18-5eb2095fc737"}