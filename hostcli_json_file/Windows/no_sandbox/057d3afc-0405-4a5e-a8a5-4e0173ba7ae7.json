{"actions": {"057d3afc-0405-4a5e-a8a5-4e0173ba7ae7": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "ver": 16, "vid": "382879", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 GetAsyncKeyState()执行键盘记录器，变种 #2", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：监测可疑命令执行；针对使用到的文件在终端/主机安全产品中基于文件Hash添加本地黑名单策略。中长期建议：如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者尝试运行使用 GetAsyncKeyState() API 调用的键盘记录器。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:10", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [{"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "source": "Trellix Endpoint Security (HX)", "message": "Gen:<PERSON><PERSON><PERSON><PERSON>Tedy.324639", "source_identifier": "", "cn_show": false, "product_version": "", "signature_version": ""}], "tags": [{"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "ics_mitre_tactics", "tag": "TA0111 - 权限提升", "tag_en": "TA0111 - Privilege Escalation", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "ics_mitre_techniques", "tag": "T0874 - 挂钩", "tag_en": "T0874 - Hook<PERSON>", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "mitre_tactics", "tag": "TA0006 - 凭据访问", "tag_en": "TA0006 - Credential Access", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "mitre_techniques", "tag": "T1056.001 - 键盘记录", "tag_en": "T1056.001 - Keylogging", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "057d3afc-0405-4a5e-a8a5-4e0173ba7ae7", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": [], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1056.001", "ATT&CK:Credential Access"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "getasynckeystate.exe", "file_name": "getasynckeystate.exe", "file_owner": "system", "file_transfer_library": 999050821, "file_type": "exe", "filesize": 120320, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "4fc551ef74f3388428b38e32c96a95c7", "sha1sum": "1210c96ef49d1850a1b4cdbd3591165105cadf28", "sha256sum": "26ab33d5e7e0fbf5603c491d6bc603bae997fef8c9fe578239c18dce5ee7e542"}], "monitor_connections": "off", "raw_text": "start /B C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\getasynckeystate.exe > C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\getasynckeystate.txt \r\ntaskkill /IM getasynckeystate.exe /F & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "start /B C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\getasynckeystate.exe > C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\getasynckeystate.txt", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 180}, {"check_events": true, "cleanup": true, "command": "taskkill /IM getasynckeystate.exe /F & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}