{"actions": {"219412ce-f416-4028-8ee7-8c1f0fd71a96": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "ver": 16, "vid": "382465", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用“net user”命令将来宾用户添加为本地管理员用户", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强用户在系统内安装、卸载、执行软件、修改注册表等权限控制。中长期建议：增加多因素认证产品、特权帐号管理产品以加强对身份信息系统的监测与保护；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图使用“net localgroup Administrators guest /add”命令添加具有本地管理员权限的“guest”用户。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:40:50", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "mitre_mitigation", "tag": "M1032", "tag_en": "M1032", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "mitre_tactics", "tag": "TA0003 - 持久化", "tag_en": "TA0003 - Persistence", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "mitre_techniques", "tag": "T1136.001 - 本地账户", "tag_en": "T1136.001 - Local Account", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "219412ce-f416-4028-8ee7-8c1f0fd71a96", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1026", "ATT&CK:M1032"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1136.001"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "net.exe localgroup administrators guest /add\r\nnet.exe localgroup administrators guest /delete\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "net.exe localgroup administrators guest /add", "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "net.exe localgroup administrators guest /delete", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}