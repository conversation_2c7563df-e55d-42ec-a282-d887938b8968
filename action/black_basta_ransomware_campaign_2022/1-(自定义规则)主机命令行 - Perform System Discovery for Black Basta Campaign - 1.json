{"action": {"name": "(自定义规则)主机命令行 - Perform System Discovery for Black Basta Campaign - 1", "desc": "In this action, an attacker is trying to mimic the discovery steps of the Black Basta ransomware group.[black_basta_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "arp -a", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "ipconfig.exe /all", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "getmac.exe ", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "route PRINT", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 5, "command": "netstat -nao", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 6, "command": "net.exe localgroup", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 7, "command": "whoami.exe /all", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 8, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1082"], "mitre_mitigation": []}}, "picus_id": 23395, "sigma": {"name": "Sigma", "rules": [{"ruleName": "System Information Discovery via Native Tools", "ruleId": "7266", "description": "Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.", "releaseDate": 1670284800000, "updateDate": 1681430400000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: System Information Discovery via Native Tools\nstatus: experimental\ndescription: Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/techniques/T1016/\n   - https://attack.mitre.org/techniques/T1033/\n   - https://attack.mitre.org/techniques/T1049/\n   - https://attack.mitre.org/techniques/T1087/\n   - https://attack.mitre.org/techniques/T1082/\n   - https://attack.mitre.org/software/S0099/\n   - https://attack.mitre.org/software/S0100/\n   - https://attack.mitre.org/software/S0103/\n   - https://attack.mitre.org/software/S0039/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/arp\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/whoami\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/ipconfig\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/route_ws2008\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/netstat\n   - https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-r2-and-2012/cc725622(v=ws.11)\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/getmac\nlogsource:\n    product: windows\n    service: security\n    definition: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName|endswith: \n                - '\\whoami.exe'\n                - '\\arp.exe'\n                - '\\ipconfig.exe'\n                - '\\route.exe'\n                - '\\netstat.exe'\n                - '\\net.exe' \n                - '\\getmac.exe'\n                - '\\nbtstat.exe'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.ta0007\n    - attack.t1016\n    - attack.t1033\n    - attack.t1049\n    - attack.t1087\n    - attack.t1082", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}, {"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}, {"name": "System Network Connections Discovery", "mitreId": "T1049", "url": "https://attack.mitre.org/techniques/T1049/", "type": "technique"}, {"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}, {"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}]}, {"ruleName": "System Network Configuration Discovery by Obtaining ARP Cache", "ruleId": "8517", "description": "Detects the attempt to gather current arp cache tables via \"/a\" and \"-a\" parameters of arp tool. This technique is commonly utilized for discovery.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: System Network Configuration Discovery by Obtaining ARP Cache\nstatus: experimental\ndescription: Detects the attempt to gather current arp cache tables via \"/a\" and \"-a\" parameters of arp tool. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1016/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/arp\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\arp.exe'\n        ProcessCommandLine: \n            - '*arp* -a'\n            - '*arp* /a'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1016\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}]}, {"ruleName": "Network Connections Discovery via Netstat Command", "ruleId": "7318", "description": "Detects the attempt to display active TCP connections via netstat command. This technique is commonly utilized for execution and discovery as Turla, menuPass and APT1 Threat Group's usage in its threat campaigns.", "releaseDate": 1600732800000, "updateDate": 1600732800000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Network Connections Discovery via Netstat Command\nstatus: experimental\ndescription: Detects the attempt to display active TCP connections via netstat command. This technique is commonly utilized for execution and discovery as Turla, menuPass and APT1 Threat Group's usage in its threat campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1049/\n   - https://attack.mitre.org/techniques/T1059/\n   - https://attack.mitre.org/tactics/TA0002/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0010/\n   - https://attack.mitre.org/groups/G0045/\n   - https://attack.mitre.org/groups/G0006/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/netstat\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\netstat.exe'\n        ProcessCommandLine: '*netstat*'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.execution\n    - attack.t1049\n    - attack.t1059\n    - attack.ta0002\n    - attack.ta0007\n    - attack.g0010\n    - attack.g0045\n    - attack.g0006", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Connections Discovery", "mitreId": "T1049", "url": "https://attack.mitre.org/techniques/T1049/", "type": "technique"}, {"name": "Command and Scripting Interpreter", "mitreId": "T1059", "url": "https://attack.mitre.org/techniques/T1059/", "type": "technique"}]}, {"ruleName": "Information Collection about User, Group and Privileges for the Current User on the Local System", "ruleId": "4142", "description": "Detects the attempt to display the user, group and privileges information for the current user via “whoami\" command. This technique is commonly utilized for discovery.", "releaseDate": 1578268800000, "updateDate": 1623628800000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Information Collection about User, Group and Privileges for the Current User on the Local System\nstatus: stable\ndescription: Detects the attempt to display the user, group and privileges information for the current user via “whoami\" command. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1033/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0050/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\whoami.exe'\n    condition: selection\nfalsepositives:\n    - Usual user activity\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.t1033\n    - attack.ta0007\n    - attack.g0050", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Usual user activity"], "mitre": [{"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}]}]}, "uuid": "de96baac-9ef7-497f-b6cb-798a56a4c4e6", "sigma_device_query": ""}