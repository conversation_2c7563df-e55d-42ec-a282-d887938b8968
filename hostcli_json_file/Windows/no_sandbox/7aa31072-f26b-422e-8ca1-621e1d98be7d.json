{"actions": {"7aa31072-f26b-422e-8ca1-621e1d98be7d": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "ver": 16, "vid": "370372", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用 Powershell 脚本修改另一个用户配置文件的注册表", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者使用 PowerShell 和 cmd 行工具修改当前未在计算机上加载的每个用户配置文件的注册表项。", "created_at": "2025-07-21 21:41:43", "updated_at": "2025-07-21 21:41:43", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "mitre_mitigation", "tag": "M1024", "tag_en": "M1024", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "mitre_tactics", "tag": "TA0005 - 防御绕过", "tag_en": "TA0005 - Defense Evasion", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "mitre_techniques", "tag": "T1112 - 修改注册表", "tag_en": "T1112 - Modify Registry", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "7aa31072-f26b-422e-8ca1-621e1d98be7d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2022", "mitre_mitigation ATT&CK:M1024", "os Windows:Server 2016", "os Windows:11", "control HIDS", "os Windows:10", "mitre_techniques T1112 - 修改注册表", "control AV", "control EDR/XDR", "run_as User", "mitre_tactics TA0005 - 防御绕过", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "run_as SYSTEM", "run_as Admin", "os Windows:Server 2019"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "T1112.ps1", "file_name": "T1112.ps1", "file_owner": "system", "file_transfer_library": 999057706, "file_type": "ps1", "filesize": 2396, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "6111fba108625d2f8b7fa262cd036cc0", "sha1sum": "06204e551f0632fc98106ffd6a02014fcde4a361", "sha256sum": "c9b44dc2f99458602a06b8c7fb744c3b7a91515a3bdbfc410a4bd1d8a4b10899"}], "monitor_connections": "off", "raw_text": "powershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"\r\npowershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1112.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1112.ps1'\"\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "powershell.exe -c \"$ep=Get-ExecutionPolicy;If ($ep -ne 'Unrestricted') {Set-ExecutionPolicy Unrestricted  -scope CurrentUser -Force}; Get-ExecutionPolicy\"", "prompt": "auto", "sleep": 1, "step_order": 1, "success_check": "match", "success_match": "Unrestricted", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "powershell.exe -c \"Unblock-File 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1112.ps1'; & 'C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\T1112.ps1'\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}