{"actions": {"6d3582bc-81d1-441f-a677-fabc0df5a771": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "ver": 4, "vid": "8001391", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.3.11 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:26:43", "updated_at": "2025-08-08 11:26:43", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "6d3582bc-81d1-441f-a677-fabc0df5a771", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54751\"]", "pcap_library": {"file_location": "pcap_library_998000798.pcap", "filesize": "672846 bytes", "md5sum": "5a9bffeb2abd70ecfc0f890a785bd98b", "orig_file_name": "pcap_library_998000798.pcap", "packet_count": 656}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8080\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8080", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "1466a16c-d9ec-4312-bd73-5f25ca73f0c1": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "ver": 4, "vid": "8001390", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.3.12 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:26:06", "updated_at": "2025-08-08 11:26:06", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "1466a16c-d9ec-4312-bd73-5f25ca73f0c1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54829\"]", "pcap_library": {"file_location": "pcap_library_998000797.pcap", "filesize": "540302 bytes", "md5sum": "7c6e7ec22bacdc35ab5edf0195483642", "orig_file_name": "pcap_library_998000797.pcap", "packet_count": 507}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8081\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8081", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "e19524ee-0465-4b34-8927-0813ab0fdb26": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "ver": 4, "vid": "8001389", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.3.13 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:25:33", "updated_at": "2025-08-08 11:25:33", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "e19524ee-0465-4b34-8927-0813ab0fdb26", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54892\"]", "pcap_library": {"file_location": "pcap_library_998000796.pcap", "filesize": "654979 bytes", "md5sum": "fcb57d021a6ab0552e60457c66de04cb", "orig_file_name": "pcap_library_998000796.pcap", "packet_count": 654}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8082\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8082", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "ff7ae817-c4be-463e-983e-02dbe8a3c6a1": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "ver": 4, "vid": "8001388", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.3.14 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:24:59", "updated_at": "2025-08-08 11:24:59", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "ff7ae817-c4be-463e-983e-02dbe8a3c6a1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54980\"]", "pcap_library": {"file_location": "pcap_library_998000795.pcap", "filesize": "681075 bytes", "md5sum": "375193d309940e17dee833438335fea9", "orig_file_name": "pcap_library_998000795.pcap", "packet_count": 669}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8083\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8083", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b5a229e2-6f2e-423a-9abb-dd5325953535": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "ver": 4, "vid": "8001387", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.0 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:24:18", "updated_at": "2025-08-08 11:24:18", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b5a229e2-6f2e-423a-9abb-dd5325953535", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55071\"]", "pcap_library": {"file_location": "pcap_library_998000794.pcap", "filesize": "673030 bytes", "md5sum": "69b11aec8f63582bf60523ce36908c1a", "orig_file_name": "pcap_library_998000794.pcap", "packet_count": 657}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8084\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8084", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "451a93f9-b854-400a-bca4-0f63f22abf0b": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "ver": 4, "vid": "8001386", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.2 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:23:37", "updated_at": "2025-08-08 11:23:37", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "451a93f9-b854-400a-bca4-0f63f22abf0b", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55158\"]", "pcap_library": {"file_location": "pcap_library_998000793.pcap", "filesize": "618070 bytes", "md5sum": "999b6bff8e868d8a3c487115bf2d75e6", "orig_file_name": "pcap_library_998000793.pcap", "packet_count": 587}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8085\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8085", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "67affba8-748f-414c-b331-a4dd0a3ade19": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "ver": 4, "vid": "8001385", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.3 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:23:05", "updated_at": "2025-08-08 11:23:05", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "67affba8-748f-414c-b331-a4dd0a3ade19", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55265\"]", "pcap_library": {"file_location": "pcap_library_998000792.pcap", "filesize": "666985 bytes", "md5sum": "17f5e2b96e64911e16fb4a1f0fc1c07d", "orig_file_name": "pcap_library_998000792.pcap", "packet_count": 646}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8086\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8086", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "82c764af-2c64-43b7-be4a-8e1accab4a86": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "ver": 4, "vid": "8001384", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.4 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:22:30", "updated_at": "2025-08-08 11:22:30", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "82c764af-2c64-43b7-be4a-8e1accab4a86", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55338\"]", "pcap_library": {"file_location": "pcap_library_998000791.pcap", "filesize": "790173 bytes", "md5sum": "92567204f19fddd343ec12d0849a2058", "orig_file_name": "pcap_library_998000791.pcap", "packet_count": 798}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8087\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8087", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b762f71b-bf5f-4b24-b877-64782cd00e6f": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "ver": 4, "vid": "8001383", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.5 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:21:29", "updated_at": "2025-08-08 11:21:29", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b762f71b-bf5f-4b24-b877-64782cd00e6f", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55412\"]", "pcap_library": {"file_location": "pcap_library_998000790.pcap", "filesize": "626739 bytes", "md5sum": "c2dddfcbffa1f30528d7753956f2d889", "orig_file_name": "pcap_library_998000790.pcap", "packet_count": 613}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8088\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8088", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "db8ab979-e206-4646-84cb-16f84ac8b195": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "ver": 4, "vid": "8001382", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.6 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:20:53", "updated_at": "2025-08-08 11:20:53", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "db8ab979-e206-4646-84cb-16f84ac8b195", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55500\"]", "pcap_library": {"file_location": "pcap_library_998000789.pcap", "filesize": "914417 bytes", "md5sum": "3a80f1a9d461d7f54887176a104131f6", "orig_file_name": "pcap_library_998000789.pcap", "packet_count": 938}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8089\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8089", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "70ea1035-3c6c-446f-ae03-1fd61e097896": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "ver": 4, "vid": "8001381", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.7 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:20:00", "updated_at": "2025-08-08 11:20:00", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "70ea1035-3c6c-446f-ae03-1fd61e097896", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55586\"]", "pcap_library": {"file_location": "pcap_library_998000788.pcap", "filesize": "556865 bytes", "md5sum": "f309cbed28fce4722c9243a121a39af0", "orig_file_name": "pcap_library_998000788.pcap", "packet_count": 515}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8090\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8090", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "498d7bd0-2e5a-46ba-a05d-12b19b3cd220": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "ver": 4, "vid": "8001380", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.8 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:19:24", "updated_at": "2025-08-08 11:19:24", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "498d7bd0-2e5a-46ba-a05d-12b19b3cd220", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55647\"]", "pcap_library": {"file_location": "pcap_library_998000787.pcap", "filesize": "578696 bytes", "md5sum": "7e54dbe8f5a2abc147d5d41d2594c5b4", "orig_file_name": "pcap_library_998000787.pcap", "packet_count": 564}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8091\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8091", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "6364529a-c870-4578-bb29-3f17c17d9835": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "ver": 4, "vid": "8001379", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 jQuery-c2.4.9 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:18:36", "updated_at": "2025-08-08 11:18:36", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "6364529a-c870-4578-bb29-3f17c17d9835", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/55727\"]", "pcap_library": {"file_location": "pcap_library_998000786.pcap", "filesize": "641231 bytes", "md5sum": "380f2cb1dfab4b869399c35ec724d504", "orig_file_name": "pcap_library_998000786.pcap", "packet_count": 639}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8092\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8092", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "84200f0f-f7ef-43b2-a94d-b0c707aeae60": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "ver": 4, "vid": "8001378", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Amazon Web 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:06:48", "updated_at": "2025-08-08 11:06:48", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "84200f0f-f7ef-43b2-a94d-b0c707aeae60", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/65399\",\"TCP/65414\",\"TCP/65434\",\"TCP/65381\",\"TCP/65391\",\"TCP/65401\",\"TCP/65386\",\"TCP/65408\",\"TCP/65412\",\"TCP/65419\",\"TCP/65426\",\"TCP/65432\",\"TCP/65383\",\"TCP/65385\",\"TCP/65387\",\"TCP/65421\",\"TCP/65431\",\"TCP/65438\",\"TCP/65404\",\"TCP/65424\",\"TCP/65396\",\"TCP/65409\",\"TCP/65394\",\"TCP/65417\",\"TCP/65422\",\"TCP/65436\",\"TCP/65439\",\"TCP/65433\",\"TCP/65402\",\"TCP/65403\",\"TCP/65415\",\"TCP/65379\",\"TCP/65407\",\"TCP/65413\",\"TCP/65440\",\"TCP/65441\",\"TCP/65393\",\"TCP/65390\",\"TCP/65423\",\"TCP/65429\",\"TCP/65398\",\"TCP/65416\",\"TCP/65380\",\"TCP/65388\",\"TCP/65397\",\"TCP/65425\",\"TCP/65392\",\"TCP/65428\",\"TCP/65437\",\"TCP/65430\",\"TCP/65405\",\"TCP/65410\",\"TCP/65418\",\"TCP/65435\",\"TCP/65382\",\"TCP/65384\",\"TCP/65411\",\"TCP/65389\",\"TCP/65427\",\"TCP/65420\",\"TCP/65395\",\"TCP/65400\"]", "pcap_library": {"file_location": "pcap_library_998000784.pcap", "filesize": "217568 bytes", "md5sum": "a00ad414449e6cda5416ca5232143f62", "orig_file_name": "pcap_library_998000784.pcap", "packet_count": 699}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8121\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8121", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "c9310d7a-0eb3-40df-8978-3e1d88bef8ef": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "ver": 4, "vid": "8001377", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 BazarLoader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:03:52", "updated_at": "2025-08-08 11:03:52", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "c9310d7a-0eb3-40df-8978-3e1d88bef8ef", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49634\"]", "pcap_library": {"file_location": "pcap_library_998000783.pcap", "filesize": "169799 bytes", "md5sum": "3eda81c49c0dedf129435b6ef0117590", "orig_file_name": "pcap_library_998000783.pcap", "packet_count": 216}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8098\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8098", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "1d222a35-f2b1-4fd7-82c2-c02ff62bd381": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "ver": 4, "vid": "8001376", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Bing Map 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:02:56", "updated_at": "2025-08-08 11:02:56", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "1d222a35-f2b1-4fd7-82c2-c02ff62bd381", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49682\",\"TCP/49683\",\"TCP/49688\",\"TCP/49689\",\"TCP/49695\",\"TCP/49697\",\"TCP/49702\",\"TCP/49713\",\"TCP/49687\",\"TCP/49694\",\"TCP/49716\",\"TCP/49722\",\"TCP/49700\",\"TCP/49704\",\"TCP/49709\",\"TCP/49718\",\"TCP/49726\",\"TCP/49698\",\"TCP/49699\",\"TCP/49711\",\"TCP/49712\",\"TCP/49724\",\"TCP/49679\",\"TCP/49701\",\"TCP/49714\",\"TCP/49719\",\"TCP/49728\",\"TCP/49681\",\"TCP/49723\",\"TCP/49696\",\"TCP/49707\",\"TCP/49703\",\"TCP/49680\",\"TCP/49685\",\"TCP/49690\",\"TCP/49691\",\"TCP/49710\",\"TCP/49727\",\"TCP/49715\",\"TCP/49721\",\"TCP/49684\",\"TCP/49686\",\"TCP/49692\",\"TCP/49693\",\"TCP/49678\",\"TCP/49717\",\"TCP/49720\"]", "pcap_library": {"file_location": "pcap_library_998000782.pcap", "filesize": "258021 bytes", "md5sum": "14cff81c97afdf5e2ca00a2bb902934e", "orig_file_name": "pcap_library_998000782.pcap", "packet_count": 577}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8122\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8122", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "1abc9a20-bc4a-45f9-8430-bac281087ebf": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "ver": 4, "vid": "8001375", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 BlueNoroff RAT 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:02:12", "updated_at": "2025-08-08 11:02:12", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "1abc9a20-bc4a-45f9-8430-bac281087ebf", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49773\",\"TCP/49779\",\"TCP/49793\",\"TCP/49802\",\"TCP/49783\",\"TCP/49795\",\"TCP/49774\",\"TCP/49782\",\"TCP/49784\",\"TCP/49786\",\"TCP/49788\",\"TCP/49792\",\"TCP/49777\",\"TCP/49790\",\"TCP/49775\",\"TCP/49781\",\"TCP/49803\",\"TCP/49778\",\"TCP/49794\",\"TCP/49796\",\"TCP/49799\",\"TCP/49801\",\"TCP/49785\",\"TCP/49789\",\"TCP/49791\",\"TCP/49780\",\"TCP/49787\",\"TCP/49772\",\"TCP/49798\",\"TCP/49776\",\"TCP/49797\",\"TCP/49800\"]", "pcap_library": {"file_location": "pcap_library_998000781.pcap", "filesize": "180314 bytes", "md5sum": "32795b6d2783ba8c505b532cfd3fa32f", "orig_file_name": "pcap_library_998000781.pcap", "packet_count": 376}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8093\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8093", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "af18b4db-8d0b-4eba-8683-b4f3d86360c3": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "ver": 4, "vid": "8001374", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 APT10 ChChes 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 11:01:11", "updated_at": "2025-08-08 11:01:11", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "af18b4db-8d0b-4eba-8683-b4f3d86360c3", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/49837\"]", "pcap_library": {"file_location": "pcap_library_998000780.pcap", "filesize": "191338 bytes", "md5sum": "61cc7b8df1c2b8ff1c588e1e897c225d", "orig_file_name": "pcap_library_998000780.pcap", "packet_count": 161}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8094\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8094", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "ver": 4, "vid": "8001373", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Chrome 浏览器流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:58:23", "updated_at": "2025-08-08 10:58:23", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "02fcc3be-a4f5-44a9-a9ce-f23f95d49a80", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/50936\"]", "pcap_library": {"file_location": "pcap_library_998000779.pcap", "filesize": "187852 bytes", "md5sum": "86c78abcb55d0e2bc44eb8e7c0e2e948", "orig_file_name": "pcap_library_998000779.pcap", "packet_count": 159}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8123\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8123", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "310fd40c-1f94-4859-a379-28825cb6b364": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "ver": 4, "vid": "8001372", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 COVID-19 Koadic 流量，变种 #1", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:56:55", "updated_at": "2025-08-08 10:56:55", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "310fd40c-1f94-4859-a379-28825cb6b364", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/50977\"]", "pcap_library": {"file_location": "pcap_library_998000778.pcap", "filesize": "158900 bytes", "md5sum": "7862375de5f1b798c5b60550471efbee", "orig_file_name": "pcap_library_998000778.pcap", "packet_count": 122}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8100\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8100", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "dcb30afd-8320-4cec-978a-2e1a60d0afb9": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "ver": 4, "vid": "8001371", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 COVID-19 Koadic 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:55:10", "updated_at": "2025-08-08 10:55:10", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "dcb30afd-8320-4cec-978a-2e1a60d0afb9", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51027\"]", "pcap_library": {"file_location": "pcap_library_998000777.pcap", "filesize": "262654 bytes", "md5sum": "6cac4eb28ee9c3e38dd35c15e323cbfe", "orig_file_name": "pcap_library_998000777.pcap", "packet_count": 183}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8099\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8099", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "63436995-e94f-4ec0-8281-f16babcbcfbd": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "ver": 4, "vid": "8001370", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 APT29（The Dukes）流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:53:23", "updated_at": "2025-08-08 10:53:23", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "63436995-e94f-4ec0-8281-f16babcbcfbd", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51114\"]", "pcap_library": {"file_location": "pcap_library_998000776.pcap", "filesize": "146724 bytes", "md5sum": "ae808af17b74d7d07011a9b1e7e506f6", "orig_file_name": "pcap_library_998000776.pcap", "packet_count": 109}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8095\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8095", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "ver": 4, "vid": "8001369", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Emotet 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:51:22", "updated_at": "2025-08-08 10:51:22", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "7b2a8e6b-87f0-4d9c-8dc5-e829bbc140ae", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51164\"]", "pcap_library": {"file_location": "pcap_library_998000775.pcap", "filesize": "158174 bytes", "md5sum": "f257822397fa46ad2934700bfcaa6ce1", "orig_file_name": "pcap_library_998000775.pcap", "packet_count": 112}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8101\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8101", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "65c6949e-6c29-4251-9942-f1c2b75f8cd6": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "ver": 4, "vid": "8001368", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 FormBook 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:49:47", "updated_at": "2025-08-08 10:49:47", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "65c6949e-6c29-4251-9942-f1c2b75f8cd6", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51223\",\"TCP/51204\",\"TCP/51206\",\"TCP/51209\",\"TCP/51210\",\"TCP/51215\",\"TCP/51218\",\"TCP/51228\",\"TCP/51233\",\"TCP/51211\",\"TCP/51217\",\"TCP/51230\",\"TCP/51231\",\"TCP/51212\",\"TCP/51213\",\"TCP/51207\",\"TCP/51214\",\"TCP/51216\",\"TCP/51221\",\"TCP/51232\",\"TCP/51224\",\"TCP/51227\",\"TCP/51219\",\"TCP/51222\",\"TCP/51225\",\"TCP/51205\",\"TCP/51208\",\"TCP/51229\",\"TCP/51234\",\"TCP/51220\",\"TCP/51226\"]", "pcap_library": {"file_location": "pcap_library_998000774.pcap", "filesize": "181968 bytes", "md5sum": "0e19de3c55a09915fc8a19b5795098f2", "orig_file_name": "pcap_library_998000774.pcap", "packet_count": 362}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8102\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8102", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b0cae575-d6df-4293-87c5-a496abcf87fd": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "ver": 4, "vid": "8001367", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 GandCrab 勒索流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:47:32", "updated_at": "2025-08-08 10:47:32", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b0cae575-d6df-4293-87c5-a496abcf87fd", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51303\",\"TCP/51306\",\"TCP/51307\",\"TCP/51310\",\"TCP/51317\",\"TCP/51318\",\"TCP/51297\",\"TCP/51298\",\"TCP/51312\",\"TCP/51285\",\"TCP/51293\",\"TCP/51299\",\"TCP/51300\",\"TCP/51313\",\"TCP/51287\",\"TCP/51296\",\"TCP/51309\",\"TCP/51291\",\"TCP/51294\",\"TCP/51301\",\"TCP/51302\",\"TCP/51288\",\"TCP/51295\",\"TCP/51314\",\"TCP/51315\",\"TCP/51305\",\"TCP/51319\",\"TCP/51316\",\"TCP/51292\",\"TCP/51286\",\"TCP/51290\",\"TCP/51311\",\"TCP/51304\",\"TCP/51308\",\"TCP/51289\"]", "pcap_library": {"file_location": "pcap_library_998000773.pcap", "filesize": "180986 bytes", "md5sum": "7144ef10a273d842f218b4bd95de5f66", "orig_file_name": "pcap_library_998000773.pcap", "packet_count": 401}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8103\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8103", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b341e341-5c62-4f0f-a7fc-7466101bf182": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "ver": 4, "vid": "8001366", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 GlobeImposter 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:45:39", "updated_at": "2025-08-08 10:45:39", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b341e341-5c62-4f0f-a7fc-7466101bf182", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51356\"]", "pcap_library": {"file_location": "pcap_library_998000772.pcap", "filesize": "158461 bytes", "md5sum": "4e2110be3fb49a55297a05a4ad42a41e", "orig_file_name": "pcap_library_998000772.pcap", "packet_count": 133}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8104\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8104", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "cebe6d81-a6d3-4f41-ac41-6124c7d61145": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "ver": 4, "vid": "8001365", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 GoTo Meeting 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:44:30", "updated_at": "2025-08-08 10:44:30", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "cebe6d81-a6d3-4f41-ac41-6124c7d61145", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51432\",\"TCP/51439\",\"TCP/51415\",\"TCP/51442\",\"TCP/51443\",\"TCP/51444\",\"TCP/51413\",\"TCP/51416\",\"TCP/51417\",\"TCP/51429\",\"TCP/51445\",\"TCP/51412\",\"TCP/51431\",\"TCP/51438\",\"TCP/51446\",\"TCP/51414\",\"TCP/51420\",\"TCP/51424\",\"TCP/51427\",\"TCP/51447\",\"TCP/51404\",\"TCP/51405\",\"TCP/51407\",\"TCP/51411\",\"TCP/51423\",\"TCP/51440\",\"TCP/51450\",\"TCP/51402\",\"TCP/51430\",\"TCP/51433\",\"TCP/51436\",\"TCP/51441\",\"TCP/51425\",\"TCP/51418\",\"TCP/51422\",\"TCP/51449\",\"TCP/51406\",\"TCP/51409\",\"TCP/51448\",\"TCP/51410\",\"TCP/51434\",\"TCP/51419\",\"TCP/51403\",\"TCP/51421\",\"TCP/51426\",\"TCP/51435\",\"TCP/51408\",\"TCP/51428\",\"TCP/51437\"]", "pcap_library": {"file_location": "pcap_library_998000771.pcap", "filesize": "219555 bytes", "md5sum": "4fa6fba15ae344c446e1af8e956bc459", "orig_file_name": "pcap_library_998000771.pcap", "packet_count": 568}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8124\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8124", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "455eb90f-59e2-4721-8e14-f412c623df1e": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "ver": 4, "vid": "8001364", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Hancitor 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:42:40", "updated_at": "2025-08-08 10:42:40", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "455eb90f-59e2-4721-8e14-f412c623df1e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51487\"]", "pcap_library": {"file_location": "pcap_library_998000770.pcap", "filesize": "179349 bytes", "md5sum": "40ccfbbf443c837ecc1f25d9c00d4bab", "orig_file_name": "pcap_library_998000770.pcap", "packet_count": 183}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8105\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8105", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "c73f907b-da7c-459d-a496-1fa20c701de2": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "ver": 4, "vid": "8001363", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 iHeartRadio 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:41:26", "updated_at": "2025-08-08 10:41:26", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "c73f907b-da7c-459d-a496-1fa20c701de2", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51535\",\"TCP/51519\",\"TCP/51522\",\"TCP/51524\",\"TCP/51528\",\"TCP/51530\",\"TCP/51531\",\"TCP/51534\",\"TCP/51518\",\"TCP/51532\",\"TCP/51537\",\"TCP/51538\",\"TCP/51545\",\"TCP/51533\",\"TCP/51540\",\"TCP/51542\",\"TCP/51526\",\"TCP/51536\",\"TCP/51539\",\"TCP/51517\",\"TCP/51527\",\"TCP/51541\",\"TCP/51543\",\"TCP/51544\",\"TCP/51516\",\"TCP/51529\"]", "pcap_library": {"file_location": "pcap_library_998000769.pcap", "filesize": "191110 bytes", "md5sum": "afbcf07aa237c565bc9202b0d491901a", "orig_file_name": "pcap_library_998000769.pcap", "packet_count": 325}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8125\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8125", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "3cef44be-5f81-4869-b054-693a3efea06d": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "ver": 4, "vid": "8001362", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Jaff 勒索流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:40:09", "updated_at": "2025-08-08 10:40:09", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "3cef44be-5f81-4869-b054-693a3efea06d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51589\"]", "pcap_library": {"file_location": "pcap_library_998000768.pcap", "filesize": "160220 bytes", "md5sum": "e41b8be43ae86d373cbd2f49a44f6952", "orig_file_name": "pcap_library_998000768.pcap", "packet_count": 147}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8106\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8106", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "ver": 4, "vid": "8001361", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 JasperLoader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:38:25", "updated_at": "2025-08-08 10:38:25", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "2bf2f8a8-3416-4d7c-b904-963e2d1a23ec", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51627\"]", "pcap_library": {"file_location": "pcap_library_998000767.pcap", "filesize": "193222 bytes", "md5sum": "a04a13e474797065a0d81fa8b7211444", "orig_file_name": "pcap_library_998000767.pcap", "packet_count": 164}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8107\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8107", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "39bb796c-63c0-4648-9779-bb9eff6c7c6f": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "ver": 4, "vid": "8001360", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Kronos 木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:37:11", "updated_at": "2025-08-08 10:37:11", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "39bb796c-63c0-4648-9779-bb9eff6c7c6f", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51699\",\"TCP/51700\",\"TCP/51706\",\"TCP/51711\",\"TCP/51712\",\"TCP/51716\",\"TCP/51718\",\"TCP/51719\",\"TCP/51707\",\"TCP/51709\",\"TCP/51723\",\"TCP/51725\",\"TCP/51693\",\"TCP/51701\",\"TCP/51705\",\"TCP/51722\",\"TCP/51724\",\"TCP/51694\",\"TCP/51698\",\"TCP/51717\",\"TCP/51720\",\"TCP/51704\",\"TCP/51696\",\"TCP/51702\",\"TCP/51708\",\"TCP/51695\",\"TCP/51715\",\"TCP/51721\",\"TCP/51692\",\"TCP/51713\",\"TCP/51697\",\"TCP/51703\",\"TCP/51710\",\"TCP/51714\"]", "pcap_library": {"file_location": "pcap_library_998000766.pcap", "filesize": "184050 bytes", "md5sum": "53de5ca8bb036ed9e7eff09a0ee8ae09", "orig_file_name": "pcap_library_998000766.pcap", "packet_count": 394}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8108\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8108", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "e9d0c4ac-5879-4717-87d7-d2198815e8bc": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "ver": 4, "vid": "8001359", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Mayoclinic 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:31:54", "updated_at": "2025-08-08 10:31:54", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "e9d0c4ac-5879-4717-87d7-d2198815e8bc", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51774\",\"TCP/51789\",\"TCP/51796\",\"TCP/51778\",\"TCP/51792\",\"TCP/51775\",\"TCP/51787\",\"TCP/51779\",\"TCP/51780\",\"TCP/51781\",\"TCP/51790\",\"TCP/51795\",\"TCP/51798\",\"TCP/51801\",\"TCP/51788\",\"TCP/51799\",\"TCP/51773\",\"TCP/51783\",\"TCP/51794\",\"TCP/51776\",\"TCP/51786\",\"TCP/51782\",\"TCP/51791\",\"TCP/51785\",\"TCP/51797\",\"TCP/51800\",\"TCP/51793\",\"TCP/51777\",\"TCP/51784\"]", "pcap_library": {"file_location": "pcap_library_998000765.pcap", "filesize": "220665 bytes", "md5sum": "bfcbe2f909fce31e2af0edc03c7f1b7e", "orig_file_name": "pcap_library_998000765.pcap", "packet_count": 365}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8126\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8126", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "9324160e-6487-459d-a52c-4a55d05453b5": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "ver": 4, "vid": "8001358", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟微软 mscrl 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:29:14", "updated_at": "2025-08-08 10:29:14", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "9324160e-6487-459d-a52c-4a55d05453b5", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/51843\",\"TCP/51851\",\"TCP/51852\",\"TCP/51857\",\"TCP/51832\",\"TCP/51833\",\"TCP/51848\",\"TCP/51836\",\"TCP/51839\",\"TCP/51841\",\"TCP/51844\",\"TCP/51853\",\"TCP/51855\",\"TCP/51856\",\"TCP/51834\",\"TCP/51845\",\"TCP/51858\",\"TCP/51861\",\"TCP/51840\",\"TCP/51837\",\"TCP/51849\",\"TCP/51859\",\"TCP/51860\",\"TCP/51854\",\"TCP/51830\",\"TCP/51838\",\"TCP/51850\",\"TCP/51835\",\"TCP/51846\",\"TCP/51831\",\"TCP/51842\",\"TCP/51847\"]", "pcap_library": {"file_location": "pcap_library_998000764.pcap", "filesize": "179272 bytes", "md5sum": "f8ed3c3533a547bbe3f943e3f7471af7", "orig_file_name": "pcap_library_998000764.pcap", "packet_count": 380}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8127\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8127", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "989cdebc-1708-4352-bcfd-0f129950819a": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "ver": 4, "vid": "8001357", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 msu edu 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:25:43", "updated_at": "2025-08-08 10:25:43", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "989cdebc-1708-4352-bcfd-0f129950819a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52055\",\"TCP/52066\",\"TCP/52073\",\"TCP/52056\",\"TCP/52034\",\"TCP/52093\",\"TCP/52044\",\"TCP/52050\",\"TCP/52054\",\"TCP/52057\",\"TCP/52063\",\"TCP/52065\",\"TCP/52070\",\"TCP/52051\",\"TCP/52037\",\"TCP/52061\",\"TCP/52062\",\"TCP/52058\",\"TCP/52043\",\"TCP/52035\",\"TCP/52038\",\"TCP/52048\",\"TCP/52064\",\"TCP/52047\",\"TCP/52049\",\"TCP/52060\",\"TCP/52067\",\"TCP/52083\",\"TCP/52088\",\"TCP/52045\",\"TCP/52053\",\"TCP/52059\",\"TCP/52069\",\"TCP/52092\",\"TCP/52052\",\"TCP/52068\",\"TCP/52074\",\"TCP/52036\",\"TCP/52072\",\"TCP/52080\",\"TCP/52046\"]", "pcap_library": {"file_location": "pcap_library_998000763.pcap", "filesize": "212365 bytes", "md5sum": "51fb13095071c5d4275423fcb11797ca", "orig_file_name": "pcap_library_998000763.pcap", "packet_count": 537}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8128\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8128", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "08e1a0ca-94b6-4c04-a409-624c16804858": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "ver": 4, "vid": "8001356", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Office 365 Calendar 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:23:03", "updated_at": "2025-08-08 10:23:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "08e1a0ca-94b6-4c04-a409-624c16804858", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52124\",\"TCP/52126\",\"TCP/52128\",\"TCP/52136\",\"TCP/52142\",\"TCP/52143\",\"TCP/52144\",\"TCP/52152\",\"TCP/52125\",\"TCP/52134\",\"TCP/52138\",\"TCP/52148\",\"TCP/52149\",\"TCP/52150\",\"TCP/52153\",\"TCP/52132\",\"TCP/52135\",\"TCP/52158\",\"TCP/52160\",\"TCP/52162\",\"TCP/52165\",\"TCP/52166\",\"TCP/52167\",\"TCP/52140\",\"TCP/52163\",\"TCP/52170\",\"TCP/52171\",\"TCP/52175\",\"TCP/52130\",\"TCP/52145\",\"TCP/52151\",\"TCP/52155\",\"TCP/52168\",\"TCP/52146\",\"TCP/52123\",\"TCP/52172\",\"TCP/52131\",\"TCP/52161\",\"TCP/52174\",\"TCP/52133\",\"TCP/52157\",\"TCP/52173\",\"TCP/52127\",\"TCP/52129\",\"TCP/52156\",\"TCP/52169\",\"TCP/52147\",\"TCP/52164\",\"TCP/52139\",\"TCP/52141\",\"TCP/52154\",\"TCP/52159\",\"TCP/52137\"]", "pcap_library": {"file_location": "pcap_library_998000762.pcap", "filesize": "228094 bytes", "md5sum": "7741e115dea502c599358a44718f46b5", "orig_file_name": "pcap_library_998000762.pcap", "packet_count": 602}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8129\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8129", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "ver": 4, "vid": "8001355", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 POSeidon 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:22:03", "updated_at": "2025-08-08 10:22:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "52d5ef7b-9a7c-415d-9f91-c3eed7f763b2", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52212\",\"TCP/52222\",\"TCP/52223\",\"TCP/52224\",\"TCP/52236\",\"TCP/52218\",\"TCP/52219\",\"TCP/52230\",\"TCP/52238\",\"TCP/52245\",\"TCP/52234\",\"TCP/52210\",\"TCP/52226\",\"TCP/52233\",\"TCP/52241\",\"TCP/52208\",\"TCP/52211\",\"TCP/52231\",\"TCP/52243\",\"TCP/52246\",\"TCP/52225\",\"TCP/52232\",\"TCP/52239\",\"TCP/52213\",\"TCP/52221\",\"TCP/52214\",\"TCP/52235\",\"TCP/52215\",\"TCP/52217\",\"TCP/52227\",\"TCP/52228\",\"TCP/52229\",\"TCP/52237\",\"TCP/52242\",\"TCP/52209\",\"TCP/52216\",\"TCP/52240\",\"TCP/52244\",\"TCP/52247\",\"TCP/52220\"]", "pcap_library": {"file_location": "pcap_library_998000761.pcap", "filesize": "197640 bytes", "md5sum": "bf195036209960c4779f6d470631d061", "orig_file_name": "pcap_library_998000761.pcap", "packet_count": 468}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8109\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8109", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "2de84d0b-af4f-4201-a04d-d0d7c75449b6": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "ver": 4, "vid": "8001354", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 PowRuner 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:21:12", "updated_at": "2025-08-08 10:21:12", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "2de84d0b-af4f-4201-a04d-d0d7c75449b6", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52305\"]", "pcap_library": {"file_location": "pcap_library_998000760.pcap", "filesize": "177184 bytes", "md5sum": "1e149c6eca2c148154e0d5d67ea574a7", "orig_file_name": "pcap_library_998000760.pcap", "packet_count": 200}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8096\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8096", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "ccf3ef03-1db7-464b-a159-7e9eb68e41ff": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "ver": 4, "vid": "8001353", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 QakBot 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:20:13", "updated_at": "2025-08-08 10:20:13", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "ccf3ef03-1db7-464b-a159-7e9eb68e41ff", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52387\"]", "pcap_library": {"file_location": "pcap_library_998000759.pcap", "filesize": "193978 bytes", "md5sum": "eb2a77a181292b2adc52e04d569c4961", "orig_file_name": "pcap_library_998000759.pcap", "packet_count": 266}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8110\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8110", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "42b34513-9a28-4234-acae-8fd20f8fa3ca": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "ver": 4, "vid": "8001352", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 QuantLoader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:19:28", "updated_at": "2025-08-08 10:19:28", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "42b34513-9a28-4234-acae-8fd20f8fa3ca", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52453\"]", "pcap_library": {"file_location": "pcap_library_998000758.pcap", "filesize": "195773 bytes", "md5sum": "f0f5feb4c95b12e1cd2ade74eff8c851", "orig_file_name": "pcap_library_998000758.pcap", "packet_count": 272}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8111\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8111", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "32df30a2-8a91-45ff-94f4-5c04fe861dd1": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "ver": 4, "vid": "8001351", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Ramnit 木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:18:33", "updated_at": "2025-08-08 10:18:33", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "32df30a2-8a91-45ff-94f4-5c04fe861dd1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52547\"]", "pcap_library": {"file_location": "pcap_library_998000757.pcap", "filesize": "209534 bytes", "md5sum": "4fc9bf8cdd9bc772fd770173411335b0", "orig_file_name": "pcap_library_998000757.pcap", "packet_count": 275}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8112\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8112", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "02fd7d97-22a0-40bb-88fd-12e896347574": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "ver": 4, "vid": "8001350", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Ratankba 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:17:39", "updated_at": "2025-08-08 10:17:39", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "02fd7d97-22a0-40bb-88fd-12e896347574", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52711\",\"TCP/52609\",\"TCP/52652\",\"TCP/52659\",\"TCP/52661\",\"TCP/52663\",\"TCP/52620\",\"TCP/52629\",\"TCP/52637\",\"TCP/52648\",\"TCP/52655\",\"TCP/52684\",\"TCP/52627\",\"TCP/52667\",\"TCP/52671\",\"TCP/52688\",\"TCP/52606\",\"TCP/52666\",\"TCP/52702\",\"TCP/52707\",\"TCP/52604\",\"TCP/52630\",\"TCP/52647\",\"TCP/52699\",\"TCP/52701\",\"TCP/52612\",\"TCP/52639\",\"TCP/52714\",\"TCP/52631\",\"TCP/52651\",\"TCP/52678\",\"TCP/52685\",\"TCP/52704\",\"TCP/52674\",\"TCP/52677\",\"TCP/52705\",\"TCP/52621\",\"TCP/52624\",\"TCP/52634\",\"TCP/52649\",\"TCP/52640\",\"TCP/52665\",\"TCP/52615\",\"TCP/52641\",\"TCP/52716\",\"TCP/52719\",\"TCP/52614\",\"TCP/52650\",\"TCP/52668\",\"TCP/52683\",\"TCP/52619\",\"TCP/52617\",\"TCP/52653\",\"TCP/52658\",\"TCP/52676\",\"TCP/52686\",\"TCP/52698\",\"TCP/52700\",\"TCP/52626\",\"TCP/52643\",\"TCP/52646\",\"TCP/52708\",\"TCP/52679\",\"TCP/52693\",\"TCP/52623\",\"TCP/52628\",\"TCP/52717\",\"TCP/52611\",\"TCP/52664\",\"TCP/52713\",\"TCP/52616\",\"TCP/52644\",\"TCP/52681\",\"TCP/52610\",\"TCP/52675\",\"TCP/52710\",\"TCP/52618\",\"TCP/52694\",\"TCP/52605\",\"TCP/52607\",\"TCP/52709\",\"TCP/52690\",\"TCP/52660\",\"TCP/52691\",\"TCP/52695\",\"TCP/52633\",\"TCP/52645\",\"TCP/52696\",\"TCP/52625\",\"TCP/52672\",\"TCP/52673\",\"TCP/52703\",\"TCP/52613\",\"TCP/52654\",\"TCP/52656\",\"TCP/52657\",\"TCP/52712\",\"TCP/52638\",\"TCP/52687\",\"TCP/52636\",\"TCP/52642\",\"TCP/52680\",\"TCP/52718\",\"TCP/52669\",\"TCP/52692\",\"TCP/52715\",\"TCP/52608\",\"TCP/52602\",\"TCP/52689\",\"TCP/52632\",\"TCP/52603\",\"TCP/52622\",\"TCP/52682\",\"TCP/52635\",\"TCP/52706\",\"TCP/52697\",\"TCP/52662\",\"TCP/52670\"]", "pcap_library": {"file_location": "pcap_library_998000756.pcap", "filesize": "243039 bytes", "md5sum": "25e65c58f3acb7bf72e40668326fa26f", "orig_file_name": "pcap_library_998000756.pcap", "packet_count": 247}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8097\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8097", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "d563ceee-b913-4ed6-a9d7-2696c9a7e997": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "ver": 4, "vid": "8001349", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Reddit 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:13:57", "updated_at": "2025-08-08 10:13:57", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "d563ceee-b913-4ed6-a9d7-2696c9a7e997", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52810\"]", "pcap_library": {"file_location": "pcap_library_998000754.pcap", "filesize": "185808 bytes", "md5sum": "055b4b5793558312e22206092884efcb", "orig_file_name": "pcap_library_998000754.pcap", "packet_count": 231}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8130\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8130", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "5433da72-515e-4128-912b-5c8b3cd9a28a": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "ver": 4, "vid": "8001348", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 RigEK 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:12:08", "updated_at": "2025-08-08 10:12:08", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "5433da72-515e-4128-912b-5c8b3cd9a28a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/52974\",\"TCP/52902\",\"TCP/52938\",\"TCP/52940\",\"TCP/52941\",\"TCP/52965\",\"TCP/52926\",\"TCP/52943\",\"TCP/52957\",\"TCP/52908\",\"TCP/52910\",\"TCP/52961\",\"TCP/52964\",\"TCP/52967\",\"TCP/52897\",\"TCP/52925\",\"TCP/52966\",\"TCP/52973\",\"TCP/52898\",\"TCP/52899\",\"TCP/52959\",\"TCP/52969\",\"TCP/52905\",\"TCP/52927\",\"TCP/52945\",\"TCP/52971\",\"TCP/52935\",\"TCP/52970\",\"TCP/52901\",\"TCP/52913\",\"TCP/52932\",\"TCP/52896\",\"TCP/52909\",\"TCP/52923\",\"TCP/52928\",\"TCP/52954\",\"TCP/52900\",\"TCP/52934\",\"TCP/52939\",\"TCP/52963\",\"TCP/52948\",\"TCP/52949\",\"TCP/52950\",\"TCP/52906\",\"TCP/52907\",\"TCP/52911\",\"TCP/52915\",\"TCP/52921\",\"TCP/52924\",\"TCP/52937\",\"TCP/52942\",\"TCP/52956\",\"TCP/52968\",\"TCP/52972\",\"TCP/52914\",\"TCP/52931\",\"TCP/52960\",\"TCP/52903\",\"TCP/52929\",\"TCP/52936\",\"TCP/52951\",\"TCP/52958\",\"TCP/52962\",\"TCP/52946\",\"TCP/52955\",\"TCP/52975\",\"TCP/52933\",\"TCP/52944\",\"TCP/52952\",\"TCP/52953\",\"TCP/52895\",\"TCP/52930\",\"TCP/52912\",\"TCP/52947\",\"TCP/52916\",\"TCP/52904\",\"TCP/52917\"]", "pcap_library": {"file_location": "pcap_library_998000753.pcap", "filesize": "324431 bytes", "md5sum": "553e8d32415aa98122e209e0656a3cbb", "orig_file_name": "pcap_library_998000753.pcap", "packet_count": 868}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8113\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8113", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "bd24da7f-6c2a-4589-8f1c-997240c81fdf": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "ver": 4, "vid": "8001347", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Saefko 木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:09:42", "updated_at": "2025-08-08 10:09:42", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "bd24da7f-6c2a-4589-8f1c-997240c81fdf", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53029\"]", "pcap_library": {"file_location": "pcap_library_998000752.pcap", "filesize": "199040 bytes", "md5sum": "9aa3cbb9f1e8c021e6353de59979a5e9", "orig_file_name": "pcap_library_998000752.pcap", "packet_count": 363}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8114\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8114", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "3e63d093-0480-4000-8ea9-bb8e696cc437": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "ver": 4, "vid": "8001346", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Salesforce API 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:06:47", "updated_at": "2025-08-08 10:06:47", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "3e63d093-0480-4000-8ea9-bb8e696cc437", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53133\",\"TCP/53135\",\"TCP/53171\",\"TCP/53173\",\"TCP/53174\",\"TCP/53186\",\"TCP/53189\",\"TCP/53162\",\"TCP/53156\",\"TCP/53170\",\"TCP/53123\",\"TCP/53151\",\"TCP/53157\",\"TCP/53167\",\"TCP/53142\",\"TCP/53143\",\"TCP/53165\",\"TCP/53168\",\"TCP/53188\",\"TCP/53130\",\"TCP/53134\",\"TCP/53145\",\"TCP/53153\",\"TCP/53159\",\"TCP/53125\",\"TCP/53136\",\"TCP/53140\",\"TCP/53181\",\"TCP/53183\",\"TCP/53147\",\"TCP/53152\",\"TCP/53164\",\"TCP/53166\",\"TCP/53122\",\"TCP/53141\",\"TCP/53150\",\"TCP/53124\",\"TCP/53169\",\"TCP/53187\",\"TCP/53127\",\"TCP/53129\",\"TCP/53131\",\"TCP/53139\",\"TCP/53176\",\"TCP/53185\",\"TCP/53146\",\"TCP/53126\",\"TCP/53160\",\"TCP/53182\",\"TCP/53144\",\"TCP/53172\",\"TCP/53163\",\"TCP/53128\",\"TCP/53137\",\"TCP/53154\",\"TCP/53155\",\"TCP/53158\",\"TCP/53180\",\"TCP/53175\",\"TCP/53184\",\"TCP/53161\",\"TCP/53138\",\"TCP/53149\",\"TCP/53179\",\"TCP/53132\",\"TCP/53178\",\"TCP/53148\",\"TCP/53121\",\"TCP/53177\"]", "pcap_library": {"file_location": "pcap_library_998000751.pcap", "filesize": "265144 bytes", "md5sum": "4116572038817e4b6114665cc44853f2", "orig_file_name": "pcap_library_998000751.pcap", "packet_count": 781}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8131\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8131", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "d81c9d48-4561-4475-948a-e6353b0bfb1a": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "ver": 4, "vid": "8001345", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Slack 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:03:01", "updated_at": "2025-08-08 10:03:01", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "d81c9d48-4561-4475-948a-e6353b0bfb1a", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53278\",\"TCP/53292\",\"TCP/53321\",\"TCP/53328\",\"TCP/53337\",\"TCP/53284\",\"TCP/53304\",\"TCP/53335\",\"TCP/53275\",\"TCP/53288\",\"TCP/53300\",\"TCP/53333\",\"TCP/53281\",\"TCP/53283\",\"TCP/53310\",\"TCP/53338\",\"TCP/53282\",\"TCP/53294\",\"TCP/53295\",\"TCP/53305\",\"TCP/53314\",\"TCP/53317\",\"TCP/53276\",\"TCP/53301\",\"TCP/53319\",\"TCP/53339\",\"TCP/53287\",\"TCP/53316\",\"TCP/53318\",\"TCP/53323\",\"TCP/53334\",\"TCP/53342\",\"TCP/53274\",\"TCP/53286\",\"TCP/53311\",\"TCP/53320\",\"TCP/53324\",\"TCP/53298\",\"TCP/53309\",\"TCP/53325\",\"TCP/53331\",\"TCP/53289\",\"TCP/53302\",\"TCP/53329\",\"TCP/53306\",\"TCP/53303\",\"TCP/53326\",\"TCP/53332\",\"TCP/53293\",\"TCP/53322\",\"TCP/53336\",\"TCP/53341\",\"TCP/53290\",\"TCP/53296\",\"TCP/53340\",\"TCP/53297\",\"TCP/53312\",\"TCP/53285\",\"TCP/53308\",\"TCP/53330\",\"TCP/53315\",\"TCP/53277\",\"TCP/53344\",\"TCP/53313\",\"TCP/53279\",\"TCP/53327\",\"TCP/53280\",\"TCP/53299\",\"TCP/53307\"]", "pcap_library": {"file_location": "pcap_library_998000750.pcap", "filesize": "264131 bytes", "md5sum": "24d5fa0a408798ce750b421f3947b2db", "orig_file_name": "pcap_library_998000750.pcap", "packet_count": 780}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8132\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8132", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "0de8b883-86fd-4329-9da8-e227a3da97da": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "ver": 4, "vid": "8001344", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 StackOverflow 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-08 10:01:47", "updated_at": "2025-08-08 10:01:47", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "0de8b883-86fd-4329-9da8-e227a3da97da", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53447\",\"TCP/53449\",\"TCP/53451\",\"TCP/53453\",\"TCP/53464\",\"TCP/53482\",\"TCP/53486\",\"TCP/53493\",\"TCP/53455\",\"TCP/53481\",\"TCP/53461\",\"TCP/53473\",\"TCP/53477\",\"TCP/53484\",\"TCP/53485\",\"TCP/53436\",\"TCP/53448\",\"TCP/53492\",\"TCP/53495\",\"TCP/53435\",\"TCP/53441\",\"TCP/53459\",\"TCP/53470\",\"TCP/53471\",\"TCP/53478\",\"TCP/53438\",\"TCP/53440\",\"TCP/53472\",\"TCP/53474\",\"TCP/53475\",\"TCP/53439\",\"TCP/53457\",\"TCP/53488\",\"TCP/53452\",\"TCP/53467\",\"TCP/53491\",\"TCP/53442\",\"TCP/53458\",\"TCP/53479\",\"TCP/53443\",\"TCP/53450\",\"TCP/53462\",\"TCP/53483\",\"TCP/53433\",\"TCP/53437\",\"TCP/53446\",\"TCP/53469\",\"TCP/53465\",\"TCP/53468\",\"TCP/53434\",\"TCP/53454\",\"TCP/53466\",\"TCP/53494\",\"TCP/53460\",\"TCP/53476\",\"TCP/53490\",\"TCP/53487\",\"TCP/53444\",\"TCP/53445\",\"TCP/53463\",\"TCP/53480\",\"TCP/53456\",\"TCP/53489\"]", "pcap_library": {"file_location": "pcap_library_998000749.pcap", "filesize": "254999 bytes", "md5sum": "10aaa9939603bcea2320f89a5911edd9", "orig_file_name": "pcap_library_998000749.pcap", "packet_count": 719}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8133\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8133", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "*************-4f9d-a864-901b4484854d": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "*************-4f9d-a864-901b4484854d", "ver": 4, "vid": "8001333", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - VShell v4.9.3 C&C 回连心跳通信流量，TCP，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 VShell C&C 感染的设备 C&C 回连心跳通信流量。VShell 是一款以 Golang 开发的开源跨平台 Remote Access Trojan（RAT），支持 Windows、Linux 与 macOS 系统。它具备远程命令执行、文件上传/下载与系统信息收集等基本后渗透功能，同时支持 WebSockets 加密通信，能够实现实时、隐蔽且难以被检测的 C2 通道。该工具采用 文件无痕（fileless）执行方式，可驻留于内存中，并通过内存执行逃避传统防护机制 。其界面采用 Web 控制台，设计简洁易用。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-08 09:55:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [{"uuid": "*************-4f9d-a864-901b4484854d", "source": "Palo Alto Networks", "message": "Malware XOR Obfuscation Detection", "source_identifier": "18915", "cn_show": false, "product_version": "", "signature_version": ""}], "sectech_logo": null, "tags": [{"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "*************-4f9d-a864-901b4484854d", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/50734\",\"TCP/50735\",\"TCP/50736\",\"TCP/50737\"]", "pcap_library": {"file_location": "pcap_library_998000736.pcap", "filesize": "5689172 bytes", "md5sum": "4bb8b51562c417268f7897afd77cac0f", "orig_file_name": "pcap_library_998000736.pcap", "packet_count": 7545}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "*************", "target_ports": "[\"TCP/8084\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8084", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "c86714ba-55c0-44e9-9375-69d4e3e0d435": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "ver": 4, "vid": "8001343", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Trevor 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-07 19:27:54", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "c86714ba-55c0-44e9-9375-69d4e3e0d435", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53696\",\"TCP/53646\",\"TCP/53673\",\"TCP/53666\",\"TCP/53674\",\"TCP/53702\",\"TCP/53723\",\"TCP/53724\",\"TCP/53725\",\"TCP/53728\",\"TCP/53670\",\"TCP/53671\",\"TCP/53711\",\"TCP/53717\",\"TCP/53650\",\"TCP/53656\",\"TCP/53706\",\"TCP/53729\",\"TCP/53647\",\"TCP/53699\",\"TCP/53704\",\"TCP/53637\",\"TCP/53719\",\"TCP/53697\",\"TCP/53701\",\"TCP/53685\",\"TCP/53691\",\"TCP/53708\",\"TCP/53718\",\"TCP/53648\",\"TCP/53665\",\"TCP/53712\",\"TCP/53721\",\"TCP/53707\",\"TCP/53713\",\"TCP/53715\",\"TCP/53683\",\"TCP/53698\",\"TCP/53638\",\"TCP/53722\",\"TCP/53726\",\"TCP/53727\",\"TCP/53705\",\"TCP/53658\",\"TCP/53675\",\"TCP/53720\",\"TCP/53640\",\"TCP/53686\",\"TCP/53710\",\"TCP/53676\",\"TCP/53716\",\"TCP/53639\",\"TCP/53654\",\"TCP/53695\",\"TCP/53700\",\"TCP/53709\",\"TCP/53714\"]", "pcap_library": {"file_location": "pcap_library_998000748.pcap", "filesize": "259524 bytes", "md5sum": "f13fe52c0103c8b4de162b0d7dc05f4d", "orig_file_name": "pcap_library_998000748.pcap", "packet_count": 660}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8140\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8140", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "8db0874f-5b6f-4455-9777-fab6a1a5f401": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "ver": 4, "vid": "8001342", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 TrickBot Ryuk 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 19:18:10", "updated_at": "2025-08-07 19:18:10", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "8db0874f-5b6f-4455-9777-fab6a1a5f401", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53823\"]", "pcap_library": {"file_location": "pcap_library_998000747.pcap", "filesize": "212180 bytes", "md5sum": "3aa3bb5286120fcc41553697581ca858", "orig_file_name": "pcap_library_998000747.pcap", "packet_count": 226}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8115\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8115", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "c9264058-b10a-4bfe-bb67-422c1ee392c8": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "ver": 4, "vid": "8001341", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 TrickBot 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 19:16:44", "updated_at": "2025-08-07 19:16:44", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "c9264058-b10a-4bfe-bb67-422c1ee392c8", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53914\"]", "pcap_library": {"file_location": "pcap_library_998000746.pcap", "filesize": "221088 bytes", "md5sum": "591c25fac637be51cb57c5d53eaf77b5", "orig_file_name": "pcap_library_998000746.pcap", "packet_count": 343}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8116\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8116", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "7878dd32-b6d8-4599-999b-c0bea288c5dd": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "ver": 4, "vid": "8001340", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Ursnif IcedID 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 19:15:03", "updated_at": "2025-08-07 19:15:03", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "7878dd32-b6d8-4599-999b-c0bea288c5dd", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/53995\"]", "pcap_library": {"file_location": "pcap_library_998000745.pcap", "filesize": "212738 bytes", "md5sum": "a7845097754def09f1131d11fd01a994", "orig_file_name": "pcap_library_998000745.pcap", "packet_count": 307}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8117\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8117", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "23c8f71b-267b-4165-abff-b7dd4ea128b7": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "ver": 4, "vid": "8001339", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Ursnif 银行木马流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 19:12:29", "updated_at": "2025-08-07 19:12:29", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "23c8f71b-267b-4165-abff-b7dd4ea128b7", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54082\"]", "pcap_library": {"file_location": "pcap_library_998000744.pcap", "filesize": "213433 bytes", "md5sum": "026eaa6e6b3e252516b4350e5acebe7f", "orig_file_name": "pcap_library_998000744.pcap", "packet_count": 279}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8118\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8118", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "fde6e5af-1ec9-4163-96f5-48843b3fcba3": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "ver": 4, "vid": "8001338", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Xbash 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 19:08:38", "updated_at": "2025-08-07 19:08:38", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "fde6e5af-1ec9-4163-96f5-48843b3fcba3", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54180\"]", "pcap_library": {"file_location": "pcap_library_998000743.pcap", "filesize": "193020 bytes", "md5sum": "c55774256225091aa3e9bc421a5db3b1", "orig_file_name": "pcap_library_998000743.pcap", "packet_count": 258}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8119\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8119", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "ea9d8f88-013e-4e6c-8c71-b62920b92784": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "ver": 4, "vid": "8001337", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 YouTube 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 19:06:36", "updated_at": "2025-08-07 19:06:36", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "ea9d8f88-013e-4e6c-8c71-b62920b92784", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54294\",\"TCP/54332\",\"TCP/54290\",\"TCP/54303\",\"TCP/54314\",\"TCP/54327\",\"TCP/54329\",\"TCP/54340\",\"TCP/54292\",\"TCP/54293\",\"TCP/54252\",\"TCP/54275\",\"TCP/54285\",\"TCP/54312\",\"TCP/54333\",\"TCP/54250\",\"TCP/54261\",\"TCP/54271\",\"TCP/54274\",\"TCP/54307\",\"TCP/54334\",\"TCP/54337\",\"TCP/54296\",\"TCP/54302\",\"TCP/54259\",\"TCP/54268\",\"TCP/54284\",\"TCP/54335\",\"TCP/54251\",\"TCP/54265\",\"TCP/54280\",\"TCP/54324\",\"TCP/54336\",\"TCP/54270\",\"TCP/54254\",\"TCP/54283\",\"TCP/54326\",\"TCP/54331\",\"TCP/54273\",\"TCP/54305\",\"TCP/54320\",\"TCP/54287\",\"TCP/54298\",\"TCP/54308\",\"TCP/54315\",\"TCP/54257\",\"TCP/54295\",\"TCP/54304\",\"TCP/54313\",\"TCP/54317\",\"TCP/54255\",\"TCP/54306\",\"TCP/54253\",\"TCP/54263\",\"TCP/54266\",\"TCP/54278\",\"TCP/54309\",\"TCP/54310\",\"TCP/54318\",\"TCP/54256\",\"TCP/54291\",\"TCP/54319\",\"TCP/54338\",\"TCP/54260\",\"TCP/54276\",\"TCP/54281\",\"TCP/54286\",\"TCP/54301\",\"TCP/54267\",\"TCP/54258\",\"TCP/54288\",\"TCP/54297\",\"TCP/54299\",\"TCP/54300\",\"TCP/54269\",\"TCP/54279\",\"TCP/54289\",\"TCP/54321\",\"TCP/54277\",\"TCP/54282\",\"TCP/54322\",\"TCP/54262\",\"TCP/54316\",\"TCP/54272\",\"TCP/54264\",\"TCP/54325\",\"TCP/54328\",\"TCP/54330\",\"TCP/54342\",\"TCP/54339\",\"TCP/54311\",\"TCP/54341\",\"TCP/54323\"]", "pcap_library": {"file_location": "pcap_library_998000742.pcap", "filesize": "431980 bytes", "md5sum": "63ab9de05ba92895d6a86bb7dc4a5439", "orig_file_name": "pcap_library_998000742.pcap", "packet_count": 1163}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8134\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8134", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "8043c242-64a1-47cc-8372-6d1321d60235": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "ver": 4, "vid": "8001336", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Zloader 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "2025-08-07 18:01:15", "updated_at": "2025-08-07 18:01:15", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "8043c242-64a1-47cc-8372-6d1321d60235", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54436\",\"TCP/54452\",\"TCP/54457\",\"TCP/54467\",\"TCP/54415\",\"TCP/54429\",\"TCP/54434\",\"TCP/54437\",\"TCP/54447\",\"TCP/54448\",\"TCP/54458\",\"TCP/54475\",\"TCP/54417\",\"TCP/54418\",\"TCP/54435\",\"TCP/54464\",\"TCP/54469\",\"TCP/54474\",\"TCP/54420\",\"TCP/54421\",\"TCP/54438\",\"TCP/54416\",\"TCP/54430\",\"TCP/54453\",\"TCP/54471\",\"TCP/54473\",\"TCP/54426\",\"TCP/54427\",\"TCP/54470\",\"TCP/54432\",\"TCP/54439\",\"TCP/54444\",\"TCP/54466\",\"TCP/54476\",\"TCP/54414\",\"TCP/54433\",\"TCP/54449\",\"TCP/54465\",\"TCP/54442\",\"TCP/54424\",\"TCP/54446\",\"TCP/54455\",\"TCP/54423\",\"TCP/54477\",\"TCP/54478\",\"TCP/54411\",\"TCP/54440\",\"TCP/54461\",\"TCP/54412\",\"TCP/54425\",\"TCP/54456\",\"TCP/54428\",\"TCP/54431\",\"TCP/54443\",\"TCP/54454\",\"TCP/54480\",\"TCP/54462\",\"TCP/54413\",\"TCP/54481\",\"TCP/54468\",\"TCP/54445\",\"TCP/54451\",\"TCP/54459\",\"TCP/54479\",\"TCP/54450\",\"TCP/54460\",\"TCP/54419\",\"TCP/54463\",\"TCP/54441\",\"TCP/54410\",\"TCP/54472\",\"TCP/54422\"]", "pcap_library": {"file_location": "pcap_library_998000741.pcap", "filesize": "243172 bytes", "md5sum": "7bf615dc8c135bf1b1445160a6628541", "orig_file_name": "pcap_library_998000741.pcap", "packet_count": 816}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8120\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8120", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "5fbd3d5a-909d-442d-9fe9-75d3233e28e3": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "ver": 4, "vid": "8001335", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Brute Ratel C4 v1.7.4 C&C 回连心跳通信流量，HTTP，C&C通信，模拟 Zoom 流量", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Brute Ratel C4 感染的设备 C&C 回连心跳通信流量。Brute Ratel C4 (BRc4) 是一个较新的网络渗透测试和对抗模拟工具，由网络安全研究员和专业渗透测试员设计。它被创建来提供一种更隐蔽的方式来模拟攻击者的行为，并且旨在通过使用低侦察和低签名的技术来规避现代防御机制，如防病毒软件和入侵检测系统。该软件允许攻击者在受害机器上部署名为信标的代理，信标通过随机生成回连信标向 C&C 服务器报告受害机器的信息。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-07 17:58:50", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "5fbd3d5a-909d-442d-9fe9-75d3233e28e3", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/54594\",\"TCP/54600\",\"TCP/54585\",\"TCP/54590\",\"TCP/54593\",\"TCP/54614\",\"TCP/54617\",\"TCP/54622\",\"TCP/54583\",\"TCP/54586\",\"TCP/54592\",\"TCP/54605\",\"TCP/54615\",\"TCP/54625\",\"TCP/54587\",\"TCP/54591\",\"TCP/54602\",\"TCP/54575\",\"TCP/54603\",\"TCP/54606\",\"TCP/54620\",\"TCP/54579\",\"TCP/54604\",\"TCP/54618\",\"TCP/54619\",\"TCP/54624\",\"TCP/54596\",\"TCP/54611\",\"TCP/54576\",\"TCP/54589\",\"TCP/54607\",\"TCP/54608\",\"TCP/54612\",\"TCP/54613\",\"TCP/54621\",\"TCP/54610\",\"TCP/54598\",\"TCP/54599\",\"TCP/54601\",\"TCP/54577\",\"TCP/54578\",\"TCP/54581\",\"TCP/54588\",\"TCP/54609\",\"TCP/54580\",\"TCP/54582\",\"TCP/54623\",\"TCP/54595\",\"TCP/54584\",\"TCP/54597\"]", "pcap_library": {"file_location": "pcap_library_998000740.pcap", "filesize": "213088 bytes", "md5sum": "9bf250bdb8e561512bc4681784b51704", "orig_file_name": "pcap_library_998000740.pcap", "packet_count": 590}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/8135\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8135", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "8acd0502-757f-4612-8c1f-31cfd686085f": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "ver": 4, "vid": "8001334", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - VShell v4.9.3 C&C 回连心跳通信流量，WS，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 VShell C&C 感染的设备 C&C 回连心跳通信流量。VShell 是一款以 Golang 开发的开源跨平台 Remote Access Trojan（RAT），支持 Windows、Linux 与 macOS 系统。它具备远程命令执行、文件上传/下载与系统信息收集等基本后渗透功能，同时支持 WebSockets 加密通信，能够实现实时、隐蔽且难以被检测的 C2 通道。该工具采用 文件无痕（fileless）执行方式，可驻留于内存中，并通过内存执行逃避传统防护机制 。其界面采用 Web 控制台，设计简洁易用。", "created_at": "2025-08-07 17:46:31", "updated_at": "2025-08-07 17:46:31", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "8acd0502-757f-4612-8c1f-31cfd686085f", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "**************", "attacker_ports": "[\"TCP/50801\",\"TCP/50802\",\"TCP/50803\",\"TCP/50800\"]", "pcap_library": {"file_location": "pcap_library_998000739.pcap", "filesize": "5729409 bytes", "md5sum": "07a224d3e8f975e809cc98f00426b4c8", "orig_file_name": "pcap_library_998000739.pcap", "packet_count": 7264}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "*************", "target_ports": "[\"TCP/8085\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "8085", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "90669af4-87f3-4ab2-bcd1-a4860c98bf47": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "ver": 4, "vid": "8001332", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Supershell C&C 回连心跳通信流量，WS，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Supershell C&C 感染的设备 C&C 回连心跳通信流量。Supershell 是一款基于 Web 的远控（C2）平台，由 Python 编写，支持通过反向 SSH 隧道提供跨平台（Windows、Linux、Android）交互式 Shell 和 Payload 生成功能。其特性包括团队协作管理、自动重连、文件管理、内存注入、服务安装等，部署便捷（支持 Docker 部署）。", "created_at": "2025-08-07 17:31:06", "updated_at": "2025-08-07 17:31:06", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "90669af4-87f3-4ab2-bcd1-a4860c98bf47", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/51782\"]", "pcap_library": {"file_location": "pcap_library_998000735.pcap", "filesize": "350408 bytes", "md5sum": "d230d72c37bd478e687908ebe53f74dc", "orig_file_name": "pcap_library_998000735.pcap", "packet_count": 2388}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/3232\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "3232", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "e5bd8194-5002-4dc8-a1d9-fbecac6f103c": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "ver": 4, "vid": "8001331", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Supershell C&C 回连心跳通信流量，WSS，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Supershell C&C 感染的设备 C&C 回连心跳通信流量。Supershell 是一款基于 Web 的远控（C2）平台，由 Python 编写，支持通过反向 SSH 隧道提供跨平台（Windows、Linux、Android）交互式 Shell 和 Payload 生成功能。其特性包括团队协作管理、自动重连、文件管理、内存注入、服务安装等，部署便捷（支持 Docker 部署）。", "created_at": "2025-08-07 17:29:33", "updated_at": "2025-08-07 17:29:33", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "e5bd8194-5002-4dc8-a1d9-fbecac6f103c", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/51783\"]", "pcap_library": {"file_location": "pcap_library_998000734.pcap", "filesize": "305344 bytes", "md5sum": "234a37df0d2a8296459430beba7319a1", "orig_file_name": "pcap_library_998000734.pcap", "packet_count": 2002}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/3232\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "3232", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "49ad89fb-98e6-4fd9-80ec-707424358d16": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "ver": 4, "vid": "8001330", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Supershell C&C 回连心跳通信流量，TLS，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Supershell C&C 感染的设备 C&C 回连心跳通信流量。Supershell 是一款基于 Web 的远控（C2）平台，由 Python 编写，支持通过反向 SSH 隧道提供跨平台（Windows、Linux、Android）交互式 Shell 和 Payload 生成功能。其特性包括团队协作管理、自动重连、文件管理、内存注入、服务安装等，部署便捷（支持 Docker 部署）。", "created_at": "2025-08-07 17:27:42", "updated_at": "2025-08-07 17:27:42", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "49ad89fb-98e6-4fd9-80ec-707424358d16", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/51784\"]", "pcap_library": {"file_location": "pcap_library_998000733.pcap", "filesize": "300053 bytes", "md5sum": "e7c4a25c807fde68aa0634488293db78", "orig_file_name": "pcap_library_998000733.pcap", "packet_count": 1923}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/3232\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "3232", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "5190bac4-ccf6-4cfc-822e-55f20a77cee7": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "ver": 4, "vid": "8001329", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - Supershell C&C 回连心跳通信流量，SSH，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 Supershell C&C 感染的设备 C&C 回连心跳通信流量。Supershell 是一款基于 Web 的远控（C2）平台，由 Python 编写，支持通过反向 SSH 隧道提供跨平台（Windows、Linux、Android）交互式 Shell 和 Payload 生成功能。其特性包括团队协作管理、自动重连、文件管理、内存注入、服务安装等，部署便捷（支持 Docker 部署）。", "created_at": "2025-08-07 17:24:01", "updated_at": "2025-08-07 17:24:01", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "5190bac4-ccf6-4cfc-822e-55f20a77cee7", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/51786\"]", "pcap_library": {"file_location": "pcap_library_998000732.pcap", "filesize": "225021 bytes", "md5sum": "55e44ffa0332d7ebfa1e94e2ed7aa0cb", "orig_file_name": "pcap_library_998000732.pcap", "packet_count": 1704}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/3232\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "3232", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "ver": 4, "vid": "8001328", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - AdaptixC2 C&C 回连心跳通信流量，TCP，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 AdaptixC2 C&C 感染的设备 C&C 回连心跳通信流量。AdaptixC2 是一款可扩展的后渗透与对抗仿真框架，专为渗透测试人员设计。其核心为 Go 语言编写的服务器端，配合使用 C++ + Qt 开发的跨平台 GUI 客户端，可运行于 Windows、Linux 和 macOS 环境该框架采用服务器/客户端架构，支持多用户协同操作，并且所有通信均经过加密，保障安全性。其架构高度模块化，支持以 Extender（扩展插件） 形式的监听器和代理，支持 HTTP/S、SMB、TCP、Gopher 等多种协议。", "created_at": "2025-08-07 17:17:27", "updated_at": "2025-08-07 17:17:27", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "738d1c9e-cd6a-46e1-945e-dfe3ffa363d0", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/52101\"]", "pcap_library": {"file_location": "pcap_library_998000731.pcap", "filesize": "25663 bytes", "md5sum": "b5c38888d70108f9d8b589a2fe118d27", "orig_file_name": "pcap_library_998000731.pcap", "packet_count": 55}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/4322\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "4322", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "ff5ce781-e61b-49bf-ac28-55b9364de3b2": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "ver": 4, "vid": "8001327", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - AdaptixC2 C&C 回连心跳通信流量，MTLS，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 AdaptixC2 C&C 感染的设备 C&C 回连心跳通信流量。AdaptixC2 是一款可扩展的后渗透与对抗仿真框架，专为渗透测试人员设计。其核心为 Go 语言编写的服务器端，配合使用 C++ + Qt 开发的跨平台 GUI 客户端，可运行于 Windows、Linux 和 macOS 环境该框架采用服务器/客户端架构，支持多用户协同操作，并且所有通信均经过加密，保障安全性。其架构高度模块化，支持以 Extender（扩展插件） 形式的监听器和代理，支持 HTTP/S、SMB、TCP、Gopher 等多种协议。", "created_at": "2025-08-07 17:16:12", "updated_at": "2025-08-07 17:16:12", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "ff5ce781-e61b-49bf-ac28-55b9364de3b2", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Protocols", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/52104\"]", "pcap_library": {"file_location": "pcap_library_998000730.pcap", "filesize": "31769 bytes", "md5sum": "9852c0423745020705667b8dc4edd780", "orig_file_name": "pcap_library_998000730.pcap", "packet_count": 66}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/5423\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "5423", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b2b04641-b584-4044-999a-ace67a4e4501": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "ver": 4, "vid": "8001326", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - AdaptixC2 C&C 回连心跳通信流量，HTTPS，POST，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 AdaptixC2 C&C 感染的设备 C&C 回连心跳通信流量。AdaptixC2 是一款可扩展的后渗透与对抗仿真框架，专为渗透测试人员设计。其核心为 Go 语言编写的服务器端，配合使用 C++ + Qt 开发的跨平台 GUI 客户端，可运行于 Windows、Linux 和 macOS 环境该框架采用服务器/客户端架构，支持多用户协同操作，并且所有通信均经过加密，保障安全性。其架构高度模块化，支持以 Extender（扩展插件） 形式的监听器和代理，支持 HTTP/S、SMB、TCP、Gopher 等多种协议。", "created_at": "2025-08-07 17:14:41", "updated_at": "2025-08-07 17:14:41", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b2b04641-b584-4044-999a-ace67a4e4501", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/52006\",\"TCP/52013\",\"TCP/51991\",\"TCP/51999\",\"TCP/52008\",\"TCP/52011\",\"TCP/52005\",\"TCP/52014\",\"TCP/51987\",\"TCP/52016\",\"TCP/52017\",\"TCP/52018\",\"TCP/52009\",\"TCP/51994\",\"TCP/51997\",\"TCP/51986\",\"TCP/51988\",\"TCP/51993\",\"TCP/52002\",\"TCP/52003\",\"TCP/52015\",\"TCP/52012\",\"TCP/51992\",\"TCP/51990\",\"TCP/51998\",\"TCP/51985\",\"TCP/51996\",\"TCP/52000\",\"TCP/52010\",\"TCP/52001\",\"TCP/51995\",\"TCP/52004\",\"TCP/52007\"]", "pcap_library": {"file_location": "pcap_library_998000729.pcap", "filesize": "112684 bytes", "md5sum": "e15582b19536efac5e616d11b506be4c", "orig_file_name": "pcap_library_998000729.pcap", "packet_count": 677}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/443\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "443", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b1c1657d-0d82-48ba-ae06-2ea4615db2ec": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "ver": 4, "vid": "8001325", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - AdaptixC2 C&C 回连心跳通信流量，HTTPS，GET，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 AdaptixC2 C&C 感染的设备 C&C 回连心跳通信流量。AdaptixC2 是一款可扩展的后渗透与对抗仿真框架，专为渗透测试人员设计。其核心为 Go 语言编写的服务器端，配合使用 C++ + Qt 开发的跨平台 GUI 客户端，可运行于 Windows、Linux 和 macOS 环境该框架采用服务器/客户端架构，支持多用户协同操作，并且所有通信均经过加密，保障安全性。其架构高度模块化，支持以 Extender（扩展插件） 形式的监听器和代理，支持 HTTP/S、SMB、TCP、Gopher 等多种协议。", "created_at": "2025-08-07 17:13:56", "updated_at": "2025-08-07 17:13:56", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b1c1657d-0d82-48ba-ae06-2ea4615db2ec", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/52077\",\"TCP/52079\",\"TCP/52088\",\"TCP/52038\",\"TCP/52040\",\"TCP/52056\",\"TCP/52057\",\"TCP/52061\",\"TCP/52066\",\"TCP/52067\",\"TCP/52068\",\"TCP/52042\",\"TCP/52045\",\"TCP/52049\",\"TCP/52053\",\"TCP/52063\",\"TCP/52074\",\"TCP/52078\",\"TCP/52036\",\"TCP/52044\",\"TCP/52046\",\"TCP/52076\",\"TCP/52082\",\"TCP/52039\",\"TCP/52051\",\"TCP/52065\",\"TCP/52069\",\"TCP/52041\",\"TCP/52055\",\"TCP/52071\",\"TCP/52072\",\"TCP/52073\",\"TCP/52075\",\"TCP/52081\",\"TCP/52083\",\"TCP/52052\",\"TCP/52059\",\"TCP/52084\",\"TCP/52085\",\"TCP/52043\",\"TCP/52060\",\"TCP/52080\",\"TCP/52037\",\"TCP/52048\",\"TCP/52047\",\"TCP/52062\",\"TCP/52064\",\"TCP/52035\",\"TCP/52050\",\"TCP/52054\",\"TCP/52034\",\"TCP/52086\",\"TCP/52087\",\"TCP/52058\"]", "pcap_library": {"file_location": "pcap_library_998000728.pcap", "filesize": "169018 bytes", "md5sum": "04a719fc609c752d8b78dc3cb8768fd7", "orig_file_name": "pcap_library_998000728.pcap", "packet_count": 1100}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/444\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "444", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b1bd6260-effb-4154-ba11-5d78272bb978": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "ver": 4, "vid": "8001324", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - AdaptixC2 C&C 回连心跳通信流量，HTTP，POST，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 AdaptixC2 C&C 感染的设备 C&C 回连心跳通信流量。AdaptixC2 是一款可扩展的后渗透与对抗仿真框架，专为渗透测试人员设计。其核心为 Go 语言编写的服务器端，配合使用 C++ + Qt 开发的跨平台 GUI 客户端，可运行于 Windows、Linux 和 macOS 环境该框架采用服务器/客户端架构，支持多用户协同操作，并且所有通信均经过加密，保障安全性。其架构高度模块化，支持以 Extender（扩展插件） 形式的监听器和代理，支持 HTTP/S、SMB、TCP、Gopher 等多种协议。", "created_at": "2025-08-07 17:12:53", "updated_at": "2025-08-07 17:12:53", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b1bd6260-effb-4154-ba11-5d78272bb978", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/51874\"]", "pcap_library": {"file_location": "pcap_library_998000727.pcap", "filesize": "43722 bytes", "md5sum": "cce1b38a351a0ab004450a6958e99984", "orig_file_name": "pcap_library_998000727.pcap", "packet_count": 149}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/80\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "80", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}, "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4": {"newStyle": false, "enable": true, "action_type": "pcap", "os": "any", "must_sandbox": false, "is_endpoint": false, "severity": 4, "uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "ver": 4, "vid": "8001323", "timeout_ms": 10000, "notes": "塞讯验证建议在内部/受信任的安全区域中选择源验证机器人，在外部/不受信任的区域中选择目标验证机器人。", "name": "命令与控制 - AdaptixC2 C&C 回连心跳通信流量，HTTP，GET，C&C通信", "action_mitigation": "", "metadata_version": 18, "desc": "此验证动作还原了受 AdaptixC2 C&C 感染的设备 C&C 回连心跳通信流量。AdaptixC2 是一款可扩展的后渗透与对抗仿真框架，专为渗透测试人员设计。其核心为 Go 语言编写的服务器端，配合使用 C++ + Qt 开发的跨平台 GUI 客户端，可运行于 Windows、Linux 和 macOS 环境该框架采用服务器/客户端架构，支持多用户协同操作，并且所有通信均经过加密，保障安全性。其架构高度模块化，支持以 Extender（扩展插件） 形式的监听器和代理，支持 HTTP/S、SMB、TCP、Gopher 等多种协议。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-08-07 17:11:55", "is_apt": false, "is_self": true, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "brt_version": "", "common_detection_alerts": [], "sectech_logo": null, "tags": [{"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "os", "tag": "ANY", "tag_en": "ANY", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "mitre_techniques", "tag": "T1008 - 回连通道", "tag_en": "T1008 - Fallback Channels", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "mitre_tactics", "tag": "TA0011 - 命令与控制", "tag_en": "TA0011 - Command and Control", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "control", "tag": "NTA/NDR", "tag_en": "NTA/NDR", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "control", "tag": "NGFW", "tag_en": "NGFW", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "control", "tag": "Proxy", "tag_en": "Proxy", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "control", "tag": "IDS/IPS", "tag_en": "IDS/IPS", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "b9896cc3-88f4-4940-b20a-5dcf39a3c6d4", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:External:Untrusted", "tag_en": "Src:Internal:Trusted+Dst:External:Untrusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": null, "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>HTTP(S)", "Attacker Location>General-Location", "Behavior Type>General-C&C Comm", "OS/Platform>General-OS/Platform", "Stage of Attack>Command & Control"], "captive_dns_action": null, "captive_ioc_url_action": null, "captive_ioc_ip_action": null, "dns_query_action": null, "email_action": null, "file_transfer_action": null, "exploit_action": null, "host_cli_action": null, "web_action": null, "pcap_action": {"attacker_ip": "*************", "attacker_ports": "[\"TCP/51982\"]", "pcap_library": {"file_location": "pcap_library_998000726.pcap", "filesize": "47375 bytes", "md5sum": "f0984fe084592d9201f40b68bcd42ddd", "orig_file_name": "pcap_library_998000726.pcap", "packet_count": 145}, "protos": "[\"TCP\"]", "start_packet": 1, "target": "**************", "target_ports": "[\"TCP/81\"]"}, "port_scan_action": null, "required_parms": [], "is_http": true, "args": {"clear_encoding_header": "false", "host_header_update": "on", "target_port": "81", "use_https_connection": "false"}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}