{"action": {"name": "(自定义规则)主机命令行 - Delete Shadow Copy via WMI Objects - 10", "desc": "In this action, shadow copies are deleted via WMI objects.[blackcat_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe -c \"Get-CimInstance Win32_ShadowCopy | Remove-CimInstance\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Impact", "ATT&CK:T1490"], "mitre_mitigation": ["ATT&CK:M1018", "ATT&CK:M1053", "ATT&CK:M1028"]}}, "picus_id": 21278, "sigma": {"name": "Sigma", "rules": [{"ruleName": "Volume Shadow Copy Deletion via PowerShell Cmdlet", "ruleId": "8508", "description": "Detects the attempt to delete volume shadow copies via PowerShell cmdlet. This method is mostly utilized by ransomwares such as Netwalker to inhibit system recovery.", "releaseDate": 1591574400000, "updateDate": 1663632000000, "author": "Picus Security", "severity": "high", "reportScore": 0, "query": "title: Volume Shadow Copy Deletion via PowerShell Cmdlet\nstatus: stable\ndescription: Detects the attempt to delete volume shadow copies via PowerShell cmdlet. This method is mostly utilized by ransomwares such as <PERSON><PERSON><PERSON> to inhibit system recovery.\nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/techniques/T1490/\n    - https://attack.mitre.org/tactics/TA0040\n    - https://blog.trendmicro.com/trendlabs-security-intelligence/netwalker-fileless-ransomware-injected-via-reflective-loading/\nlogsource:\n    product: windows\n    service: powershell/operational\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging'\ndetection:\n    selection:\n        EventID: 4103\n        Message|contains:\n            - 'Win32_Shadowcopy'\n    selection1:\n         Message|contains:\n            - 'Remove'\n            - 'Delete'\n    condition: selection and selection1\nfalsepositives: \n    - Administrative tools/scripts\nlevel: high\ntags:\n    - attack.impact\n    - attack.t1490\n    - attack.ta0040", "logSource": {"productName": "windows", "service": "powershell/operational", "policies": ["Requirements: Group Policy : Computer Configuration\\Administrative Templates\\Windows Components\\Windows PowerShell\\Turn On Module Logging"]}, "falsePositives": ["Administrative tools/scripts"], "mitre": [{"name": "Inhibit System Recovery", "mitreId": "T1490", "url": "https://attack.mitre.org/techniques/T1490/", "type": "technique"}]}]}, "uuid": "2d7f1ee4-c49d-44c8-b4ec-ef0156106167", "sigma_device_query": ""}