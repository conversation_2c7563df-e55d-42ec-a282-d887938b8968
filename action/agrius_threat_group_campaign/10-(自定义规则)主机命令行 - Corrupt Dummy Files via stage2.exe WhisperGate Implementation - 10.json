{"action": {"name": "(自定义规则)主机命令行 - Corrupt Dummy Files via stage2.exe WhisperGate Implementation - 10", "desc": "In this action, an attacker is trying to corrupt dummy files by overwriting the first 1 MiB of each file with 0xCC bytes.[agrius_threat_group_campaign]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "stage2.exe", "file_owner": "system", "file_transfer_library": 998000479, "randomize_file": null, "file_name": "stage2.exe"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "dummy.zip", "file_owner": "system", "file_transfer_library": 998000480, "randomize_file": null, "file_name": "dummy.zip"}, {"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "dummy.xls", "file_owner": "system", "file_transfer_library": 998000481, "randomize_file": null, "file_name": "dummy.xls"}], "steps": [{"step_order": 1, "command": "cmd.exe /c mkdir \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\test\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 1, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\stage2.exe overwrite \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\test\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "Overwrote", "check_events": true, "event_logs": null, "sleep": 1, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 0, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}}, "tags": [{"tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "tag_type": "os", "type": 1}, {"tag": "Windows:11", "tag_en": "Windows:11", "tag_type": "os", "type": 1}, {"tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "tag_type": "os", "type": 1}, {"tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "tag_type": "os", "type": 1}, {"tag": "Windows:10", "tag_en": "Windows:10", "tag_type": "os", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"tag_type": "mitre_tactics", "tag": "TA0040 - 破坏影响", "tag_en": "TA0040 - Impact", "type": 1}, {"tag_type": "mitre_techniques", "tag": "T1485 - 数据销毁", "tag_en": "T1485 - Data Destruction", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1053", "tag_en": "ATT&CK:M1053", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1032", "tag_en": "ATT&CK:M1032", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1018", "tag_en": "ATT&CK:M1018", "type": 1}], "picus_id": 32456, "sigma": "", "uuid": "621b88f3-bd75-4ac9-bd4d-daee80725437"}