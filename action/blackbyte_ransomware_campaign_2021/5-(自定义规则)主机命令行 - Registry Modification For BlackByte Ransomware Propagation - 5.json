{"action": {"name": "(自定义规则)主机命令行 - Registry Modification For BlackByte Ransomware Propagation - 5", "desc": "In this action, some registry modifications are made to elevate local privilege, connect mapped drives, enable long paths for the preparation of BlackByte Ransomware propagation.[blackbyte_ransomware_campaign_2021]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "reg.exe add \"HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\" /v LocalAccountTokenFilterPolicy /t REG_DWORD /d 1 /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "reg.exe add \"HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System\" /v EnableLinkedConnections /t REG_DWORD /d 1 /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "reg.exe add \"HKLM\\SYSTEM\\CurrentControlSet\\Control\\FileSystem\" /v LongPathsEnabled /t REG_DWORD /d 1 /f", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Defense Evasion", "ATT&CK:T1112"], "mitre_mitigation": ["ATT&CK:M1024"]}}, "picus_id": 21282, "sigma": {"name": "Sigma", "rules": [{"ruleName": "BlackByte Ransomware Registry Modification", "ruleId": "3255", "description": "Detects the attempt to registry modification to escalate privileges and begin setting the stage for lateral movement and encryption. This technique is utilized for defense evasion by the BlackByte Ransomware.", "releaseDate": 1663718400000, "updateDate": 1663718400000, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: BlackByte Ransomware Registry Modification\nstatus: stable\ndescription: Detects the attempt to registry modification to escalate privileges and begin setting the stage for lateral movement and encryption. This technique is utilized for defense evasion by the BlackByte Ransomware. \nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/tactics/TA0005/\n    - https://attack.mitre.org/techniques/T1112/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\reg.exe'\n    selection1:\n        ProcessCommandLine|contains|all:\n            - '\\SYSTEM\\CurrentControlSet\\Control\\FileSystem'\n            - 'LongPathsEnabled'\n    selection2:\n        ProcessCommandLine|contains|all:\n            - '\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System'\n            - 'LocalAccountTokenFilterPolicy'\n    selection3:\n        ProcessCommandLine|contains|all:\n            - '\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System'\n            - 'EnableLinkedConnections'\n    condition: selection and (selection1 or selection2 or selection3)\nfalsepositives:\n    - Unknown\nlevel: medium\ntags:\n    - attack.defense_evasion\n    - attack.ta0005\n    - attack.t1112", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Unknown"], "mitre": [{"name": "Modify Registry", "mitreId": "T1112", "url": "https://attack.mitre.org/techniques/T1112/", "type": "technique"}]}]}, "uuid": "0cde840a-921d-4f0d-a6a2-bb921839a2c2", "sigma_device_query": "title: BlackByte Ransomware Registry Modification\nstatus: stable\ndescription: Detects the attempt to registry modification to escalate privileges and begin setting the stage for lateral movement and encryption. This technique is utilized for defense evasion by the BlackByte Ransomware. \nauthor: Picus Security\nreferences:\n    - https://attack.mitre.org/tactics/TA0005/\n    - https://attack.mitre.org/techniques/T1112/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\reg.exe'\n    selection1:\n        ProcessCommandLine|contains|all:\n            - '\\SYSTEM\\CurrentControlSet\\Control\\FileSystem'\n            - 'LongPathsEnabled'\n    selection2:\n        ProcessCommandLine|contains|all:\n            - '\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System'\n            - 'LocalAccountTokenFilterPolicy'\n    selection3:\n        ProcessCommandLine|contains|all:\n            - '\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System'\n            - 'EnableLinkedConnections'\n    condition: selection and (selection1 or selection2 or selection3)\nfalsepositives:\n    - Unknown\nlevel: medium\ntags:\n    - attack.defense_evasion\n    - attack.ta0005\n    - attack.t1112"}