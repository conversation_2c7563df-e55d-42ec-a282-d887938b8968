var shell = new ActiveXObject('WScript.Shell');
var wmi = GetObject ("WinMgmts:{impersonationLevel=impersonate}!//./root/cimv2");

function get_active_directory_information() { 
	var adobj = new ActiveXObject('ADSystemInfo'); 
	return adobj.ComputerName; 
} 
function get_env_var (name) {
	return shell.ExpandEnvironmentStrings(name); 
}

function get_system_information () { 
	var result=[];
	try {
		WScript.Echo("TEST");
		result.push('username***' + get_env_var('%USERNAME%')); 
		
		WScript.Echo(result[1]);
		result.push('hostname***' + get_env_var('%COMPUTERNAME%')); 
		var ad = get_active_directory_information(); 
		if(ad) { 
			result.push('adinformation***' + ad); 
		}else{ 
			result.push('adinformation***no_ad');
		}
		var csRequest = wmi.ExecQuery('Select * from Win32_ComputerSystem'); 
		var csItems = new Enumerator(csRequest); 
		for (;!csItems.atEnd(); csItems.moveNext()) { 
		result.push('pc_domain***' + csItems.item().Domain); 
		result.push('pc_dns_host_name***' + csItems.item().DNSHostName); 
		result.push('pc_model***' + csItems.item().Model);
		}
	}catch(e) { 
		result.push('error0***code_error'); 
	}
	var osRequest = wmi.ExecQuery('select * from win32_OperatingSystem');
	WScript.Echo(result);
}	

get_system_information();
