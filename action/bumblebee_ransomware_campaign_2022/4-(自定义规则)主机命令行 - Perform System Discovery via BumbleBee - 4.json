{"action": {"name": "(自定义规则)主机命令行 - Perform System Discovery via BumbleBee - 4", "desc": "In this action, an attacker is trying to mimic the discovery steps of BumbleBee malware.[bumblebee_ransomware_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 2, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "bumblebeeDiscovery.bat", "file_owner": "system", "file_transfer_library": 998002332, "randomize_file": null, "file_name": "bumblebeeDiscovery.bat"}], "steps": [{"step_order": 1, "command": "cmd.exe /c \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\bumblebeeDiscovery.bat\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1082"], "mitre_mitigation": []}}, "picus_id": 23017, "uuid": "4afc6bc2-3214-4338-be26-044bc86fdbf1", "sigma": {"name": "Sigma", "rules": [{"ruleName": "System Information Discovery via Native Tools", "ruleId": "7266", "description": "Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.", "releaseDate": 1670284800000, "updateDate": 1681430400000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: System Information Discovery via Native Tools\nstatus: experimental\ndescription: Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/techniques/T1016/\n   - https://attack.mitre.org/techniques/T1033/\n   - https://attack.mitre.org/techniques/T1049/\n   - https://attack.mitre.org/techniques/T1087/\n   - https://attack.mitre.org/techniques/T1082/\n   - https://attack.mitre.org/software/S0099/\n   - https://attack.mitre.org/software/S0100/\n   - https://attack.mitre.org/software/S0103/\n   - https://attack.mitre.org/software/S0039/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/arp\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/whoami\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/ipconfig\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/route_ws2008\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/netstat\n   - https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-r2-and-2012/cc725622(v=ws.11)\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/getmac\nlogsource:\n    product: windows\n    service: security\n    definition: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName|endswith: \n                - '\\whoami.exe'\n                - '\\arp.exe'\n                - '\\ipconfig.exe'\n                - '\\route.exe'\n                - '\\netstat.exe'\n                - '\\net.exe' \n                - '\\getmac.exe'\n                - '\\nbtstat.exe'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.ta0007\n    - attack.t1016\n    - attack.t1033\n    - attack.t1049\n    - attack.t1087\n    - attack.t1082", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Configuration Discovery", "mitreId": "T1016", "url": "https://attack.mitre.org/techniques/T1016/", "type": "technique"}, {"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}, {"name": "System Network Connections Discovery", "mitreId": "T1049", "url": "https://attack.mitre.org/techniques/T1049/", "type": "technique"}, {"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}, {"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}]}, {"ruleName": "Remote System Discovery via Net Tool", "ruleId": "8755", "description": "Detects the attempt to collect information about domains, computers or resources on the network via net tool. This technique is commonly utilized for discovery as Turla and APT1 Group’s usage in its campaigns.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Remote System Discovery via Net Tool\nstatus: stable\ndescription: Detects the attempt to collect information about domains, computers or resources on the network via net tool. This technique is commonly utilized for discovery as Turla and APT1 Group’s usage in its campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1018/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0006/\n   - https://attack.mitre.org/groups/G0010/\n   - https://ss64.com/nt/net-view.html\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: \n          - '*\\net.exe'\n          - '*\\net1.exe'\n        ProcessCommandLine: '*view*'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1018\n    - attack.ta0007\n    - attack.g0006\n    - attack.g0010", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Remote System Discovery", "mitreId": "T1018", "url": "https://attack.mitre.org/techniques/T1018/", "type": "technique"}]}, {"ruleName": "User Account Logon Policy Information Gathering", "ruleId": "8411", "description": "Detects the attempt to gather user account logon policy information such as password expiration period via \"net account\" command. This technique is commonly utilized for discovery as OilRig Threat Group's usage in its threat campaigns.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: User Account Logon Policy Information Gathering\nstatus: experimental\ndescription: Detects the attempt to gather user account logon policy information such as password expiration period via \"net account\" command. This technique is commonly utilized for discovery as OilRig Threat Group's usage in its threat campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1201/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0049/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: \n            - '*\\net.exe'\n            - '*\\net1.exe'\n        ProcessCommandLine: \n            - '* accounts'\n            - '* accounts /do*'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1201\n    - attack.ta0007\n    - attack.g0049", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Password Policy Discovery", "mitreId": "T1201", "url": "https://attack.mitre.org/techniques/T1201/", "type": "technique"}]}, {"ruleName": "Network Connections Discovery via Netstat Command", "ruleId": "7318", "description": "Detects the attempt to display active TCP connections via netstat command. This technique is commonly utilized for execution and discovery as Turla, menuPass and APT1 Threat Group's usage in its threat campaigns.", "releaseDate": 1600732800000, "updateDate": 1600732800000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Network Connections Discovery via Netstat Command\nstatus: experimental\ndescription: Detects the attempt to display active TCP connections via netstat command. This technique is commonly utilized for execution and discovery as Turla, menuPass and APT1 Threat Group's usage in its threat campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1049/\n   - https://attack.mitre.org/techniques/T1059/\n   - https://attack.mitre.org/tactics/TA0002/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0010/\n   - https://attack.mitre.org/groups/G0045/\n   - https://attack.mitre.org/groups/G0006/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/netstat\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\netstat.exe'\n        ProcessCommandLine: '*netstat*'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.execution\n    - attack.t1049\n    - attack.t1059\n    - attack.ta0002\n    - attack.ta0007\n    - attack.g0010\n    - attack.g0045\n    - attack.g0006", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Network Connections Discovery", "mitreId": "T1049", "url": "https://attack.mitre.org/techniques/T1049/", "type": "technique"}, {"name": "Command and Scripting Interpreter", "mitreId": "T1059", "url": "https://attack.mitre.org/techniques/T1059/", "type": "technique"}]}, {"ruleName": "Information Collection about Environment Variables via Set Command", "ruleId": "5426", "description": "Detects the attempt to gather information about the cmd.exe environment variables via set command. This technique is commonly utilized for discovery.", "releaseDate": 1574121600000, "updateDate": 1623715200000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Information Collection about Environment Variables via Set Command\nstatus: experimental\ndescription: Detects the attempt to gather information about the cmd.exe environment variables via set command. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1082/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/set_1\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\cmd.exe'\n        ProcessCommandLine: '* set'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1082\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "System Information Discovery", "mitreId": "T1082", "url": "https://attack.mitre.org/techniques/T1082/", "type": "technique"}]}, {"ruleName": "Domain Controllers Discovery via Net Tool", "ruleId": "5378", "description": "Detects the attempt to gather names of domain controllers via net tool. This technique is commonly utilized for discovery as APT1 Threat Group's usage in its threat campaigns.", "releaseDate": 1574121600000, "updateDate": *************, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Domain Controllers Discovery via Net Tool\nstatus: experimental\ndescription: Detects the attempt to gather names of domain controllers via net tool. This technique is commonly utilized for discovery as APT1 Threat Group's usage in its threat campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1087/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0006/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: \n            - '*\\net.exe'\n            - '*\\net1.exe'\n        ProcessCommandLine: \n            - '* group \"domain controllers\"'\n            - '* group \"domain controllers\" /do*'\n            - '* group /domain \"domain controllers\"'\n            - '* group /do \"domain controllers\"'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.t1087\n    - attack.ta0007\n    - attack.g0006", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}]}, {"ruleName": "Information Collection about User, Group and Privileges for the Current User on the Local System", "ruleId": "4142", "description": "Detects the attempt to display the user, group and privileges information for the current user via “whoami\" command. This technique is commonly utilized for discovery.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "medium", "reportScore": 0, "query": "title: Information Collection about User, Group and Privileges for the Current User on the Local System\nstatus: stable\ndescription: Detects the attempt to display the user, group and privileges information for the current user via “whoami\" command. This technique is commonly utilized for discovery.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1033/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0050/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: '*\\whoami.exe'\n    condition: selection\nfalsepositives:\n    - Usual user activity\nlevel: medium\ntags:\n    - attack.discovery\n    - attack.t1033\n    - attack.ta0007\n    - attack.g0050", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Usual user activity"], "mitre": [{"name": "System Owner/User Discovery", "mitreId": "T1033", "url": "https://attack.mitre.org/techniques/T1033/", "type": "technique"}]}, {"ruleName": "Network Shares on Local System Discovery via Net Tool", "ruleId": "3921", "description": "Detects the attempt to obtain details of shared drives on local system via “share” command of net tool. Adversaries use this information to identify potential systems for lateral movement.", "releaseDate": 1573603200000, "updateDate": 1619481600000, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Network Shares on Local System Discovery via Net Tool\nstatus: experimental\ndescription: Detects the attempt to obtain details of shared drives on local system via “share” command of net tool. Adversaries use this information to identify potential systems for lateral movement.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1135/\n   - https://attack.mitre.org/tactics/TA0007/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: \n            - '*\\net.exe'\n            - '*\\net1.exe'\n        ProcessCommandLine: '*share*'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1135\n    - attack.ta0007", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Network Share Discovery", "mitreId": "T1135", "url": "https://attack.mitre.org/techniques/T1135/", "type": "technique"}]}, {"ruleName": "Accounts in the Domain and Enterprise Admins Permission Group Discovery via Net Tool", "ruleId": "3050", "description": "Detects the attempt to obtain accounts in the Domain and Enterprise Admins permissions group via “net group” command. This technique is commonly utilized for discovery as APT1 threat group's usage in its campaigns.", "releaseDate": *************, "updateDate": *************, "author": "Picus Security", "severity": "low", "reportScore": 0, "query": "title: Accounts in the Domain and Enterprise Admins Permission Group Discovery via Net Tool\nstatus: experimental\ndescription: Detects the attempt to obtain accounts in the Domain and Enterprise Admins permissions group via “net group” command. This technique is commonly utilized for discovery as APT1 threat group's usage in its campaigns.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/techniques/T1087/\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/groups/G0006/\nlogsource:\n    product: windows\n    service: security\n    definition1: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\n    definition2: 'Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName: \n            - '*\\net.exe'\n            - '*\\net1.exe'\n        ProcessCommandLine:\n            - '* group \"domain admins\" /do*'\n            - '* group \"enterprise admins\" /do*'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.t1087\n    - attack.ta0007\n    - attack.g0006", "logSource": {"productName": "windows", "service": "security", "policies": ["Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation", "Requirements: Group Policy : Computer Configuration\\ Administrative Templates\\ System\\ Audit Process Creation\\ Include Command Line"]}, "falsePositives": ["Legitimate administrative activities"], "mitre": [{"name": "Account Discovery", "mitreId": "T1087", "url": "https://attack.mitre.org/techniques/T1087/", "type": "technique"}]}]}}