{"actions": {"7ef8612a-5d81-4709-b413-333dd0e96aec": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 4, "uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "ver": 16, "vid": "370367", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019 验证机器人上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过伪装的 LNK 文件执行二进制文件", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图通过名为 slowtempest.docx.lnk 的伪装 LNK 文件执行二进制文件。", "created_at": "2025-07-21 21:41:14", "updated_at": "2025-07-21 21:41:14", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1017", "tag_en": "M1017", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1021", "tag_en": "M1021", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1031", "tag_en": "M1031", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1049", "tag_en": "M1049", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_mitigation", "tag": "M1054", "tag_en": "M1054", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_tactics", "tag": "TA0001 - 初始访问", "tag_en": "TA0001 - Initial Access", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "mitre_techniques", "tag": "T1566.001 - 网络钓鱼附件", "tag_en": "T1566.001 - <PERSON><PERSON><PERSON><PERSON> Attachment", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "7ef8612a-5d81-4709-b413-333dd0e96aec", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["os Windows:Server 2016", "mitre_mitigation ATT&CK:M1017", "mitre_mitigation ATT&CK:M1018", "mitre_mitigation ATT&CK:M1021", "os Windows:Server 2022", "mitre_techniques T1566.001 - 网络钓鱼附件", "control EDR/XDR", "control HIDS", "os Windows:10", "os Windows:Server 2019", "mitre_tactics TA0001 - 初始访问", "mitre_mitigation ATT&CK:M1049", "mitre_mitigation ATT&CK:M1047", "mitre_mitigation ATT&CK:M1031", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "os Windows:11", "mitre_mitigation ATT&CK:M1054", "run_as Admin", "run_as User", "control AV"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "slowtempest.docx.lnk", "file_name": "slowtempest.docx.lnk", "file_owner": "system", "file_transfer_library": 999057713, "file_type": "lnk", "filesize": 920, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "62c1a8b101a1ea6628e8a0beb21c2a6c", "sha1sum": "89c550d1d6991a6f4244294e2b5aa121883fc31f", "sha256sum": "d9924fceb7911900bce48617d675e10b3561d3afd3a7556c8156085308d1834e"}], "monitor_connections": "off", "raw_text": "whoami\r\nexplorer \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\slowtempest.docx.lnk\"\r\ntasklist /svc | findstr /i winver.exe\r\ntaskkill.exe /f /im winver.exe\r\nrd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "explorer \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\slowtempest.docx.lnk\"", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i winver.exe", "prompt": "auto", "sleep": 1, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im winver.exe", "prompt": "auto", "sleep": 0, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 0, "step_order": 5, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}