{"action": {"name": "(自定义规则)主机命令行 - Execute a Command by using the PsExec Variant-1 - 9", "desc": "In this action, an attacker is trying to execute a command on a machine by using the PsExec.[black_basta_ransomware_chat_log_campaign]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 4, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [{"destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "PsExec64.exe", "file_owner": "system", "file_transfer_library": 998005656, "randomize_file": null, "file_name": "PsExec64.exe"}], "steps": [{"step_order": 1, "command": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\PsExec64.exe -accepteula \\\\127.0.0.1 -i winver.exe", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "{predefined-process-list} winver.exe", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}}, "tags": [{"tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "tag_type": "os", "type": 1}, {"tag": "Windows:11", "tag_en": "Windows:11", "tag_type": "os", "type": 1}, {"tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "tag_type": "os", "type": 1}, {"tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "tag_type": "os", "type": 1}, {"tag": "Windows:10", "tag_en": "Windows:10", "tag_type": "os", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"tag_type": "mitre_tactics", "tag": "TA0008 - 横向移动", "tag_en": "TA0008 - Lateral Movement", "type": 1}, {"tag_type": "mitre_techniques", "tag": "T1021.002 - SMB/Windows管理共享", "tag_en": "T1021.002 - SMB/Windows Admin Shares", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1027", "tag_en": "ATT&CK:M1027", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1035", "tag_en": "ATT&CK:M1035", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1026", "tag_en": "ATT&CK:M1026", "type": 1}, {"uuid": "", "tag_type": "mitre_mitigation", "tag": "ATT&CK:M1037", "tag_en": "ATT&CK:M1037", "type": 1}], "picus_id": 23796, "sigma": "", "uuid": "6d2d5215-a104-4f6d-b78a-94354f922c82"}