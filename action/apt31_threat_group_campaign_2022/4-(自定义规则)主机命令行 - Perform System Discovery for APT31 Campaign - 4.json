{"action": {"name": "(自定义规则)主机命令行 - Perform System Discovery for APT31 Campaign - 4", "desc": "In this action, an attacker is trying to mimic the discovery steps of the APT31 threat group.[apt31_threat_group_campaign_2022]", "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "action_type": "host_cli", "common_detection_alerts": [], "is_apt": false, "metadata_version": 31, "timeout_ms": 1000, "ver": 16, "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "threat": 3, "host_cli_action": {"shell": "cmd.exe", "raw_text": "", "delivery_wait_time": 5, "delivery_failed_result": "blocked", "destination_exists_result": "overwrite", "separate_cleanup_shell": false, "automatic_file_cleanup": false, "monitor_connections": "off", "user_variables": [], "host_cli_action_file_transfer_libraries": [], "steps": [{"step_order": 1, "command": "tasklist.exe /svc", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 2, "command": "netstat -nao", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 3, "command": "reg.exe query \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Nls\\Language\"", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "match", "success_match": "REG_SZ", "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 4, "command": "cmd.exe /c ipconfig.exe /all", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 5, "command": "cmd.exe /c net.exe user", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": false, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}, {"step_order": 6, "command": "dir", "prompt": "auto", "error_check": null, "error_match": null, "blocked_check": null, "blocked_match": null, "success_check": "zero", "success_match": null, "check_events": true, "event_logs": null, "sleep": 4, "cleanup": true, "blocked_order": null, "error_order": null, "success_order": 1, "timeout": 60}]}, "tags": {"control": ["Control:Endpoint"], "nist_control": [], "run_as": ["RunAs:<PERSON><PERSON>"], "src_destination": ["Src:External:Untrusted+Dst:Internal:Trusted"], "user": [], "user_control": [], "user_mitre_mitigation": [], "user_nist_control": [], "user_os": [], "user_run_as": [], "user_src_destination": [], "os": ["OS:Windows:11", "OS:Windows Server:2019", "OS:Windows Server:2016", "OS:Windows:10"], "verodin": ["ATT&CK:Discovery", "ATT&CK:T1082"], "mitre_mitigation": []}}, "picus_id": 23272, "uuid": "4abc222b-4841-4ed5-aeb0-6476378c57b3", "sigma_device_query": "title: System Information Discovery via Native Tools\nstatus: experimental\ndescription: Detects the execution of common tools that are used for getting details about the settings of systems. Adversaries use these tools within close timeframe on discovery phase.\nauthor: Picus Security\nreferences:\n   - https://attack.mitre.org/tactics/TA0007/\n   - https://attack.mitre.org/techniques/T1016/\n   - https://attack.mitre.org/techniques/T1033/\n   - https://attack.mitre.org/techniques/T1049/\n   - https://attack.mitre.org/techniques/T1087/\n   - https://attack.mitre.org/techniques/T1082/\n   - https://attack.mitre.org/software/S0099/\n   - https://attack.mitre.org/software/S0100/\n   - https://attack.mitre.org/software/S0103/\n   - https://attack.mitre.org/software/S0039/\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/arp\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/whoami\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/ipconfig\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/route_ws2008\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/netstat\n   - https://learn.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2012-r2-and-2012/cc725622(v=ws.11)\n   - https://learn.microsoft.com/en-us/windows-server/administration/windows-commands/getmac\nlogsource:\n    product: windows\n    service: security\n    definition: 'Requirements: Group Policy : Computer Configuration\\Windows Settings\\Security Settings\\Advanced Audit Policy Configuration\\Audit Policies\\Detailed Tracking\\Audit Process Creation'\ndetection:\n    selection:\n        EventID: 4688\n        NewProcessName|endswith: \n                - '\\whoami.exe'\n                - '\\arp.exe'\n                - '\\ipconfig.exe'\n                - '\\route.exe'\n                - '\\netstat.exe'\n                - '\\net.exe' \n                - '\\getmac.exe'\n                - '\\nbtstat.exe'\n    condition: selection\nfalsepositives:\n    - Legitimate administrative activities\nlevel: low\ntags:\n    - attack.discovery\n    - attack.ta0007\n    - attack.t1016\n    - attack.t1033\n    - attack.t1049\n    - attack.t1087\n    - attack.t1082"}