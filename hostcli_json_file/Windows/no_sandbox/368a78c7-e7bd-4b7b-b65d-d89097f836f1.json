{"actions": {"368a78c7-e7bd-4b7b-b65d-d89097f836f1": {"newStyle": false, "enable": true, "action_type": "host_cli", "os": "windows", "must_sandbox": false, "is_endpoint": true, "severity": 3, "uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "ver": 16, "vid": "377833", "timeout_ms": 1000, "notes": "塞讯验证建议在 Windows 10、Windows 11、Windows Server 2016、Windows Server 2019验证机器人 上以系统、非管理员、管理员用户身份运行此验证动作。", "name": "主机命令行 - 使用反射 DLL 执行键盘记录器", "action_mitigation": "如果终端/主机安全产品对此攻击漏检，塞讯验证短期建议：加强在系统内安装、卸载、执行软件等权限控制；监测可疑命令执行。中长期建议：加强应用程序的代码签名检测；如果尚未部署EDR，则建议使用EDR类的安全产品对其行为进行检测与防御；加强对终端的日志审计在SIEM系统中对此攻击时的行为进行监测与告警。", "metadata_version": 31, "desc": "此验证动作还原了攻击者试图通过注入带有反射加载器存根的 shellcode 来执行使用 RegisterRawInputDevices API 调用的键盘记录器。", "created_at": "0001-01-01 08:05:43", "updated_at": "2025-07-21 21:41:37", "is_apt": false, "is_self": false, "is_net_host_cli": false, "is_delete": false, "is_vulnhub": false, "common_detection_alerts": [], "tags": [{"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "control", "tag": "AV", "tag_en": "AV", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "control", "tag": "EDR/XDR", "tag_en": "EDR/XDR", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "control", "tag": "HIDS", "tag_en": "HIDS", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "mitre_tactics", "tag": "TA0006 - 凭据访问", "tag_en": "TA0006 - Credential Access", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "mitre_tactics", "tag": "TA0009 - 信息收集", "tag_en": "TA0009 - Collection", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "mitre_techniques", "tag": "T1056 - 输入捕获", "tag_en": "T1056 - Input Capture", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "os", "tag": "Windows:10", "tag_en": "Windows:10", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "os", "tag": "Windows:11", "tag_en": "Windows:11", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "os", "tag": "Windows:Server 2016", "tag_en": "Windows:Server 2016", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "os", "tag": "Windows:Server 2019", "tag_en": "Windows:Server 2019", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "os", "tag": "Windows:Server 2022", "tag_en": "Windows:Server 2022", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "run_as", "tag": "Admin", "tag_en": "Admin", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "run_as", "tag": "SYSTEM", "tag_en": "SYSTEM", "type": 1}, {"uuid": "368a78c7-e7bd-4b7b-b65d-d89097f836f1", "tag_type": "src_destination", "tag": "Src:Internal:Trusted+Dst:Internal:Trusted", "tag_en": "Src:Internal:Trusted+Dst:Internal:Trusted", "type": 1}], "old_tags": {"control": null, "mitre_mitigation": null, "nist_control": null, "os": null, "run_as": null, "src_destination": null, "user": null, "user_control": null, "user_mitre_mitigation": null, "user_nist_control": null, "user_os": null, "user_run_as": null, "user_src_destination": null, "verodin": ["control EDR/XDR", "os Windows:Server 2019", "os Windows:Server 2016", "control AV", "run_as Admin", "src_destination Src:Internal:Trusted+Dst:Internal:Trusted", "mitre_techniques T1056 - 输入捕获", "mitre_tactics TA0006 - 凭据访问", "os Windows:Server 2022", "os Windows:10", "control HIDS", "mitre_tactics TA0009 - 信息收集", "os Windows:11", "run_as SYSTEM"], "catetory": null, "affected_software": null, "app": null, "target": null, "cve": null}, "trees": ["Attack Vector>General-Vector", "Attacker Location>External", "Behavior Type>General-Behavior", "OS/Platform>Windows", "Stage of Attack>Action on Target"], "host_cli_action": {"delivery_failed_result": "blocked", "delivery_wait_time": 5, "destination_exists_result": "overwrite", "host_cli_action_file_transfer_libraries": [{"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "srdinject.exe", "file_name": "srdinject.exe", "file_owner": "system", "file_transfer_library": 999053890, "file_type": "exe", "filesize": 137216, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "95aedef07fe5651bf572aefbb80b77bf", "sha1sum": "283ca2af618ee5cb3bbf43698d650cff07014384", "sha256sum": "109041cf47217a5920ea45283e58b2e46525caa5e9042ecbc770301c2819646d"}, {"check_date": "0001-01-01 00:00:00", "destination_directory": "C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "destination_name": "srdikeylog.bin", "file_name": "srdikeylog.bin", "file_owner": "system", "file_transfer_library": 999053891, "file_type": "bin", "filesize": 218924, "first_submission_date": "0001-01-01 00:00:00", "last_submission_date": "0001-01-01 00:00:00", "md5sum": "38270c9a8b1e46b86ce2f88b22942149", "sha1sum": "0b598f17cfda582d93c7dc548e9e20f7559c3c29", "sha256sum": "0f3630d8372b42c836414ae815e44436a3e545ce12a856910743d4f214dcea84"}], "monitor_connections": "off", "raw_text": "start /b C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\srdinject.exe \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\srdikeylog.bin\"\r\ntasklist /svc | findstr /i srdinject\r\ntaskkill.exe /f /im srdinject.exe\r\ndel C:\\Windows\\Temp\\keylog.txt & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}\r\n", "shell": "cmd.exe", "steps": [{"check_events": true, "command": "start /b C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\srdinject.exe \"C:\\Users\\<USER>\\Documents\\{{v_action_id}}\\srdikeylog.bin\"", "prompt": "auto", "sleep": 60, "step_order": 1, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "command": "tasklist /svc | findstr /i srdinject", "prompt": "auto", "sleep": 4, "step_order": 2, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "taskkill.exe /f /im srdinject.exe", "prompt": "auto", "sleep": 4, "step_order": 3, "success_check": "zero", "success_order": 1, "timeout": 60}, {"check_events": true, "cleanup": true, "command": "del C:\\Windows\\Temp\\keylog.txt & rd /s /q C:\\Users\\<USER>\\Documents\\{{v_action_id}}", "prompt": "auto", "sleep": 4, "step_order": 4, "success_check": "zero", "success_order": 1, "timeout": 60}]}, "required_parms": [], "is_http": false, "args": {"custom_shell_path": ""}, "arch": null, "last_run": {"time": "", "error": false, "count": 0, "jobId": 0, "jobGroupId": 0, "jobGroupActionId": 0}, "selfActionTags": []}}}